# 160901(1)_1_45_translate_20250526171032 - 第 5 部分

页面 21 - 25

---

## 第 21 页

HumaFIA |用户手册
23
在上一屏幕中按下“ 添加测试到快速模式列表” 可关闭样本详细信息界面，并将样本从下往上逐一添加到工
作列表中。
要以快速模式运行样本，请按照以下步骤进行每个测试。
在快速模式工作列表的“ 计时器” 列中，会出现“ 下一个样本” 按钮。秒数显示了可以开
始下一个（或第一个）样本的时间。如果秒数达到00或显示80µL vol.已添加，则可以开始
该测试。
下一步是将80µL的样本加入到卡盒中。完成后，请立即按下此按钮。现在开始监测该测试
的孵育时间。注意：根据参数的不同，80µL的样本体积可以是血液/血清，也可以是血液/
血清的稀释液（参见参数IFU）。
对于本试验，现在剩余的孵育时间以mm：ss格式显示。当本试验的孵育时间快结束时，下
一个按钮出现。
现在，用户有10秒时间将试剂盒插入分析仪并按下“ 读取” 按钮。为了获得正确结果，当
计时器达到00时，按下“ 读取” 按钮。阅读仅需几秒钟，然后显示此参数的结果。
如果时间正确，--：- -出现，表明孵育时间正确，并且在可接受范围内。
如果出现红色数字，如+02：11，则孵育时间过长2分钟11秒，表明结果无效，可能需要重
复。
要运行一系列测试，可以启动上述工作流程以进行下一次测试。
一旦上一个测试的孵育时间开始，此按钮就会显示出来，以便在快速工作列表中准备下一
个测试。工作流程程序运行计时器，允许用户准备下一个测试。如果计时器显示达到00，
系统就准备好准备下一个测试盒了。
现在进行第二次测试，必须将80µL的样本移液到卡盒中。完成此操作后，立即按下此按
钮。现在开始监测工作列表中第二次测试的孵育时间。
将对上述第一项测试的系列进行处理，以进行第二项测试和后续测试，直至快速工作列表完成。
当快速工作列表清空后，每行将提供以下样本信息：患者的姓名、ID、参数类型（括号内为单位）、结果、
测试日期和时间，以及使用的样本类型（B=全血，S=血清，P=血浆）。此外，还将提供该参数的批次（LOT）
和截止值。


---

## 第 22 页

HumaFIA |用户手册
24
显示为参考。分析仪仅显示结果，而不显示线性标志，因为这些标志表明测试无效，必须重复。如果计时器
列中出现红色数字，则孵育时间不正确。
可在设置中启用以下声学警报：
声报警
功能
2个短蜂鸣声
可选择下一个样本
长蜂鸣声
孵育时间快结束，距离孵育时间结束还有10秒
非常长的蜂鸣声
孵育时间结束
短蜂鸣
完成对墨盒的读取，显示结果
表3-1声音报警设置，可在设置菜单中启用和修改
注：三合一检测（如cTnI/肌钙蛋白/CK-MB）以三行显示。每个参数在屏幕上分别显示一行。参数名称以两个点开
头和结尾（例如，.. cTnI..，或..肌钙蛋白. .)。
3.2.4 添加样本、患者数据和参数类型
在三种样本运行模式中，均采用相同程序添加患者ID、选择参数和样本类型或预稀释。此外，还可以向样本
添加其他患者数据，如姓氏、名字、生日、性别及备注。
下一个屏幕允许用户在Auto sample、STAT sample和Fast模式下将患者样本添加到工作列表中，屏幕始终相
同，只是关闭屏幕的按钮不同，取决于样本模式。见下图3-4：
图3-4向Auto-sample、STAT-sample或Fast-mode过程添加新样本以运行测试


---

## 第 23 页

HumaFIA |用户手册
25
3.2.4. 1在3种测试模式中确认患者数据
根据样本模式，按下以下按钮之一关闭上一个屏幕：
自动采样
STAT样本
快速模式
3.2.4.2添加ID
要添加新的患者样本，请按“ 添加ID” 以分配一个ID编号。然后将自动生成一个新的ID编号。
3.2.4.3 选择参数
使用深灰色按钮选择参数类型。由于之前已上传有效的校准曲线，可
用参数以白色字体显示。如果按钮为红色，则先上传校准曲线。如果
所需参数不在列表中，请按其他。当正确参数显示在浅灰色字段中
时，按退出将所选参数转移到上一个屏幕。
选择样本类型，白色显示的类型可用于所选参数类型。
如果需要对样本进行稀释以获得线性范围内的结果，则将预稀释功
能（Pre-Dil.）设置为“ 是” 。检查参数的IFU值，以使用经验证的
稀释因子。
3.2.4.4 可自动计算预稀释样品的选项
如果预期值非常高，系统可选择自动计算预稀释样品。如果选择“ 是” 作为“ 预稀释” （参见上图），则在
处理结果时会自动考虑预稀释因子。
按下“ 插入培养盒” 按钮，将在自动样品模式下关闭此屏幕并启动软件计时
器。
将患者样本加入到试剂盒中，并在按下此按钮之前将试剂盒插入分析仪。
按下“ 读取” 孵育盒按钮，将关闭STAT样本模式下的此屏幕并立即开始测试
读数。
请确保：将患者样本添加到试剂盒中，并在按下此按钮之前将试剂盒插入分
析仪。
按下“ 添加测试到快速模式列表” 按钮，将关闭快速模式下的此屏幕并填充
快速工作列表。新测试/患者将依次添加到工作列表中，从按钮到顶部。
可在下一个屏幕上启动孵育和读取。


---

## 第 24 页

HumaFIA |用户手册
26
注意：只有当预稀释与软件显示的稀释率相匹配时，结果才是正确的。为了进行预稀释，根据IFU，用5 %
BSA生理盐水溶液稀释样品。
3.2.4.5 添加患者数据
有两种方式可以添加患者数据：
A) 通过键盘手动
B) 通过可选条形码阅读器
成功添加患者数据后，姓、名、出生日期和性别显示在浅灰色字段中。
A) 键盘
如果选择，将显示以下屏幕。
图3-5软件键盘选择名为“ 姓氏” 的浅灰色字段，用键盘添加数据。
姓名和生日也是如此。性别可以在性别下方的下拉菜单中选择。按Enter键关闭屏幕，将所有输入的数据转移
到上一个屏幕。
B) 可选条形码
患者数据可以通过条形码阅读器上传。
3.3 质量控制模块
根据药物非临床研究质量管理规范（GLP），必须每天使用对照品，以确保系统和试剂正常工作。
QC模块允许上载控制材料的目标值、测量QC样品和操作服务功能工具，标准试剂盒（参见第4.9章“ 服务和维
护” ）。
必须始终提前上传目标值，否则无法通过卡匣运行控制材料。请参见第3.3.1章“ 上传新的控制材料” 。
关于制备对照品的更多信息，例如复溶或向测试盒中移液对照体积，请遵循对照品和相应测试参数的使用说
明。对照品的使用方式与相同参数类型的样品相同。
要运行每日控制，有两种模式可用。


---

## 第 25 页

HumaFIA |用户手册
27
1.
自动模式下的运行控制：如果只需要检查少量的对照材料，且在孵育和读取期间无需人工操作，则
推荐使用此选项。
2.
快速模式下运行控制：如果需要在短时间内运行不同级别和参数的控制材料，建议使用此选项。
图3-6质量控制主界面
上传的控制的批号、目标值（范围）和参数显示在一行中。请注意，每个控制级别显示在单独的一行中。
“ 结果” 列显示最后记录的值，日期和时间信息在“ 日期时间” 列中可用。
如果结果与目标值匹配，则列“ 范围” 中的标志显示P，表示通过。如果列“ 范围” 中的标志为红色，并显示
CH或CL，则表示控制结果过高（CH）或过低（CL）。
要删除已过期的控件或其他原因，请按要删除的控件前面的深灰色按钮。
如果当前有一个控制装置处于自动模式，并且留在分析仪中进行孵育和读取，则会显示沙漏。沙漏旁边的数
字表示剩余的孵育时间（以毫米为单位）：ss。
控制读数的最新结果显示在最后一行。
注：要删除控制结果，请按下每个列出的控制结果上的深灰色按钮。
3.3.1 上传新的控制材料
控制套件包含目标值表，提供参数必须匹配的范围和批号。


---


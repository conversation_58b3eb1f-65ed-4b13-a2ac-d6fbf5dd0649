# TRF Development Guidelines

## 🌐 Language Policy

### English-Only Development Rule
**All code, comments, logs, and documentation must be written in English.**

#### Reasons:
1. **Embedded System Compatibility** - Avoids font library dependencies
2. **International Collaboration** - Enables global developer participation  
3. **Deployment Simplicity** - Reduces system requirements on target hardware
4. **Maintenance Efficiency** - Easier debugging and troubleshooting

#### Specific Requirements:
- ✅ **Code Comments** - English only
- ✅ **Debug Logs** - English only (`qDebug()`, logging output)
- ✅ **Variable/Function Names** - English only
- ✅ **UI Text** - English only  
- ✅ **Error Messages** - English only
- ✅ **Documentation** - English only

#### Examples:

**❌ Bad (Chinese):**
```cpp
qDebug() << "用户登录成功";
QString 用户名 = "admin";
// 设置定时器
timer->start(1000);
```

**✅ Good (English):**
```cpp
qDebug() << "User login successful";
QString username = "admin";
// Set up timer
timer->start(1000);
```

## 🎯 Target System Optimization

### myd-y6ull14x14 Embedded System
- **Architecture**: ARMv7l Linux 4.1.15+
- **Qt Version**: 5.5.1+ compatibility required (build env: 5.5.1, target: 5.6.2)
- **Font Policy**: No external font dependencies
- **Resource Constraints**: Optimized for embedded environment
- **Build Standard**: C++11 (`-std=c++0x`)

### Font Configuration Rules:
- ✅ Use Qt built-in fonts only
- ✅ Prefer system fonts: Liberation Sans, DejaVu Sans, Noto Sans
- ❌ No Chinese font files required
- ❌ No QT_QPA_FONTDIR environment variables

## 🛠️ Development Standards

### Code Quality:
- Use English for all developer-facing content
- Optimize for embedded system constraints
- Maintain Qt 5.5.1+ compatibility (see [Qt 5.5.1 Compatibility Guide](Qt_5.5_Compatibility_Guide.md))
- Follow cross-compilation best practices
- Use traditional Qt patterns over modern C++ features
- Always add version checks for Qt 5.6+ APIs

### Testing:
- Test on target ARMv7l architecture when possible
- Verify font-independent operation
- Validate English-only user experience

---
**Last Updated**: 2025-01-18
**Applies to**: All TRF project development 
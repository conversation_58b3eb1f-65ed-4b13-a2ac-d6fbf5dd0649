#include "Patient.h"
#include <QStringList>
#include <QRegularExpression>

Patient::Patient()
    : m_patientId(0)
    , m_birthday(QDate::currentDate())
    , m_gender("Male")
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
}

Patient::Patient(const QString& lastName, const QString& firstName, 
                 const QDate& birthday, const QString& gender)
    : m_patientId(0)
    , m_lastName(lastName)
    , m_firstName(firstName)
    , m_birthday(birthday)
    , m_gender(gender)
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
    updatePatientName();
}

Patient::Patient(int patientId, const QString& patientName, const QString& lastName, 
                 const QString& firstName, const QDate& birthday, const QString& gender, 
                 const QString& remarks)
    : m_patientId(patientId)
    , m_patientName(patientName)
    , m_lastName(lastName)
    , m_firstName(firstName)
    , m_birthday(birthday)
    , m_gender(gender)
    , m_remarks(remarks)
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
    if (m_patientName.isEmpty()) {
        updatePatientName();
    }
}

bool Patient::isValid() const
{
    // 检查必填字段
    if (m_lastName.trimmed().isEmpty()) {
        return false;
    }
    
    if (m_firstName.trimmed().isEmpty()) {
        return false;
    }
    
    // 验证生日
    if (!m_birthday.isValid() || m_birthday > QDate::currentDate()) {
        return false;
    }
    
    // 验证性别
    if (!isValidGender(m_gender)) {
        return false;
    }
    
    // 验证年龄范围（0-150岁）
    int age = getAge();
    if (age < 0 || age > 150) {
        return false;
    }
    
    return true;
}

QString Patient::getValidationError() const
{
    QStringList errors;
    
    if (m_lastName.trimmed().isEmpty()) {
        errors << "姓氏不能为空";
    }
    
    if (m_firstName.trimmed().isEmpty()) {
        errors << "名字不能为空";
    }
    
    if (!m_birthday.isValid()) {
        errors << "生日格式无效";
    } else if (m_birthday > QDate::currentDate()) {
        errors << "生日不能晚于当前日期";
    }
    
    if (!isValidGender(m_gender)) {
        errors << "性别必须为 Male 或 Female";
    }
    
    int age = getAge();
    if (age < 0 || age > 150) {
        errors << "年龄必须在 0-150 岁之间";
    }
    
    return errors.join("; ");
}

int Patient::getAge() const
{
    if (!m_birthday.isValid()) {
        return -1;
    }
    
    QDate currentDate = QDate::currentDate();
    int age = currentDate.year() - m_birthday.year();
    
    // 如果还没到生日，年龄减1
    if (currentDate.month() < m_birthday.month() || 
        (currentDate.month() == m_birthday.month() && currentDate.day() < m_birthday.day())) {
        age--;
    }
    
    return age;
}

QString Patient::getFormattedName() const
{
    if (!m_lastName.isEmpty() && !m_firstName.isEmpty()) {
        return QString("%1, %2").arg(m_lastName.trimmed(), m_firstName.trimmed());
    } else if (!m_patientName.isEmpty()) {
        return m_patientName;
    } else if (!m_lastName.isEmpty()) {
        return m_lastName;
    } else if (!m_firstName.isEmpty()) {
        return m_firstName;
    }
    // 返回空字符串而不是"Unknown Patient"
    return QString("");
}

bool Patient::operator==(const Patient& other) const
{
    return m_patientId == other.m_patientId &&
           m_lastName == other.m_lastName &&
           m_firstName == other.m_firstName &&
           m_birthday == other.m_birthday &&
           m_gender == other.m_gender;
}

bool Patient::operator!=(const Patient& other) const
{
    return !(*this == other);
}

QString Patient::toString() const
{
    return QString("Patient[ID=%1, Name='%2', Birthday='%3', Gender='%4', Age=%5]")
           .arg(m_patientId)
           .arg(getFormattedName())
           .arg(m_birthday.toString("yyyy-MM-dd"))
           .arg(m_gender)
           .arg(getAge());
}

void Patient::updatePatientName()
{
    if (!m_lastName.isEmpty() || !m_firstName.isEmpty()) {
        m_patientName = getFormattedName();
    }
}

bool Patient::isValidGender(const QString& gender) const
{
    return gender == "Male" || gender == "Female";
}

QDebug operator<<(QDebug debug, const Patient& patient)
{
    debug.nospace() << patient.toString();
    return debug;
} 
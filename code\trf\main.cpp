#include "mainwindow.h"
#include "loginscreen.h"
#include "database/storage/FileStorage.h"
#include <QApplication>
#include <QFile>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QTextStream>
#include <QDateTime>
#include <QMessageBox>
#include <QLibraryInfo>
#include <QStyleFactory>
#include <QFontDatabase>
// Conditional include QTextCodec - only for Qt versions before 6.0
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
#include <QTextCodec>
#endif
#include <QFileInfo>
#include <QtGlobal>
#include <QThread>
#include <cstdio>
#include <exception>

// 全局错误日志文件
static QFile *g_logFile = nullptr;
static QTextStream *g_logStream = nullptr;

// 内存监控函数
void logMemoryUsage(const QString& stage) {
    Q_UNUSED(stage); // 避免编译警告
#ifdef TARGET_SYSTEM_MYD_Y6ULL14X14
    QFile meminfo("/proc/meminfo");
    if (meminfo.open(QIODevice::ReadOnly)) {
        QTextStream stream(&meminfo);
        QString line;
        int memTotal = 0, memAvailable = 0;

        while (!stream.atEnd()) {
            line = stream.readLine();
            if (line.startsWith("MemTotal:")) {
                memTotal = line.split(QRegExp("\\s+"))[1].toInt() / 1024; // Convert to MB
            } else if (line.startsWith("MemAvailable:")) {
                memAvailable = line.split(QRegExp("\\s+"))[1].toInt() / 1024; // Convert to MB
                break;
            }
        }

        qDebug() << QString("[%1] Memory: %2MB total, %3MB available").arg(stage).arg(memTotal).arg(memAvailable);

        if (memAvailable < 50) {
            qWarning() << QString("[%1] WARNING: Low memory! Only %2MB available").arg(stage).arg(memAvailable);
        }
    }
#endif
}

// Qt版本兼容性检查和配置
void setupQtCompatibility() {
    // 内存优化设置 - Qt 5.6.2兼容版本
#if QT_VERSION >= QT_VERSION_CHECK(5, 14, 0)
    QCoreApplication::setAttribute(Qt::AA_DisableShaderDiskCache, true);
#endif
    QCoreApplication::setAttribute(Qt::AA_ShareOpenGLContexts, false);

    // Set UTF-8 encoding support (compatibility handling)
#if QT_VERSION < 0x060000  // Before Qt 6.0.0
    // Only set Locale encoding, avoid using deprecated functions
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    // Note: setCodecForTr and setCodecForCStrings are deprecated in Qt 5.15+, so not used
#endif
    // Qt 6.0+ uses UTF-8 by default, no additional setup needed
    
#ifdef Q_OS_WIN
    // Windows platform optimization settings
    #if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
        // Qt 5.x version high DPI settings
        QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling, true);
        QCoreApplication::setAttribute(Qt::AA_UseHighDpiPixmaps, true);
    #endif
    
    #if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0) && QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
        // This attribute is only available in Qt 5.10+, removed in Qt 6.0+
        QCoreApplication::setAttribute(Qt::AA_DisableWindowContextHelpButton, true);
    #endif
    
    qDebug() << "Qt Version Compatibility Config:";
    qDebug() << "  Build Version:" << QT_VERSION_STR;
    qDebug() << "  Runtime Version:" << qVersion();
    qDebug() << "  UTF-8 encoding configured";
    qDebug() << "  Windows system optimization enabled";
#else
    // ARM embedded system optimized application properties
    #if QT_VERSION >= QT_VERSION_CHECK(5, 6, 0) && QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
        QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling, false);  // ARM systems typically don't need high DPI
        QCoreApplication::setAttribute(Qt::AA_UseHighDpiPixmaps, false);
    #endif
    
    #if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0) && QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
        QCoreApplication::setAttribute(Qt::AA_DisableWindowContextHelpButton, true);
    #endif
    
    // For Qt 5.5.x compatibility - these attributes don't exist, skip them
    qDebug() << "Qt version:" << QT_VERSION_STR << "- optimized for embedded ARM systems";
    
    // Target system myd-y6ull14x14 dedicated optimization configuration
    #ifdef TARGET_SYSTEM_MYD_Y6ULL14X14
        qDebug() << "Detected target system myd-y6ull14x14, enabling dedicated configuration...";
        
        // Specific compatibility settings for Qt 5.6.2
        // 明确指定framebuffer设备路径和显示参数
        qputenv("QT_QPA_PLATFORM", "linuxfb:fb=/dev/fb0");
        qputenv("QT_QPA_FB_DISABLE_INPUT", "0");
        
        // 强制全屏显示，确保界面可见
        qputenv("QT_QPA_FB_FORCE_FULLSCREEN", "1");
        qputenv("QT_QPA_FB_DRM", "1");

        // 额外的显示强制设置
        qputenv("QT_QPA_FB_HIDECURSOR", "1");  // 隐藏鼠标光标
        qputenv("QT_QPA_FB_DISABLE_INPUT", "0");  // 确保输入启用
        
        // 显示设备配置验证
        if (QFile::exists("/dev/fb0")) {
            qDebug() << "  ✓ Found framebuffer device: /dev/fb0";
        } else {
            qDebug() << "  ⚠ Framebuffer /dev/fb0 not found, checking alternatives...";
            if (QFile::exists("/dev/fb1")) {
                qputenv("QT_QPA_PLATFORM", "linuxfb:fb=/dev/fb1");
                qDebug() << "  ✓ Using alternative framebuffer: /dev/fb1";
            }
        }
        
        // Note: Removed QT_QPA_FONTDIR setting as we no longer need Chinese fonts
        
        // Touch input optimization for iMX6UL TouchScreen Controller
        QString touchDevice;
        if (QFile::exists("/dev/input/event1")) {
            touchDevice = "evdevtouch:/dev/input/event1";
            qDebug() << "  Using touch device: /dev/input/event1";
        } else if (QFile::exists("/dev/input/event0")) {
            touchDevice = "evdevtouch:/dev/input/event0";
            qDebug() << "  Using touch device: /dev/input/event0";
        }
        if (!touchDevice.isEmpty()) {
            qputenv("QT_QPA_GENERIC_PLUGINS", touchDevice.toLocal8Bit());
        }
        
        // EGL/OpenGL ES configuration optimization (System supports libEGL.so.1 and libGLESv2.so.2)
        qputenv("QT_QPA_EGLFS_INTEGRATION", "eglfs_viv");
        qputenv("QT_QPA_EGLFS_DISABLE_INPUT", "0");
        
        qDebug() << "  myd-y6ull14x14 dedicated configuration enabled";
        qDebug() << "  LinuxFB graphics output configured";
        qDebug() << "  Touch input configured";
        qDebug() << "  EGL/OpenGL ES support enabled";
        qDebug() << "  Font configuration: Using Qt built-in fonts (no external font dependency)";
    #endif
    
    // Set locale environment variables to support UTF-8
    qputenv("LC_ALL", "C.UTF-8");
    qputenv("LANG", "C.UTF-8");
    qputenv("LC_CTYPE", "C.UTF-8");
    
    // Disable some features that may be problematic on ARM systems
    qputenv("QT_QPA_EGLFS_DISABLE_INPUT", "0");
    qputenv("QT_LOGGING_RULES", "qt.qpa.fonts.warning=false;qt.widgets.gestures.debug=false");
    
    qDebug() << "Qt Version Compatibility Config:";
    qDebug() << "  Build Version:" << QT_VERSION_STR;
    qDebug() << "  Runtime Version:" << qVersion();
    qDebug() << "  UTF-8 encoding configured";
    qDebug() << "  ARM system optimization enabled";
    
    // Output target system matching information
    #ifdef TARGET_SYSTEM_MYD_Y6ULL14X14
        qDebug() << "  Target system: myd-y6ull14x14 (Linux 4.1.15+ armv7l Qt 5.6.2)";
        qDebug() << "  Precise matching mode activated";
    #endif
#endif
}

// Optimized font configuration (English-only, better visual effect)
void setupFontPaths() {
    qDebug() << "Setting up optimized font configuration...";
    
    // Disable font warnings to avoid dependency issues
    qputenv("QT_LOGGING_RULES", "qt.qpa.fonts.warning=false");
    
    // Check available system fonts and select best ones
    QFontDatabase fontDb;
    QStringList families = fontDb.families();
    qDebug() << "Available font families:" << families.size();
    
    if (families.size() > 0) {
        qDebug() << "Using optimized system fonts (English only)";
        
        // Try to find and set better fonts for UI consistency
        QString selectedFont = "";
        
        // Preferred font order for embedded systems
        QStringList preferredFonts = {
            "Liberation Sans",      // Good for embedded systems
            "DejaVu Sans",         // Clean appearance
            "Noto Sans",           // Universal font
            "Arial",               // Common system font
            "Helvetica",           // Alternative system font
            "Ubuntu",              // Clean modern font
            families.first()       // Fallback to first available
        };
        
        // Select the first available preferred font
        for (const QString& preferred : preferredFonts) {
            if (families.contains(preferred, Qt::CaseInsensitive)) {
                selectedFont = preferred;
                break;
            }
        }
        
        if (selectedFont.isEmpty()) {
            selectedFont = families.first();
        }
        
        // Set application-wide font with optimized settings
        QFont appFont(selectedFont);
        appFont.setPointSize(10);           // Reasonable size for embedded displays
        appFont.setWeight(QFont::Normal);   // Normal weight
        appFont.setStyleHint(QFont::SansSerif, QFont::PreferAntialias);
        QApplication::setFont(appFont);
        
        qDebug() << "Selected font:" << selectedFont << "size:" << appFont.pointSize();
    } else {
        qDebug() << "No font families found, using Qt built-in fonts";
        // Qt will fall back to built-in fonts automatically
        
        // Set a reasonable default font size for Qt built-in fonts
        QFont defaultFont;
        defaultFont.setPointSize(10);
        defaultFont.setWeight(QFont::Normal);
        QApplication::setFont(defaultFont);
    }
}

// 智能插件路径配置
void setupPluginPaths() {
    qDebug() << "Starting Qt plugin path configuration...";
    
    // Get current plugin paths
    QStringList currentPaths = QCoreApplication::libraryPaths();
    qDebug() << "  Current plugin paths:";
    for (const QString &path : currentPaths) {
        qDebug() << "    -" << path << (QDir(path).exists() ? "(exists)" : "(does not exist)");
    }
    
#ifdef Q_OS_WIN
    // Windows system plugin paths - Qt usually configures them automatically
    qDebug() << "  Windows system - using Qt's automatically configured plugin paths";
#else
    // Possible plugin paths for ARM embedded systems
    QStringList additionalPaths = {
        "/usr/lib/qt5/plugins",                    // Standard Qt5 plugin directory
        "/usr/lib/arm-linux-gnueabihf/qt5/plugins", // ARM specific plugin directory
        "/usr/local/lib/qt5/plugins",              // User installed Qt5 plugins
        "/opt/qt5/plugins",                        // Custom Qt5 installation
        "/usr/lib/qt/plugins",                     // Simplified path
        "/usr/lib/plugins",                        // Alternative plugin directory
        QCoreApplication::applicationDirPath(),    // Application directory
        QDir::currentPath()                        // Current working directory
    };
    
    for (const QString &path : additionalPaths) {
        if (QDir(path).exists() && !currentPaths.contains(path)) {
            QCoreApplication::addLibraryPath(path);
            qDebug() << "  ✓ Adding plugin path:" << path;
        }
    }
    
    // Configure input device plugins for ARM systems
    qDebug() << "  Configuring ARM system input devices...";
    
    // Check touch devices and configure corresponding input plugins
    QStringList touchDevices = {"/dev/input/event0", "/dev/input/event1", "/dev/input/event2"};
    QString activeTouchDevice = "";
    
    for (const QString &device : touchDevices) {
        if (QFile::exists(device)) {
            qDebug() << "  ✓ Found touch device:" << device;
            if (activeTouchDevice.isEmpty()) {
                activeTouchDevice = device;
            }
        }
    }
    
    // Set a generic input plugin suitable for ARM systems (does not require tslib)
    if (!activeTouchDevice.isEmpty()) {
        // Use evdev as the primary input scheme, avoiding tslib dependency
        QString inputPlugins = QString("evdevtouch:%1").arg(activeTouchDevice);
        qputenv("QT_QPA_GENERIC_PLUGINS", inputPlugins.toLocal8Bit());
        qDebug() << "  → Setting input plugins to:" << inputPlugins;
    } else {
        qDebug() << "  ⚠ No touch device found, using default input";
    }
#endif
    
    // Check key plugin directories
    QStringList updatedPaths = QCoreApplication::libraryPaths();
    for (const QString &basePath : updatedPaths) {
        QString platformsPath = basePath + "/platforms";
        QString sqldriversPath = basePath + "/sqldrivers";
        
        if (QDir(platformsPath).exists()) {
            qDebug() << "  ✓ Found platforms plugin:" << platformsPath;
        }
        if (QDir(sqldriversPath).exists()) {
            qDebug() << "  ✓ Found sqldrivers plugin:" << sqldriversPath;
        }
    }
}

void setupErrorLogging() {
    // Create log file path
    QString logPath = QDir::currentPath() + "/trf_error.log";
    
    g_logFile = new QFile(logPath);
    if (g_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        g_logStream = new QTextStream(g_logFile);
        #if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
            g_logStream->setCodec("UTF-8");  // Ensure log file uses UTF-8 encoding
        #else
            // Qt 6.0+ uses UTF-8 by default, no need to set
        #endif
        
        // Write startup marker
        *g_logStream << "\n" << QString(50, '=') << "\n";
        *g_logStream << "TRF Startup Diagnostics Log - " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
        *g_logStream << QString(50, '=') << "\n";
        g_logStream->flush();
    }
}

void logError(const QString &message) {
    qCritical() << message;
    if (g_logStream) {
        *g_logStream << "[ERROR] " << QDateTime::currentDateTime().toString("hh:mm:ss") 
                    << " " << message << "\n";
        g_logStream->flush();
    }
}

void logInfo(const QString &message) {
    qDebug() << message;
    if (g_logStream) {
        *g_logStream << "[INFO] " << QDateTime::currentDateTime().toString("hh:mm:ss") 
                    << " " << message << "\n";
        g_logStream->flush();
    }
}

void logSystemInfo() {
    logInfo("=== System Environment Diagnostics ===");
    logInfo(QString("Qt Version: %1").arg(QT_VERSION_STR));
    logInfo(QString("Qt Runtime Version: %1").arg(qVersion()));
    logInfo(QString("Application Directory: %1").arg(QDir::currentPath()));
    logInfo(QString("Executable Path: %1").arg(QCoreApplication::applicationFilePath()));
    
    // Check Qt plugin paths
    logInfo("Qt Plugin Search Paths:");
    QStringList pluginPaths = QCoreApplication::libraryPaths();
    for (const QString &path : pluginPaths) {
        logInfo(QString("  - %1 (exists: %2)").arg(path).arg(QDir(path).exists() ? "Yes" : "No"));
    }
    
    // File Storage System - No SQL drivers needed
    logInfo("Using File Storage System (JSON-based)");
    
    // Check platform plugins
    QString platformsPath = QDir::currentPath() + "/platforms";
    logInfo(QString("platforms directory: %1 (exists: %2)").arg(platformsPath).arg(QDir(platformsPath).exists() ? "Yes" : "No"));
    
    // Check styles
    logInfo("Available Styles:");
    QStringList styles = QStyleFactory::keys();
    for (const QString &style : styles) {
        logInfo(QString("  - %1").arg(style));
    }
}

bool checkResourceFiles() {
    logInfo("=== Checking Resource Files ===");
    
    // Check style file
    QFile styleFile(":/styles/global_colors.qss");
    bool styleExists = styleFile.exists();
    logInfo(QString("Global Style File: %1").arg(styleExists ? "Exists" : "Missing"));
    
    if (!styleExists) {
        logError("Critical resource file missing: global_colors.qss");
        return false;
    }
    
    // Check image resource examples
    QStringList testImages = {
        ":/images/background_login.png",
        ":/images/header_home.png", 
        ":/images/common/red_sample.png"
    };
    
    int missingImages = 0;
    for (const QString &imgPath : testImages) {
        QFile imgFile(imgPath);
        bool exists = imgFile.exists();
        logInfo(QString("Image Resource %1: %2").arg(imgPath).arg(exists ? "Exists" : "Missing"));
        if (!exists) missingImages++;
    }
    
    if (missingImages > 0) {
        logError(QString("Found %1 image resources missing").arg(missingImages));
    }
    
    return true;
}

void cleanupLogging() {
    if (g_logStream) {
        *g_logStream << "[INFO] " << QDateTime::currentDateTime().toString("hh:mm:ss") 
                    << " Program exited normally\n";
        delete g_logStream;
        g_logStream = nullptr;
    }
    
    if (g_logFile) {
        g_logFile->close();
        delete g_logFile;
        g_logFile = nullptr;
    }
}

void showErrorDialog(const QString &title, const QString &message) {
    QMessageBox msgBox;
    msgBox.setIcon(QMessageBox::Critical);
    msgBox.setWindowTitle(title);
    msgBox.setText(message);
    msgBox.setDetailedText(QString("Detailed error information can be found in the log file: %1/trf_error.log").arg(QDir::currentPath()));
    msgBox.setStandardButtons(QMessageBox::Ok);
    msgBox.exec();
}

int main(int argc, char *argv[])
{
    // 启动时内存监控
    logMemoryUsage("Application Start");

    // CRITICAL: 显式初始化资源系统 - 解决交叉编译后资源访问问题
    // 必须在QApplication创建之前初始化资源
    Q_INIT_RESOURCE(resources);
    
    // 验证资源初始化是否成功
    if (!QFile(":/styles/global_colors.qss").exists()) {
        qCritical() << "Critical Error: Resource initialization failed! Cannot access embedded resources.";
        qCritical() << "This typically indicates a cross-compilation or Qt version compatibility issue.";
        return -1;
    }
    
    // Set Qt compatibility before application creation
    setupQtCompatibility();
    
    // Set up error logging
    setupErrorLogging();
    
    logInfo("Starting TRF application initialization...");
        
        // Create application instance
        QApplication a(argc, argv);
        logInfo("QApplication created successfully");
        logMemoryUsage("After QApplication Creation");

        // Configure plugin paths and fonts
        setupPluginPaths();
        setupFontPaths();

        // Log system information
        logSystemInfo();
        logMemoryUsage("After System Setup");
        
        // Check resource files
        if (!checkResourceFiles()) {
            showErrorDialog("Resource File Error", "Critical resource files missing, program cannot run.");
            cleanupLogging();
            return -2;
        }
        
        logInfo("=== System Environment Diagnostics ===");
        logInfo(QString("Qt Version: %1").arg(QT_VERSION_STR));
        logInfo(QString("Qt Runtime Version: %1").arg(qVersion()));
        logInfo(QString("Application Directory: %1").arg(a.applicationDirPath()));
        logInfo(QString("Executable Path: %1").arg(a.applicationFilePath()));
        
        // Qt插件路径诊断
        logInfo("Qt Plugin Search Paths:");
        QStringList pluginPaths = a.libraryPaths();
        for (const QString& path : pluginPaths) {
            bool exists = QDir(path).exists();
            logInfo(QString("  - %1 (exists: %2)").arg(path, exists ? "Yes" : "No"));
        }
        
        // 初始化文件存储系统
        logInfo("Using File Storage System (JSON-based)");
        
        // 平台插件验证
        QString platformsDir = QDir(a.applicationDirPath()).absoluteFilePath("platforms");
        logInfo(QString("platforms directory: %1 (exists: %2)").arg(platformsDir, QFileInfo(platformsDir).exists() ? "Yes" : "No"));
        
        // 样式验证
        logInfo("Available Styles:");
        QStringList styles = QStyleFactory::keys();
        for (const QString& style : styles) {
            logInfo(QString("  - %1").arg(style));
        }
        
        // 资源文件验证
        logInfo("=== Checking Resource Files ===");
        QFile globalStyleFile(":/styles/global_colors.qss");
        logInfo(QString("Global Style File: %1").arg(globalStyleFile.exists() ? "Exists" : "Missing"));
        
        QFile bgImage(":/images/background_login.png");
        logInfo(QString("Image Resource :/images/background_login.png: %1").arg(bgImage.exists() ? "Exists" : "Missing"));
        
        QFile headerImage(":/images/header_home.png");
        logInfo(QString("Image Resource :/images/header_home.png: %1").arg(headerImage.exists() ? "Exists" : "Missing"));
        
        QFile testImage(":/images/common/red_sample.png");
        logInfo(QString("Image Resource :/images/common/red_sample.png: %1").arg(testImage.exists() ? "Exists" : "Missing"));
        
        // 初始化文件存储系统
        logInfo("Starting HumaFIA file storage system initialization...");
        logInfo("Using file storage system (SQLite replacement) - Persistent data storage");
        
        if (!FileStorage::getInstance()->initialize()) {
            QString error = QString("File storage initialization failed: %1").arg(FileStorage::getInstance()->getLastError());
            logError(error);
            showErrorDialog("File Storage Error", error);
            return -2;
        }
        
        logInfo("File storage system initialized successfully");
        logInfo(QString("Patient count: %1").arg(FileStorage::getInstance()->getAllPatients().size()));
        logInfo(QString("Test result count: %1").arg(FileStorage::getInstance()->getAllTestResults().size()));
        logInfo("Note: Using JSON file storage system, completely replacing SQLite database");

        // Load global stylesheet
        logInfo("Loading global stylesheet...");
        QFile styleFile(":/styles/global_colors.qss");
        if (styleFile.open(QFile::ReadOnly | QFile::Text)) {
            QByteArray styleData = styleFile.readAll();
            QString styleSheet = QString::fromUtf8(styleData);
            styleFile.close();
            
            // 验证样式表内容
            if (styleSheet.isEmpty()) {
                logError("Stylesheet file is empty");
            } else if (styleSheet.length() < 100) {
                logError(QString("Stylesheet seems too short (%1 bytes), might be corrupted").arg(styleSheet.length()));
            } else {
                // 检查是否包含基本的CSS语法
                if (!styleSheet.contains("{") || !styleSheet.contains("}")) {
                    logError("Stylesheet does not contain valid CSS syntax");
                } else {
                    // 应用样式表
                    a.setStyleSheet(styleSheet);
                    logInfo(QString("Global stylesheet loaded successfully (%1 bytes)").arg(styleSheet.length()));
                }
            }
        } else {
            logError("Failed to open global stylesheet file");
            // 使用最小化的降级样式表
            QString fallbackStyle = "* { font-family: sans-serif; font-size: 10pt; }";
            a.setStyleSheet(fallbackStyle);
            logInfo("Applied minimal fallback stylesheet");
        }
        
        // GUI creation and error handling
        logInfo("Creating login screen...");
        LoginScreen* loginScreen = nullptr;
        MainWindow* mainWindow = nullptr;
        
        loginScreen = new LoginScreen();
        if (!loginScreen) {
            logError("Failed to create login screen: Memory allocation failed");
            showErrorDialog("GUI Error", "Failed to create login screen: Memory allocation failed");
            return -5;
        }
        logInfo("Login screen created successfully");
        
        logInfo("Creating main window...");
        mainWindow = new MainWindow();
        if (!mainWindow) {
            logError("Failed to create main window: Memory allocation failed");
            delete loginScreen;
            showErrorDialog("GUI Error", "Failed to create main window: Memory allocation failed");
            return -6;
        }
        logInfo("Main window created successfully");

        // Connect login success signal with enhanced display logic
        QObject::connect(loginScreen, &LoginScreen::loginSuccessful, [&]() {
            logInfo("Login successful, showing main window");

            // 强制全屏显示主窗口
            mainWindow->showFullScreen();
            mainWindow->raise();
            mainWindow->activateWindow();
            mainWindow->repaint();
            QCoreApplication::processEvents();

            // 验证主窗口显示状态
            if (mainWindow->isVisible()) {
                logInfo("Main window displayed successfully and is visible");
            } else {
                logError("Main window show() called but window is not visible");
            }

            // 关闭登录窗口
            loginScreen->close();
        });

        // 检查Qt平台插件状态
        logInfo("Checking Qt platform plugin status...");
        QGuiApplication* guiApp = qobject_cast<QGuiApplication*>(QCoreApplication::instance());
        if (guiApp) {
            logInfo(QString("Qt platform name: %1").arg(guiApp->platformName()));
        } else {
            logError("Failed to get QGuiApplication instance");
        }

        // Show login screen with enhanced display logic
        logInfo("Displaying login screen");
        logInfo("Step 1: About to call showFullScreen()");
        // 尝试先用普通显示，再切换到全屏
        logInfo("Step 2a: Trying normal show() first");
        loginScreen->show();
        logInfo("Step 2b: Normal show() completed, now trying showFullScreen()");

        // 强制全屏显示，确保在framebuffer上可见
        loginScreen->showFullScreen();
        logInfo("Step 2c: showFullScreen() completed successfully");

        // 强制激活窗口并置于前台
        logInfo("Step 3: About to call raise() and activateWindow()");
        loginScreen->raise();
        loginScreen->activateWindow();
        logInfo("Step 4: raise() and activateWindow() completed");

        // 强制刷新显示
        logInfo("Step 5: About to call repaint() and processEvents()");
        loginScreen->repaint();
        QCoreApplication::processEvents();
        logInfo("Step 6: repaint() and processEvents() completed");

        // 额外的显示强制措施（嵌入式系统特殊处理）
        logInfo("Step 7: About to call update() and setFocus()");
        loginScreen->update();
        loginScreen->setFocus();
        QCoreApplication::processEvents();
        logInfo("Step 8: update() and setFocus() completed");

        // 短暂延迟确保显示完成
        logInfo("Step 9: About to sleep for 100ms");
        QThread::msleep(100);
        logInfo("Step 10: Sleep completed");

        // 验证窗口是否真正可见
        if (loginScreen->isVisible()) {
            logInfo("Login screen displayed successfully and is visible");
            qDebug() << "✅ Login screen is visible";
        } else {
            logError("Login screen show() called but window is not visible");
            qDebug() << "❌ Login screen is NOT visible";
        }

        // 输出窗口几何信息用于调试
        QRect geometry = loginScreen->geometry();
        logInfo(QString("Login screen geometry: %1x%2 at (%3,%4)")
               .arg(geometry.width()).arg(geometry.height())
               .arg(geometry.x()).arg(geometry.y()));
        qDebug() << "Login screen geometry:" << geometry;

        // 强制输出到标准输出确保可见
        printf("=== CRITICAL DEBUG: About to enter event loop ===\n");
        fflush(stdout);
        
        logInfo("Entering application event loop");
        qDebug() << "🔄 About to enter Qt event loop";
        printf("=== CRITICAL DEBUG: Entering application event loop ===\n");
        fflush(stdout);

        // 最后一次验证窗口状态
        if (loginScreen->isVisible()) {
            logInfo("Final check: Login screen is visible before entering event loop");
            qDebug() << "✅ Final check: Login screen is visible";
            printf("=== CRITICAL DEBUG: Login screen IS visible ===\n");
        } else {
            logError("Final check: Login screen is NOT visible before entering event loop");
            qDebug() << "❌ Final check: Login screen is NOT visible";
            printf("=== CRITICAL DEBUG: Login screen is NOT visible ===\n");
        }
        fflush(stdout);

        // 强制处理所有待处理事件
        QCoreApplication::processEvents();
        qDebug() << "🔄 Processed pending events, starting event loop";
        printf("=== CRITICAL DEBUG: About to call a.exec() ===\n");
        fflush(stdout);

        int result = a.exec();
        printf("=== CRITICAL DEBUG: a.exec() returned with code: %d ===\n", result);
        fflush(stdout);
        qDebug() << "🔄 Event loop exited with code:" << result;
        
        // Cleanup
        delete mainWindow;
        delete loginScreen;
        
        // File storage system cleanup (automatic)
        logInfo("File storage system closed");
        logInfo(QString("Program exited normally, return code: %1").arg(result));

        cleanupLogging();
        return result;
}

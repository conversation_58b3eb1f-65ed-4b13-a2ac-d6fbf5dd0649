#ifndef DATEPICKERDIALOG_H
#define DATEPICKERDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QScrollArea>
#include <QListWidget>
#include <QDate>

class DatePickerDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DatePickerDialog(QWidget *parent = nullptr);
    QDate getSelectedDate() const;
    void setSelectedDate(const QDate &date);

private slots:
    void onYearSelected();
    void onMonthSelected();
    void onDaySelected();
    void onOkClicked();
    void onCancelClicked();

private:
    void setupUi();
    void updateDaysList();
    void setSelectionStyle();
    
    QListWidget *yearList;
    QListWidget *monthList;
    QListWidget *dayList;
    QPushButton *okButton;
    QPushButton *cancelButton;
    
    QDate selectedDate;
};

#endif // DATEPICKERDIALOG_H 
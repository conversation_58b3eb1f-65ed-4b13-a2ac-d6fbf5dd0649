# ARM系统Qt兼容性修复完成

## 任务概述
修复TRF程序在目标ARM系统(myd-y6ull14x14, Qt 5.12.8)上运行时的兼容性问题。

## 问题分析
根据error.txt错误日志，发现以下主要问题：
1. **Qt版本不兼容**：系统Qt 5.12.8 vs 程序期望Qt 5.6.2
2. **中文显示乱码**：字符编码问题导致中文显示异常
3. **字体目录缺失**：`/usr/share/fonts`目录不存在
4. **tslib插件错误**：触摸输入插件加载失败

## 修复方案

### 1. 宽泛Qt版本兼容支持
**文件**: `code/trf/main.cpp`
- 添加`setupQtCompatibility()`函数
- 支持Qt 5.6+ 到 Qt 5.15+ 全系列版本
- 设置UTF-8编码支持
- 动态检测Qt版本并适配

### 2. 智能字体路径检测
**文件**: `code/trf/main.cpp`
- 添加`setupFontPaths()`函数
- 检测多个可能的字体目录：
  - `/usr/share/fonts` (标准Linux)
  - `/usr/local/share/fonts` (用户安装)
  - `/system/fonts` (Android风格)
  - `/opt/qt5/fonts` (Qt5特定)
  - 应用程序目录下字体
- 自动设置`QT_QPA_FONTDIR`环境变量

### 3. 智能插件路径配置
**文件**: `code/trf/main.cpp`
- 添加`setupPluginPaths()`函数
- 检测并添加多个插件路径：
  - `/usr/lib/qt5/plugins`
  - `/usr/lib/arm-linux-gnueabihf/qt5/plugins`
  - `/usr/local/lib/qt5/plugins`
  - `/opt/qt5/plugins`
  - 应用程序目录

### 4. 触摸输入智能降级
**文件**: `.github/workflows/release.yml` (run.sh脚本)
- 三级降级策略：
  1. **优先**: tslib + /dev/input/event0 (如果tslib库存在)
  2. **降级**: evdevtouch + /dev/input/event1
  3. **最后**: 键盘鼠标输入
- 智能检测设备和库的可用性

### 5. 构建配置优化
**文件**: `code/trf/trf.pro`
- 设置`QT_DISABLE_DEPRECATED_BEFORE=0x050600`确保Qt 5.6+兼容
- 添加UTF-8编码支持：`CODECFORTR = UTF-8`
- 优化编译参数

**文件**: `.github/workflows/release.yml`
- 更新Docker构建配置使用Qt 5.6+兼容性
- 添加UTF-8支持编译标志

## 技术特性

### Qt版本兼容性
```cpp
// 支持Qt 5.6+ 到 Qt 5.15+ 全系列
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif
```

### 智能路径检测
```cpp
QStringList fontPaths = {
    "/usr/share/fonts",
    "/usr/local/share/fonts", 
    "/system/fonts",
    "/opt/qt5/fonts",
    // ...
};
```

### 触摸输入降级
```bash
# 1. 尝试tslib
if [ -c /dev/input/event0 ] && ldconfig -p | grep -q "libts"; then
    export QT_QPA_GENERIC_PLUGINS=tslib:/dev/input/event0
# 2. 降级到evdev
elif [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS=evdevtouch:/dev/input/event1
fi
```

## 预期效果
1. **消除Qt版本兼容性警告**
2. **修复中文字符乱码问题**
3. **解决字体加载失败警告**
4. **改善触摸输入或提供优雅降级**
5. **提供更详细的错误诊断信息**

## 测试建议
1. 在目标ARM系统上重新测试启动
2. 检查`trf_error.log`和`trf_startup.log`日志文件
3. 验证中文字符显示是否正常
4. 测试触摸输入功能

## 构建说明
重新构建时将自动应用这些修复：
```bash
# 重新构建ARM版本以包含修复
git tag v1.0.1.0  # 或适当的版本号
git push origin v1.0.1.0
```

新构建的版本将包含所有兼容性修复，应该能在Qt 5.12.8系统上正常运行。 
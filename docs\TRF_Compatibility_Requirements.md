# TRF 项目兼容性要求规范

## 概述
本文档定义了TRF项目在开发过程中必须遵循的兼容性要求，确保软件能够在目标硬件平台上稳定运行。

## 目标平台规格

### 主要目标系统
- **硬件平台**: myd-y6ull14x14 开发板
- **操作系统**: Linux 4.1.15+ armv7l GNU/Linux
- **系统架构**: ARMv7l (ARM Cortex-A7)
- **Qt版本**: Qt 5.6.2 (关键兼容性基准)

### 硬件组件要求
- **处理器**: NXP i.MX6UL (ARM Cortex-A7 528MHz)
- **触摸控制器**: iMX6UL TouchScreen Controller
- **显示输出**: LinuxFB 帧缓冲
- **图形加速**: Vivante GC/VG GPU (EGL + OpenGL ES 2.0)
- **输入设备**: /dev/input/event1 (触摸), /dev/input/event0 (备用)

## Qt框架兼容性要求

### 核心版本要求
- **最低支持版本**: Qt 5.6.0
- **目标版本**: Qt 5.6.2 (精确匹配)
- **最高兼容版本**: Qt 5.15.x (LTS)
- **禁用版本**: Qt 6.x (不兼容ARM平台)

### Qt模块兼容性
```cpp
// 必需的Qt模块及其最低版本
Qt5Core     >= 5.6.0   // 核心功能
Qt5Gui      >= 5.6.0   // GUI基础
Qt5Widgets  >= 5.6.0   // 控件系统  
Qt5Sql      >= 5.6.0   // 数据库支持
Qt5Network  >= 5.6.0   // 网络功能
Qt5Concurrent >= 5.6.0 // 并发处理

// 可选模块
Qt5DBus     >= 5.6.0   // D-Bus支持
Qt5Xml      >= 5.6.0   // XML解析
Qt5Test     >= 5.6.0   // 单元测试
```

### Qt API兼容性规则
```cpp
// 禁用新版本API，确保向后兼容
#define QT_DISABLE_DEPRECATED_BEFORE 0x050600  // Qt 5.6.0

// 避免使用的新版本API
- QRandomGenerator (Qt 5.10+)    → 使用 qrand()
- QStringView (Qt 5.10+)         → 使用 QString
- qOverload (Qt 5.7+)            → 使用传统重载语法
- Q_FALLTHROUGH (Qt 5.8+)        → 使用注释或条件编译

// 推荐的兼容性检查
#if QT_VERSION >= QT_VERSION_CHECK(5, 10, 0)
    // 新版本特性
#else  
    // 兼容性实现
#endif
```

## 编译器和构建要求

### 编译器兼容性
- **主要编译器**: GCC 5.4+ (Ubuntu 16.04标准)
- **架构参数**: `-march=armv7-a -mfpu=neon -mfloat-abi=hard`
- **优化级别**: `-O2` (平衡性能和稳定性)
- **调试信息**: `-g` (调试版本)

### 构建环境
```dockerfile
# 推荐的Docker构建环境
FROM arm32v7/ubuntu:16.04

# 必需的包
apt-get install -y \
    qt5-default \           # Qt 5.6.x基础包
    qtbase5-dev \           # 开发头文件
    qtbase5-dev-tools \     # 构建工具
    libqt5sql5-sqlite \     # SQLite驱动
    build-essential \       # 编译工具链
    pkg-config             # 包配置工具
```

### qmake配置要求
```pro
# trf.pro 兼容性配置
QT += core gui widgets sql network

# 版本兼容性设置
CONFIG += c++11                    # C++11标准（Qt 5.6兼容）
DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x050600

# ARM平台优化
target.path = /usr/local/bin
INSTALLS += target

# 条件编译支持
contains(DEFINES, TARGET_SYSTEM_MYD_Y6ULL14X14) {
    # myd-y6ull14x14专用配置
    DEFINES += ARM_EMBEDDED_OPTIMIZATIONS
}
```

## 系统库依赖要求

### 图形系统
```bash
# 必需的图形库
libEGL.so.1         # OpenGL ES接口
libGLESv2.so.2      # OpenGL ES 2.0实现
libdrm.so.2         # Direct Rendering Manager

# LinuxFB支持
QT_QPA_PLATFORM=linuxfb
QT_QPA_FB_DISABLE_INPUT=0
```

### 触摸输入系统
```bash
# 触摸库优先级
1. libts-1.0.so.0           # tslib触摸库 (首选)
2. evdevtouch              # 内核evdev接口 (备用)
3. keyboard/mouse          # 键盘鼠标 (最后选择)

# 设备节点优先级
1. /dev/input/event1       # iMX6UL TouchScreen Controller
2. /dev/input/event0       # 备用输入设备
```

### 数据库支持
```bash
# SQLite支持要求
libQt5Sql.so.5              # Qt SQL模块
libsqlite3.so.0             # SQLite库
/usr/lib/qt5/plugins/sqldrivers/libqsqlite.so  # SQLite驱动插件
```

## 代码兼容性规范

### C++标准要求
- **标准版本**: C++11 (Qt 5.6.2兼容)
- **禁用特性**: C++14/17/20新特性
- **内存管理**: 避免智能指针复杂用法，使用Qt对象树

### Qt特性使用规范
```cpp
// ✅ 推荐使用 (Qt 5.6兼容)
QString, QStringList, QByteArray    // 字符串处理
QTimer, QThread, QMutex            // 并发控制
QWidget, QLayout, QPushButton      // UI组件
QSqlDatabase, QSqlQuery            // 数据库操作
QFile, QDir, QStandardPaths        // 文件系统

// ⚠️ 条件使用 (需要版本检查)
QJsonDocument, QJsonObject         // JSON处理 (Qt 5.0+)
QLoggingCategory                   // 日志分类 (Qt 5.2+)

// ❌ 避免使用 (Qt 5.6后新增)
QRandomGenerator                   // 随机数生成器 (Qt 5.10+)
QStringView                        // 字符串视图 (Qt 5.10+)
QMetaType::fromType()              // 元类型 (Qt 5.15+)
```

### 平台特定代码
```cpp
// 使用条件编译进行平台适配
#ifdef TARGET_SYSTEM_MYD_Y6ULL14X14
    // myd-y6ull14x14专用代码
    setupTouchscreenInput();
    configureLinuxFB();
#endif

#ifdef Q_OS_WIN
    // Windows平台代码
#endif

#ifdef Q_OS_LINUX  
    // Linux通用代码
#endif
```

## 环境变量配置规范

### Qt环境变量
```bash
# 核心配置
export QT_SELECT=qt5
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FONTDIR="/usr/share/fonts:/usr/share/fonts/truetype"

# 插件路径
export QT_PLUGIN_PATH="/usr/lib/qt5/plugins:/usr/lib/arm-linux-gnueabihf/qt5/plugins"

# 图形优化
export QT_QPA_EGLFS_INTEGRATION=eglfs_viv
export QT_QPA_EGLFS_DISABLE_INPUT=0

# 日志控制
export QT_LOGGING_RULES="qt.qpa.fonts.warning=false;qt.widgets.gestures.debug=false"

# 字符编码
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
export LC_CTYPE=C.UTF-8
```

## 测试和验证要求

### 兼容性测试清单
- [ ] Qt库版本检查 (期望 5.6.x)
- [ ] 系统架构验证 (armv7l)
- [ ] 硬件设备检测 (/dev/fb0, /dev/input/event1)
- [ ] 图形库可用性 (EGL, OpenGL ES)
- [ ] 触摸输入功能 (iMX6UL Controller)
- [ ] 数据库功能 (SQLite驱动)
- [ ] 字体渲染 (中文支持)
- [ ] 应用启动和基本操作

### 自动化验证脚本
```bash
# 系统兼容性检查
./check.sh                    # 返回匹配度评分 (A+/A/B/C/F)

# Python详细验证
python3 scripts/verify_system_compatibility.py

# 构建测试
python3 scripts/test_build.py
```

## 版本管理规范

### 发布版本命名
- **主版本**: v1.x.x (重大功能变更)
- **次版本**: v1.1.x (兼容性功能添加)
- **修订版本**: v1.1.1 (兼容性修复)
- **预发布**: v1.1.1-rc.1 (候选版本)

### 兼容性声明
每个发布版本必须包含兼容性声明：
```
TRF v1.1.0 兼容性声明:
- 目标平台: myd-y6ull14x14 (Linux 4.1.15+ armv7l)
- Qt版本: 5.6.2 (精确匹配)
- 构建环境: Ubuntu 16.04 + Qt 5.6.x
- 兼容性等级: A+ (完美匹配)
```

## 开发流程规范

### 新功能开发
1. **兼容性评估**: 检查新功能对Qt 5.6.2的影响
2. **API选择**: 优先使用Qt 5.6兼容的API
3. **条件编译**: 为不同Qt版本提供兼容实现
4. **测试验证**: 在目标平台上验证功能

### 代码审查要点
- [ ] 是否使用了Qt 5.6不支持的API
- [ ] 是否添加了适当的版本检查
- [ ] 是否考虑了ARM平台的特殊性
- [ ] 是否更新了兼容性测试

### 持续集成要求
- **构建环境**: 必须使用Ubuntu 16.04 + Qt 5.6.x
- **目标平台**: ARM v7l交叉编译
- **验证步骤**: 兼容性检查 → 构建 → 功能测试

## 故障排除指南

### 常见兼容性问题
1. **Qt版本不匹配**: 
   - 症状: 启动失败，库加载错误
   - 解决: 检查ldconfig输出，确认Qt 5.6.x库

2. **触摸输入失效**:
   - 症状: 无法响应触摸操作
   - 解决: 检查/dev/input/event1，配置evdevtouch

3. **字体渲染问题**:
   - 症状: 中文乱码或字体缺失
   - 解决: 配置QT_QPA_FONTDIR，安装中文字体

4. **图形渲染异常**:
   - 症状: 界面显示错误或黑屏
   - 解决: 检查LinuxFB配置，验证EGL支持

### 调试工具
```bash
# 系统信息收集
uname -a                      # 系统架构
ldconfig -p | grep Qt5        # Qt库版本
ldd ./trf | grep Qt5          # 程序依赖
ls /dev/input/event*          # 输入设备

# 运行时调试
QT_LOGGING_RULES="*.debug=true" ./trf   # 启用详细日志
strace -e file ./trf          # 文件访问跟踪
gdb ./trf                     # 调试器
```

## 更新维护

### 定期评估
- **季度评估**: 检查目标平台系统更新
- **年度评估**: 考虑Qt版本升级的可行性
- **兼容性矩阵**: 维护支持的平台和版本列表

### 文档更新
- 兼容性要求变更需要更新本文档
- 新增的兼容性问题和解决方案需要记录
- 测试用例需要与兼容性要求保持同步

---

**版本**: 1.0.0  
**更新日期**: 2025年1月  
**适用范围**: TRF项目所有开发人员  
**审核**: 项目架构师

遵循此兼容性要求规范，确保TRF项目在目标平台上的稳定性和可靠性。 
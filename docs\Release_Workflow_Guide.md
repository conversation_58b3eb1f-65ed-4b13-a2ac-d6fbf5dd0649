# TRF GitHub Actions 发布工作流指南

## 概述

本项目已配置自动化的 GitHub Actions 工作流，可以自动构建并发布支持多个平台的 TRF 应用程序。

## 支持的平台

- **Windows x64**: 便携版 ZIP 包，包含所有必要的 Qt5 运行时依赖  
- **Linux ARM v7l**: 源码包，需在目标 ARMv7l 系统上本地构建

## 如何创建发布

### 方法 1: 标签触发（推荐）

1. **创建版本标签**:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. **自动构建**: 工作流将自动开始构建两个平台的版本

3. **自动发布**: 构建完成后会自动创建 GitHub Release

### 方法 2: 手动触发

1. 进入 GitHub 仓库的 **Actions** 页面
2. 选择 **Release Build** 工作流
3. 点击 **Run workflow** 按钮
4. 选择分支并点击 **Run workflow**

## 构建产物

### Windows 版本
- **文件名**: `TRF-{version}-Windows-x64.zip`
- **内容**: 
  - `trf.exe` - 主程序
  - Qt 运行时库和依赖
  - 必要的插件和资源文件

### Linux ARM 版本
- **文件名**: `TRF-{version}-Linux-ARM-v7l-Source.tar.gz`
- **内容**:
  - 必要的源代码文件（.cpp, .h, .ui, .pro, .qrc）
  - 资源文件（styles/, images/）
  - `build.sh` - 构建脚本
  - `run.sh` - 启动脚本

## 本地测试

在提交代码前，可以使用提供的测试脚本验证构建是否正常：

```bash
# Windows (PowerShell/CMD)
python scripts/test_build.py

# Linux/macOS
python3 scripts/test_build.py
```

## 工作流配置

### 环境变量
- `QT_VERSION`: Qt 版本 (当前: 5.15.2 - 匹配目标ARM系统)

### 触发条件
- 推送版本标签 (`v*`)
- 手动触发

### 构建超时
- Windows: 默认 (通常 10-15 分钟)
- Linux ARM: 45 分钟 (由于交叉编译)

## 目标系统兼容性

### 基于实际硬件的配置
本工作流专门为以下目标系统优化：

**Linux ARM 目标系统信息**:
```
uname -a: Linux myd-y6ull14x14 4.1.15+ #4 SMP PREEMPT armv7l
```

**已验证的Qt5库** (通过 ldconfig -p):
- libQt5Core.so.5, libQt5Gui.so.5, libQt5Widgets.so.5
- libQt5Network.so.5, libQt5Concurrent.so.5, libQt5DBus.so.5
- libQt5Xml.so.5, libQt5Sql.so.5, libQt5Test.so.5

**运行环境设置**:
- QT_QPA_PLATFORM=linuxfb (Linux帧缓冲)
- QT_QPA_GENERIC_PLUGINS=tslib (触摸屏支持)
- 字体路径: /usr/share/fonts

## 故障排除

### 常见问题

1. **Windows 构建失败**
   - 检查是否有 Qt 版本兼容性问题
   - 验证所有 Qt 模块都已正确引用

2. **Linux ARM 构建失败**
   - 交叉编译可能需要特定的库依赖
   - 检查 ARM 工具链是否正确安装

3. **资源文件缺失**
   - 确保 `resources.qrc` 包含所有必要的资源
   - 检查资源文件路径是否正确

### 查看构建日志

1. 进入 GitHub 仓库的 **Actions** 页面
2. 点击相应的工作流运行
3. 展开各个步骤查看详细日志

## 版本管理

### 版本号格式
推荐使用语义化版本控制（SemVer）:
- `v1.0.0` - 主版本
- `v1.1.0` - 次版本  
- `v1.1.1` - 修订版本

### 预发布版本
对于测试版本，可以使用：
- `v1.0.0-beta.1`
- `v1.0.0-rc.1`

## 高级配置

### 修改 Qt 版本
编辑 `.github/workflows/release.yml` 中的环境变量：
```yaml
env:
  QT_VERSION: '5.15.2'  # 当前版本，匹配目标ARM系统的Qt5
```

### 添加新平台
1. 在工作流中添加新的构建任务
2. 配置相应的工具链和依赖
3. 更新发布说明生成逻辑

### 自定义构建选项
修改 qmake 命令参数：
```bash
qmake trf.pro CONFIG+=release CONFIG+=custom_option
```

## 相关文件

- `.github/workflows/release.yml` - 主工作流文件
- `scripts/test_build.py` - 本地构建测试脚本
- `issues/GitHub Actions发布工作流实现.md` - 实现记录
- `code/trf/trf.pro` - Qt 项目文件

## 联系信息

如有问题或建议，请在 GitHub Issues 中反馈。 
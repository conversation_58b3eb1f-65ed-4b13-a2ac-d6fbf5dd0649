#ifndef STATSAMPLEPAGE_H
#define STATSAMPLEPAGE_H

#include <QWidget>
#include <QScrollArea>
#include <QLabel>
#include <QVBoxLayout>
#include <QScroller>
#include <QMap>
#include <QVector>
#include <QScrollBar>
#include <QElapsedTimer>

// Forward declarations
class TestResult;
class Patient;

namespace Ui {
class StatSamplePage;
}

// Struct definition - StatSample row data
struct StatSampleRowData {
    int rowNumber;
    QString patient;
    QString parameter;
    QString result;
    QString datetime;
    QString timer;
    QString lot;
    QString cutoff;
    
    StatSampleRowData() : rowNumber(0) {}
    StatSampleRowData(int num, const QString &pat, const QString &param, const QString &res,
                      const QString &dt, const QString &tm, const QString &lt, const QString &co)
        : rowNumber(num), patient(pat), parameter(param), result(res), datetime(dt), timer(tm), lot(lt), cutoff(co) {}
};

// Struct definition - Dynamic row UI elements
struct StatSampleDynamicRowUI {
    QWidget* widget;           // Row container widget
    QLabel* patientLabel;
    QLabel* parameterLabel;
    QLabel* resultLabel;
    QLabel* datetimeLabel;
    QLabel* timerLabel;
    QLabel* lotLabel;
    QLabel* cutoffLabel;
    
    StatSampleDynamicRowUI() : widget(nullptr), patientLabel(nullptr), parameterLabel(nullptr), resultLabel(nullptr),
                               datetimeLabel(nullptr), timerLabel(nullptr), lotLabel(nullptr), cutoffLabel(nullptr) {}
};

class StatSamplePage : public QWidget
{
    Q_OBJECT

public:
    explicit StatSamplePage(QWidget *parent = nullptr);
    ~StatSamplePage();

    // Dynamic row management methods
    void addSampleRow(const QString &patient, const QString &parameter, 
                     const QString &result, const QString &datetime,
                     const QString &timer, const QString &lot, const QString &cutoff);
    void addSampleRow(const StatSampleRowData &rowData);
    void removeRow(int rowNumber);
    void clearAllRows();
    void updateScrollableContent();
    void generateDemoData(); // Generate random demo data
    
    // Lazy loading related methods
    void loadInitialDataFromDatabase();
    void convertTestResultToRowData(const TestResult& testResult, const Patient& patient, StatSampleRowData& rowData);
    bool isDataLoaded() const { return m_dataLoaded; }
    int getNextRowNumber(); // New method: get next row number for inserting new data at the top
    
    // Initialization and data loading methods
    void initializeWithTestData(); // Deprecated
    void loadHundredRows(); // Deprecated
    void initializeEmptyDisplay(); // Initialize empty display state
    void ensureMinimumRows(); // Ensure at least 5 rows are displayed
    int findFirstEmptyRowOrGetNext(); // Find first empty row or get next row number
    
    // 数据清空后刷新方法
    void refreshAfterDataClear();

signals:
    void addSampleClicked();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void showEvent(QShowEvent *event) override; // 添加showEvent处理页面显示时的懒加载

private:
    Ui::StatSamplePage *ui;
    
    // 动态行数据管理 - 按照AutoSample模式
    QMap<int, StatSampleRowData> m_rowData;                      // 行号 -> 行数据
    QMap<int, StatSampleDynamicRowUI*> m_dynamicRows;           // 行号 -> UI组件
    QVector<int> m_visibleRows;                                 // 当前可见的行号列表
    QWidget* m_scrollContent;
    QScrollArea* m_scrollArea;
    
    // 懒加载标志
    bool m_dataLoaded;                                          // 数据是否已从数据库加载
    
    // 列配置 - StatSample专用
    struct ColumnConfig {
        int x;
        int width;
    };
    
    static const ColumnConfig PATIENT_COL;
    static const ColumnConfig PARAMETER_COL;
    static const ColumnConfig RESULT_COL;
    static const ColumnConfig DATETIME_COL;
    static const ColumnConfig TIMER_COL;
    static const ColumnConfig LOT_COL;
    static const ColumnConfig CUTOFF_COL;
    
    static const int ROW_HEIGHT = 66;
    static const int CONTENT_WIDTH = 1012;
    
    // 私有方法
    void setupScrollableTable();
    void enableMouseScrolling();
    void enableTouchScrolling();
    
    // 动态行核心方法 - 按照AutoSample模式
    StatSampleDynamicRowUI* createDataRow(const StatSampleRowData& data);
    void positionRow(StatSampleDynamicRowUI* rowUI, int visualIndex);
    void applyRowStyles(StatSampleDynamicRowUI* rowUI);
    void destroyRow(StatSampleDynamicRowUI* rowUI);
    void updateVisibleRows();
    int getRowDisplayIndex(int rowNumber);
    QLabel* createColumnLabel(QWidget *parent, const ColumnConfig &config, const QString &text);
    void updateRowUI(StatSampleDynamicRowUI *rowUI, const StatSampleRowData &rowData);
    void repositionAllRows();
    
public slots:
    void testScrolling(); // For debugging
    
private slots:
    void onScrollValueChanged(int value);
};

#endif // STATSAMPLEPAGE_H 
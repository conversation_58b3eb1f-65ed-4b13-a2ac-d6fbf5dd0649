
#ifndef MAINSCREEN_H
#define MAINSCREEN_H

#include <QWidget>
#include <QTimer>

namespace Ui {
class MainScreen;
}

// Forward declarations
class QPushButton;

class MainScreen : public QWidget
{
    Q_OBJECT

public:
    explicit MainScreen(QWidget *parent = nullptr);
    ~MainScreen();

    // Add public getters for the navigation buttons
    QPushButton *getAutoSampleButton();
    QPushButton *getStatSampleButton();
    QPushButton *getFastModeButton();
    QPushButton *getQcModuleButton();
    QPushButton *getDatabaseButton();
    QPushButton *getSettingsButton();

public slots:
    void updateDateTime();
    void on_main_button_clicked();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

private:
    Ui::MainScreen *ui;
    QTimer *timer;
};

#endif // MAINSCREEN_H 
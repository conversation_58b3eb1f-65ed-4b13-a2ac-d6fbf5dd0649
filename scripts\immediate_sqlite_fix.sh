#!/bin/bash
# TRF SQLite 立即修复脚本
# 临时解决方案 - 在完美交叉编译版本发布前使用
# 适用于当前遇到的SQLite驱动加载问题

echo "TRF SQLite 立即修复脚本"
echo "=============================="
echo "这是一个临时解决方案，用于修复当前的SQLite驱动问题"
echo "完美的静态SQLite版本正在GitHub Actions中构建..."
echo ""

APP_DIR="/mnt/sd/TRF-1.0.0.28-Linux-ARM-v7l"

if [ ! -d "$APP_DIR" ]; then
    echo "❌ 错误：未找到应用目录 $APP_DIR"
    echo "请确认TRF应用已正确部署到此路径"
    exit 1
fi

cd "$APP_DIR"

echo "📍 当前目录：$(pwd)"
echo "📊 诊断当前SQLite问题..."

# 1. 检查系统SQLite驱动
echo ""
echo "=== 系统SQLite驱动检查 ==="
if ldconfig -p | grep -q "libQt5Sql.so.5"; then
    QT_SQL_PATH=$(ldconfig -p | grep "libQt5Sql.so.5" | awk '{print $NF}' | head -1)
    echo "✅ 找到系统Qt5 SQL库: $QT_SQL_PATH"
    
    # 检查SQLite插件
    SYSTEM_PLUGIN_PATHS=(
        "/usr/lib/qt5/plugins/sqldrivers"
        "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers"
        "/usr/local/qt5/plugins/sqldrivers"
    )
    
    FOUND_SYSTEM_SQLITE=false
    for plugin_path in "${SYSTEM_PLUGIN_PATHS[@]}"; do
        if [ -f "$plugin_path/libqsqlite.so" ]; then
            echo "✅ 找到系统SQLite插件: $plugin_path/libqsqlite.so"
            SYSTEM_SQLITE_PLUGIN="$plugin_path/libqsqlite.so"
            FOUND_SYSTEM_SQLITE=true
            break
        fi
    done
    
    if [ "$FOUND_SYSTEM_SQLITE" = false ]; then
        echo "⚠ 未找到系统SQLite插件，尝试安装..."
        apt-get update > /dev/null 2>&1
        apt-get install -y libqt5sql5-sqlite > /dev/null 2>&1
        
        # 重新检查
        for plugin_path in "${SYSTEM_PLUGIN_PATHS[@]}"; do
            if [ -f "$plugin_path/libqsqlite.so" ]; then
                echo "✅ 安装后找到SQLite插件: $plugin_path/libqsqlite.so"
                SYSTEM_SQLITE_PLUGIN="$plugin_path/libqsqlite.so"
                FOUND_SYSTEM_SQLITE=true
                break
            fi
        done
    fi
else
    echo "❌ 未找到系统Qt5 SQL库"
    echo "正在尝试安装Qt5 SQL支持..."
    apt-get update > /dev/null 2>&1
    apt-get install -y qt5-default libqt5sql5-sqlite > /dev/null 2>&1
fi

# 2. 创建兼容的SQLite插件环境
echo ""
echo "=== 创建兼容SQLite环境 ==="

# 清理可能冲突的插件目录
if [ -d "$APP_DIR/plugins" ]; then
    echo "🧹 清理现有插件目录..."
    rm -rf "$APP_DIR/plugins"
fi

# 创建新的插件目录结构
mkdir -p "$APP_DIR/plugins/sqldrivers"
mkdir -p "$APP_DIR/plugins/platforms"

# 复制系统SQLite插件
if [ "$FOUND_SYSTEM_SQLITE" = true ] && [ -f "$SYSTEM_SQLITE_PLUGIN" ]; then
    echo "📋 复制系统SQLite插件..."
    cp "$SYSTEM_SQLITE_PLUGIN" "$APP_DIR/plugins/sqldrivers/"
    chmod 755 "$APP_DIR/plugins/sqldrivers/libqsqlite.so"
    echo "✅ SQLite插件已复制并设置权限"
else
    echo "❌ 无法找到可用的系统SQLite插件"
    echo "将使用替代方案..."
    
    # 创建符号链接到系统库（如果可能）
    if [ -f "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers/libqsqlite.so" ]; then
        ln -sf "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers/libqsqlite.so" "$APP_DIR/plugins/sqldrivers/"
        echo "✅ 创建SQLite插件符号链接"
    fi
fi

# 复制平台插件
PLATFORM_PLUGIN_PATHS=(
    "/usr/lib/qt5/plugins/platforms"
    "/usr/lib/arm-linux-gnueabihf/qt5/plugins/platforms"
)

for platform_path in "${PLATFORM_PLUGIN_PATHS[@]}"; do
    if [ -d "$platform_path" ]; then
        echo "📋 复制平台插件从: $platform_path"
        cp -r "$platform_path"/* "$APP_DIR/plugins/platforms/" 2>/dev/null || true
        break
    fi
done

# 3. 创建优化的启动脚本
echo ""
echo "=== 创建优化启动脚本 ==="

cat > "$APP_DIR/run_sqlite_fixed.sh" << 'EOF'
#!/bin/bash
# TRF SQLite 修复版启动脚本
# 临时解决方案，优化SQLite驱动加载

echo "TRF SQLite 修复版启动 - $(date)"
echo "=================================="
echo "使用优化的SQLite驱动配置"

APP_DIR="$(pwd)"

if [ ! -f "./trf" ]; then
    echo "❌ 错误：找不到trf可执行文件"
    exit 1
fi

chmod +x ./trf

# 日志文件
LOG_FILE="trf_sqlite_fixed.log"
echo "TRF SQLite修复版启动日志 - $(date)" > $LOG_FILE

# 系统环境配置
export QT_QPA_PLATFORM="linuxfb"
export QT_QPA_FB_DISABLE_INPUT="0"

# 触摸设备配置
if [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
    echo "✅ 使用触摸设备: /dev/input/event1"
elif [ -c /dev/input/event0 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event0"
    echo "✅ 使用触摸设备: /dev/input/event0"
fi

# EGL配置
export QT_QPA_EGLFS_INTEGRATION="eglfs_viv"
export QT_QPA_EGLFS_DISABLE_INPUT="0"

# 字符编码
export LC_ALL="C.UTF-8"
export LANG="C.UTF-8"
export LC_CTYPE="C.UTF-8"

# 关键：SQLite驱动路径优化配置
export QT_PLUGIN_PATH="$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"

# 强制使用应用目录的插件，避免系统插件干扰
export QT_QPA_PLATFORM_PLUGINS="linuxfb"

# SQLite插件强制加载
export QT_SQL_DRIVERS="$APP_DIR/plugins/sqldrivers"
export LD_LIBRARY_PATH="$APP_DIR/plugins/sqldrivers:$LD_LIBRARY_PATH"

# 详细插件调试（临时启用）
export QT_DEBUG_PLUGINS=1
export QT_LOGGING_RULES="qt.sql.debug=true"

echo "环境变量配置:" >> $LOG_FILE
echo "QT_PLUGIN_PATH=$QT_PLUGIN_PATH" >> $LOG_FILE
echo "QT_SQL_DRIVERS=$QT_SQL_DRIVERS" >> $LOG_FILE
echo "LD_LIBRARY_PATH=$LD_LIBRARY_PATH" >> $LOG_FILE

# 验证SQLite插件
echo "SQLite插件验证:"
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
    echo "✅ SQLite插件文件存在"
    echo "📄 文件信息: $(file $APP_DIR/plugins/sqldrivers/libqsqlite.so)"
    echo "SQLite plugin found: $(file $APP_DIR/plugins/sqldrivers/libqsqlite.so)" >> $LOG_FILE
else
    echo "❌ SQLite插件文件缺失"
    echo "SQLite plugin missing" >> $LOG_FILE
fi

# 权限修复
chmod 666 /dev/input/event* 2>/dev/null || true

echo "启动TRF (SQLite修复版)..."
echo "预期：SQLite驱动应该能够正常加载"
echo "如果仍有问题，请等待完美静态版本..."

echo "启动时间: $(date)" >> $LOG_FILE

# 启动程序，捕获详细输出
./trf "$@" 2>&1 | tee -a $LOG_FILE

RESULT=$?
echo "退出代码: $RESULT" >> $LOG_FILE
echo "退出时间: $(date)" >> $LOG_FILE

if [ $RESULT -ne 0 ]; then
    echo ""
    echo "❌ 程序异常退出 (代码: $RESULT)"
    echo "这是临时修复版本。如果问题持续，请等待完美静态SQLite版本发布"
    echo "详细信息请查看: $LOG_FILE"
    echo ""
    echo "💡 建议："
    echo "1. 检查 $LOG_FILE 中的错误信息"
    echo "2. 运行 ldd ./trf 检查库依赖"
    echo "3. 等待GitHub Actions构建的完美版本"
else
    echo "✅ 程序正常退出"
    echo "如果SQLite功能正常，此临时方案成功！"
fi

exit $RESULT
EOF

chmod +x "$APP_DIR/run_sqlite_fixed.sh"

# 4. 验证修复结果
echo ""
echo "=== 修复结果验证 ==="

echo "📂 应用目录结构："
ls -la "$APP_DIR/" | grep -E "(trf|run_|plugins)"

echo ""
echo "🔌 SQLite插件状态："
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
    echo "✅ SQLite插件已配置: $(file $APP_DIR/plugins/sqldrivers/libqsqlite.so)"
else
    echo "❌ SQLite插件配置失败"
fi

echo ""
echo "🎯 平台插件状态："
if [ -d "$APP_DIR/plugins/platforms" ] && [ "$(ls -A $APP_DIR/plugins/platforms)" ]; then
    echo "✅ 平台插件已配置: $(ls $APP_DIR/plugins/platforms | head -3 | tr '\n' ' ')"
else
    echo "⚠ 平台插件可能缺失，但不影响SQLite功能"
fi

# 5. 使用说明
echo ""
echo "=== 使用说明 ==="
echo "✅ SQLite修复脚本配置完成！"
echo ""
echo "🚀 启动TRF："
echo "   cd $APP_DIR"
echo "   ./run_sqlite_fixed.sh"
echo ""
echo "📋 检查日志："
echo "   tail -f $APP_DIR/trf_sqlite_fixed.log"
echo ""
echo "💡 说明："
echo "   - 这是临时修复方案，解决当前SQLite问题"
echo "   - 完美的静态SQLite版本正在GitHub Actions中构建"
echo "   - 如果此方案有效，您将看到数据库正常工作"
echo "   - 新版本发布后，建议升级到完美版本以获得最佳体验"
echo ""
echo "🔧 如果仍有问题："
echo "   1. 检查trf_sqlite_fixed.log文件"
echo "   2. 确认系统架构: uname -m"
echo "   3. 检查Qt库: ldconfig -p | grep Qt5"
echo "   4. 等待完美版本发布（包含静态SQLite支持）"

echo ""
echo "✨ 修复完成！请尝试运行 ./run_sqlite_fixed.sh" 
#ifndef BASEPAGE_H
#define BASEPAGE_H

#include <QWidget>
#include <QVBoxLayout>
#include "commonheader.h"

class BasePage : public QWidget
{
    Q_OBJECT

public:
    explicit BasePage(QWidget *parent = nullptr);
    ~BasePage();
    CommonHeader* getHeader();
    void setContentWidget(QWidget *widget);
    QWidget* getContentWidget() const;

private:
    void setupUi();

private:
    CommonHeader *header;
    QWidget *contentArea;
};

#endif // BASEPAGE_H 
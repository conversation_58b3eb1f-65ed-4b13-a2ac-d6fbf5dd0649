#ifndef PATIENT_H
#define PATIENT_H

#include <QString>
#include <QDate>
#include <QDateTime>
#include <QDebug>

/**
 * 患者数据实体类
 * 包含患者的基本信息和验证方法
 */
class Patient
{
public:
    Patient();
    Patient(const QString& lastName, const QString& firstName, 
            const QDate& birthday, const QString& gender);
    Patient(int patientId, const QString& patientName, const QString& lastName, 
            const QString& firstName, const QDate& birthday, const QString& gender, 
            const QString& remarks = QString());
    
    // Getter methods
    int getPatientId() const { return m_patientId; }
    QString getPatientName() const { return m_patientName; }
    QString getLastName() const { return m_lastName; }
    QString getFirstName() const { return m_firstName; }
    QDate getBirthday() const { return m_birthday; }
    QString getGender() const { return m_gender; }
    QString getRemarks() const { return m_remarks; }
    QDateTime getCreatedAt() const { return m_createdAt; }
    QDateTime getUpdatedAt() const { return m_updatedAt; }
    
    // Setter methods
    void setPatientId(int patientId) { m_patientId = patientId; }
    void setPatientName(const QString& patientName) { m_patientName = patientName; }
    void setLastName(const QString& lastName) { m_lastName = lastName; updatePatientName(); }
    void setFirstName(const QString& firstName) { m_firstName = firstName; updatePatientName(); }
    void setBirthday(const QDate& birthday) { m_birthday = birthday; }
    void setGender(const QString& gender) { m_gender = gender; }
    void setRemarks(const QString& remarks) { m_remarks = remarks; }
    void setCreatedAt(const QDateTime& createdAt) { m_createdAt = createdAt; }
    void setUpdatedAt(const QDateTime& updatedAt) { m_updatedAt = updatedAt; }
    
    /**
     * 验证患者数据有效性
     * @return bool 数据是否有效
     */
    bool isValid() const;
    
    /**
     * 获取验证错误信息
     * @return QString 错误描述
     */
    QString getValidationError() const;
    
    /**
     * 计算患者年龄
     * @return int 年龄（岁）
     */
    int getAge() const;
    
    /**
     * 获取格式化的患者姓名
     * @return QString 格式化后的姓名
     */
    QString getFormattedName() const;
    
    /**
     * 判断两个患者对象是否相等
     */
    bool operator==(const Patient& other) const;
    bool operator!=(const Patient& other) const;
    
    /**
     * 调试输出
     */
    QString toString() const;

private:
    int m_patientId;
    QString m_patientName;      // 组合的患者姓名
    QString m_lastName;         // 姓氏
    QString m_firstName;        // 名字
    QDate m_birthday;          // 生日
    QString m_gender;          // 性别 (Male/Female)
    QString m_remarks;         // 备注
    QDateTime m_createdAt;     // 创建时间
    QDateTime m_updatedAt;     // 更新时间
    
    /**
     * 更新组合的患者姓名
     */
    void updatePatientName();
    
    /**
     * 验证性别有效性
     */
    bool isValidGender(const QString& gender) const;
};

// 调试输出支持
QDebug operator<<(QDebug debug, const Patient& patient);

#endif // PATIENT_H 
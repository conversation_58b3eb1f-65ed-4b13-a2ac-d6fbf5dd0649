#!/bin/bash
# TRF 完整SQLite诊断和修复脚本
# 基于system.txt分析的完整解决方案

echo "=== TRF 完整SQLite诊断 ==="
echo "日期: $(date)"
echo "系统: $(uname -a)"

# 检查当前目录
if [ ! -f "./trf" ]; then
  echo "❌ 请在TRF程序目录中运行此脚本"
  exit 1
fi

APP_DIR="$(pwd)"
echo "✓ TRF程序目录: $APP_DIR"

echo ""
echo "=== 系统环境分析 (基于system.txt) ==="

# 检查系统Qt库
echo "1. 系统Qt库检查:"
if [ -f "/usr/lib/libQt5Sql.so.5.6.2" ]; then
  echo "  ✓ 发现Qt5Sql核心库: /usr/lib/libQt5Sql.so.5.6.2"
else
  echo "  ⚠ 未找到Qt5Sql核心库"
fi

# 检查系统SQLite插件目录
echo ""
echo "2. 系统SQLite插件目录检查:"
for plugin_dir in "/usr/lib/qt5/plugins/sqldrivers" "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers"; do
  if [ -d "$plugin_dir" ]; then
    echo "  ✓ 发现插件目录: $plugin_dir"
    if [ -f "$plugin_dir/libqsqlite.so" ]; then
      echo "    ✓ 包含SQLite插件"
    else
      echo "    ❌ 不包含SQLite插件"
    fi
  else
    echo "  ❌ 插件目录不存在: $plugin_dir"
  fi
done

echo ""
echo "=== 应用SQLite插件检查 ==="

# 检查应用插件
echo "3. 应用SQLite插件:"
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
  echo "  ✓ 发现应用SQLite插件"
  echo "  文件信息:"
  ls -la "$APP_DIR/plugins/sqldrivers/libqsqlite.so"
  if command -v file >/dev/null 2>&1; then
    file "$APP_DIR/plugins/sqldrivers/libqsqlite.so"
  fi
  if command -v ldd >/dev/null 2>&1; then
    echo "  依赖检查:"
    ldd "$APP_DIR/plugins/sqldrivers/libqsqlite.so" 2>/dev/null | grep -E "(Qt5|sqlite)" || echo "  无明显依赖问题"
  fi
else
  echo "  ❌ 应用SQLite插件缺失"
fi

echo ""
echo "=== 环境配置测试 ==="

# 测试不同的环境配置
echo "4. 环境配置测试:"

echo ""
echo "  测试A: 仅应用插件模式"
export QT_PLUGIN_PATH="$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"
echo "    QT_PLUGIN_PATH=$QT_PLUGIN_PATH"
echo "    QT_QPA_PLATFORM_PLUGIN_PATH=$QT_QPA_PLATFORM_PLUGIN_PATH"
echo "    测试运行 (10秒):"
timeout 10s ./trf 2>&1 | grep -E "(SQLite|QSQLITE|available drivers|driver not loaded)" | head -3

echo ""
echo "  测试B: 混合插件模式 (如果有系统插件)"
if [ -d "/usr/lib/qt5/plugins" ]; then
  export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins"
  echo "    QT_PLUGIN_PATH=$QT_PLUGIN_PATH"
  echo "    测试运行 (10秒):"
  timeout 10s ./trf 2>&1 | grep -E "(SQLite|QSQLITE|available drivers|driver not loaded)" | head -3
else
  echo "    跳过 - 无系统插件目录"
fi

echo ""
echo "=== 问题诊断和建议 ==="

# 基于检查结果给出建议
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
  echo "✓ 发现应用SQLite插件"
  echo ""
  echo "推荐配置:"
  echo "export QT_PLUGIN_PATH=\"$APP_DIR/plugins\""
  echo "export QT_QPA_PLATFORM_PLUGIN_PATH=\"$APP_DIR/plugins/platforms\""
  echo "./trf"
  echo ""
  echo "如果仍然出现 'QSQLITE driver not loaded' 错误："
  echo "1. 检查插件是否为正确的ARM架构"
  echo "2. 检查插件的Qt版本兼容性"
  echo "3. 查看完整的错误日志: ./trf > debug.log 2>&1"
else
  echo "❌ 缺失SQLite插件"
  echo ""
  echo "需要获取新版本的TRF，包含交叉编译的SQLite插件"
fi

echo ""
echo "=== 快速启动命令 ==="
echo "# 复制并运行以下命令："
echo "export QT_PLUGIN_PATH=\"$APP_DIR/plugins\""
echo "export QT_QPA_PLATFORM_PLUGIN_PATH=\"$APP_DIR/plugins/platforms\""
echo "./trf"

echo ""
echo "=== 诊断完成 ===" 
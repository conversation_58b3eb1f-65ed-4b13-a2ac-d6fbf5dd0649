#!/bin/bash

# TRF ARM兼容构建脚本 - 直接解决资源和符号版本问题
# 解决关键问题：qt_resourceFeatureZlib符号版本不匹配

echo "🔧 TRF ARM兼容构建脚本启动"
echo "================================"
echo "目标: 解决Qt资源系统和符号版本兼容性问题"
echo "日期: $(date)"

# 1. 清理旧的构建文件
echo "🧹 清理旧构建文件..."
make clean 2>/dev/null || true
rm -f trf trf-arm-compatible *.o moc_* ui_* qrc_*

# 2. 显式生成资源文件
echo "📦 生成资源文件..."
if ! rcc -binary resources.qrc -o resources.rcc; then
    echo "❌ 资源文件生成失败"
    exit 1
fi

# 3. 验证资源文件
if [ ! -f resources.rcc ]; then
    echo "❌ 资源文件不存在"
    exit 1
fi

echo "✅ 资源文件生成成功: $(ls -lh resources.rcc)"

# 4. 使用兼容性qmake配置
echo "⚙️  配置qmake兼容性构建..."
qmake trf.pro \
    CONFIG+=release \
    CONFIG+=force_debug_info \
    DEFINES+=QT_DISABLE_DEPRECATED_BEFORE=0x050600 \
    DEFINES+=TARGET_SYSTEM_MYD_Y6ULL14X14 \
    DEFINES+=QT_RESOURCE_COMPATIBILITY_FIX \
    QMAKE_CXXFLAGS+="-DQT_MESSAGELOGCONTEXT" \
    QMAKE_CXXFLAGS+="-fPIC" \
    QMAKE_LFLAGS+="-Wl,--hash-style=gnu"

if [ $? -ne 0 ]; then
    echo "❌ qmake配置失败"
    exit 1
fi

# 5. 编译
echo "🔨 开始编译..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 6. 创建ARM兼容版本
echo "📱 创建ARM兼容版本..."
cp trf trf-arm-compatible
strip --strip-unneeded trf-arm-compatible

# 7. 验证构建结果
echo "🔍 验证构建结果..."
echo "文件信息:"
file trf-arm-compatible
echo "文件大小:"
ls -lh trf-arm-compatible

# 8. 检查依赖库
if command -v ldd >/dev/null 2>&1; then
    echo "动态库依赖:"
    ldd trf-arm-compatible | head -10
fi

# 9. 生成部署包
echo "📦 生成部署包..."
mkdir -p TRF-ARM-Compatible
cp trf-arm-compatible TRF-ARM-Compatible/trf
cp -r data TRF-ARM-Compatible/ 2>/dev/null || mkdir -p TRF-ARM-Compatible/data

# 创建运行脚本
cat > TRF-ARM-Compatible/run.sh << 'EOF'
#!/bin/bash

# TRF ARM兼容启动脚本
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DISABLE_INPUT=0

# 检测触摸设备
if [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
elif [ -c /dev/input/event0 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event0"
fi

# 设置字符编码
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

# 启动应用
echo "🚀 启动TRF (ARM兼容版本)..."
./trf
EOF

chmod +x TRF-ARM-Compatible/run.sh

echo "✅ ARM兼容构建完成!"
echo "📁 部署目录: TRF-ARM-Compatible/"
echo "🚀 运行命令: cd TRF-ARM-Compatible && ./run.sh" 
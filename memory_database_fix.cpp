// TRF 内存数据库解决方案
// 解决SQLite插件加载失败问题

#include "DatabaseConnection.h"

// 在 DatabaseConnection.cpp 的 getConnection() 方法中添加以下修改：

QSqlDatabase DatabaseConnection::getConnection()
{
    if (!m_database.isValid()) {
        // 检查可用的SQL驱动
        QStringList availableDrivers = QSqlDatabase::drivers();
        qDebug() << "Available SQL Drivers:" << availableDrivers;
        
        // === 新增：内存数据库解决方案 ===
        if (availableDrivers.contains("QSQLITE")) {
            // 尝试使用内存数据库，绕过文件系统和插件加载问题
            m_database = QSqlDatabase::addDatabase("QSQLITE", CONNECTION_NAME);
            
            // 关键修改：使用内存数据库
            m_database.setDatabaseName(":memory:");
            
            qDebug() << "尝试使用内存SQLite数据库 (绕过插件问题)";
            
            if (m_database.open()) {
                qDebug() << "✅ 内存数据库连接成功！";
                qDebug() << "注意: 数据将在程序重启后丢失";
                return m_database;
            } else {
                qWarning() << "内存数据库连接失败:" << m_database.lastError().text();
                m_lastError = m_database.lastError().text();
            }
        }
        
        // === 备用方案：完全禁用数据库 ===
        if (!m_database.isValid() || !m_database.isOpen()) {
            qDebug() << "所有数据库方案失败，启用无数据库模式";
            qDebug() << "程序将以无持久化存储模式运行";
            
            // 返回一个无效的数据库，让调用者处理
            m_lastError = "Database disabled - running in memory-only mode";
            return QSqlDatabase(); // 返回无效数据库
        }
    }
    
    return m_database;
}

// === 应用层修改建议 ===

// 1. 在所有数据库操作前添加检查：
bool checkDatabaseAvailable() {
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        qDebug() << "数据库不可用，使用内存存储";
        return false;
    }
    return true;
}

// 2. 为Patient和TestResult类添加内存存储备用方案：
class MemoryPatientStorage {
private:
    static QList<Patient> patients;
    static int nextId;
    
public:
    static bool savePatient(const Patient& patient) {
        // 内存存储实现
        patients.append(patient);
        return true;
    }
    
    static QList<Patient> getAllPatients() {
        return patients;
    }
    
    static void clearAll() {
        patients.clear();
        nextId = 1;
    }
};

// 3. 在main.cpp中添加启动提示：
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    // 尝试初始化数据库
    if (!DatabaseConnection::initializeDatabase()) {
        qDebug() << "=== 重要提示 ===";
        qDebug() << "数据库初始化失败，程序将以内存模式运行";
        qDebug() << "数据不会永久保存，程序重启后数据丢失";
        qDebug() << "这是临时解决方案，功能正常";
        qDebug() << "================";
    }
    
    return app.exec();
} 
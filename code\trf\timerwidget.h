#ifndef TIMERWIDGET_H
#define TIMERWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QTimer>
#include <QDateTime>
#include <QMouseEvent>

class TimerWidget : public QWidget
{
    Q_OBJECT

public:
    // Timer state enumeration
    enum TimerState {
        STAGE_0 = 0,    // Waiting state, no text display
        STAGE_1 = 1,    // Countdown "Next Sample 'ss'"
        STAGE_2 = 2,    // Prompt "80ul vol.\nadded"  
        STAGE_3 = 3,    // Incubation countdown "Incubation\n-mm:ss"
        STAGE_4 = 4,    // Reading wait "Read\n-ss"
        STAGE_5 = 5     // Completed state "--:--" or "+mm:ss"
    };

    explicit TimerWidget(QWidget *parent = nullptr);
    ~TimerWidget();

    // State management interface
    void setTimerState(TimerState state);
    TimerState getCurrentState() const { return m_currentState; }
    
    // Countdown control
    void startStage1Countdown(int seconds = 30);        // Stage 1 countdown
    void startStage3Incubation(int minutes = 15);       // Stage 3 incubation countdown
    void startStage4Reading();                          // Stage 4 reading timer
    void stopAllTimers();
    
    // State queries
    bool isStage1Complete() const;
    bool isStage3Complete() const;
    bool isClickable() const;
    
    // Error time calculation
    void setStage4StartTime(const QDateTime& time) { m_stage4StartTime = time; }
    QString calculateTimeDifference(const QDateTime& clickTime) const;

signals:
    void stageChanged(TimerState newState);
    void stage2Clicked();                               // Stage 2 clicked
    void stage4Clicked(const QString& timeDiff);        // Stage 4 clicked (with time difference)
    void incubationCompleted();                         // Incubation completed
    void readingCompleted(const QString& result);       // Reading completed

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;

private slots:
    void onCountdownTimeout();
    void onReadingTimeout();

private:
    // UI components
    QLabel *m_textLabel;
    
    // Timer components
    QTimer *m_countdownTimer;
    QTimer *m_readingTimer;
    
    // State data
    TimerState m_currentState;
    int m_remainingSeconds;                             // Remaining seconds
    QDateTime m_stage4StartTime;                        // Stage 4 start time
    QDateTime m_incubationStartTime;                    // Incubation start time
    
    // Threshold configuration
    static const int INCUBATION_ERROR_THRESHOLD = 60;   // Incubation error threshold (seconds)
    static const int READING_SIMULATION_TIME = 10;      // Reading simulation time (seconds)
    
    // Image resource paths
    static const char* STAGE_BACKGROUNDS[];
    
    // Helper methods
    void setupUI();
    void updateDisplay();
    void updateBackground();
    void updateTextStyle();
    QString formatTime(int totalSeconds) const;
    QString formatTimeMinSec(int totalSeconds) const;
    void simulateReading();
};

#endif // TIMERWIDGET_H 
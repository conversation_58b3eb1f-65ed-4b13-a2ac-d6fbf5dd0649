#ifndef PATIENTDAO_H
#define PATIENTDAO_H

#include <QObject>
#include <QList>
#include <QSqlQuery>
#include <QSqlError>
#include "../entities/Patient.h"

/**
 * 患者数据访问对象(DAO)
 * 提供患者数据的数据库CRUD操作
 */
class PatientDao : public QObject
{
    Q_OBJECT

public:
    explicit PatientDao(QObject *parent = nullptr);
    
    /**
     * 创建新患者记录
     * @param patient 患者对象
     * @return int 新创建的患者ID，失败返回-1
     */
    int create(const Patient& patient);
    
    /**
     * 根据ID查找患者
     * @param patientId 患者ID
     * @return Patient 患者对象，找不到返回无效对象
     */
    Patient findById(int patientId);
    
    /**
     * 根据患者姓名查找
     * @param patientName 患者姓名（模糊查询）
     * @return QList<Patient> 匹配的患者列表
     */
    QList<Patient> findByName(const QString& patientName);
    
    /**
     * 查找所有患者
     * @param limit 限制返回数量，默认-1表示无限制
     * @param offset 偏移量，默认0
     * @return QList<Patient> 患者列表
     */
    QList<Patient> findAll(int limit = -1, int offset = 0);
    
    /**
     * 更新患者信息
     * @param patient 患者对象（必须包含有效的patientId）
     * @return bool 更新是否成功
     */
    bool update(const Patient& patient);
    
    /**
     * 删除患者记录
     * @param patientId 患者ID
     * @return bool 删除是否成功
     */
    bool remove(int patientId);
    
    /**
     * 检查患者是否存在
     * @param patientId 患者ID
     * @return bool 是否存在
     */
    bool exists(int patientId);
    
    /**
     * 获取患者总数
     * @return int 患者总数
     */
    int count();
    
    /**
     * 根据关键词搜索患者
     * @param keyword 搜索关键词（姓名、备注等）
     * @param limit 限制返回数量
     * @return QList<Patient> 匹配的患者列表
     */
    QList<Patient> search(const QString& keyword, int limit = 50);
    
    /**
     * 检查患者姓名是否已存在
     * @param lastName 姓氏
     * @param firstName 名字
     * @param excludePatientId 排除的患者ID（用于更新时检查）
     * @return bool 是否已存在
     */
    bool isNameExists(const QString& lastName, const QString& firstName, int excludePatientId = -1);
    
    /**
     * 获取最近创建的患者
     * @param limit 限制返回数量
     * @return QList<Patient> 最近创建的患者列表
     */
    QList<Patient> getRecentPatients(int limit = 10);
    
    /**
     * 删除所有患者（谨慎使用）
     * @return bool 删除是否成功
     */
    bool deleteAllPatients();
    
    /**
     * 获取最后的错误信息
     * @return QString 错误描述
     */
    QString getLastError() const;

private:
    QString m_lastError;
    
    /**
     * 从查询结果创建Patient对象
     * @param query SQL查询对象
     * @return Patient 患者对象
     */
    Patient createPatientFromQuery(const QSqlQuery& query);
    
    /**
     * 验证患者数据
     * @param patient 患者对象
     * @return bool 数据是否有效
     */
    bool validatePatient(const Patient& patient);
    
    /**
     * 设置错误信息
     * @param error 错误描述
     */
    void setError(const QString& error);
    
    /**
     * 准备查询语句并绑定参数
     * @param query SQL查询对象
     * @param patient 患者对象
     */
    void bindPatientParameters(QSqlQuery& query, const Patient& patient);
};

#endif // PATIENTDAO_H 
#include "datepickerdialog.h"
#include <QGridLayout>
#include <QDateTime>
#include <QDebug>

DatePickerDialog::DatePickerDialog(QWidget *parent)
    : QDialog(parent)
    , selectedDate(QDate::currentDate())
{
    setupUi();
    setSelectedDate(QDate::currentDate());
}

void DatePickerDialog::setupUi()
{
    setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint); // 移除标题栏
    setFixedSize(400, 300);
    setModal(true);
    
    // 设置深灰色背景
    setStyleSheet("QDialog { background-color: #333333; }"
                  "QLabel { color: white; font-size: 14pt; font-weight: bold; }"
                  "QListWidget { background-color: #444444; color: white; border: 1px solid #666666; font-size: 12pt; }"
                  "QListWidget::item { padding: 5px; }"
                  "QListWidget::item:selected { background-color: #666666; }"
                  "QPushButton { background-color: #555555; color: white; border: 1px solid #777777; "
                  "             padding: 8px 16px; font-size: 12pt; font-weight: bold; }"
                  "QPushButton:hover { background-color: #666666; }"
                  "QPushButton:pressed { background-color: #444444; }");
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(10);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    
    // 移除标题标签部分，直接创建选择列表
    QHBoxLayout *listLayout = new QHBoxLayout();
    
    yearList = new QListWidget();
    monthList = new QListWidget();
    dayList = new QListWidget();
    
    // 填充年份列表 (1900-2050) - 纯数字格式
    for (int year = 1900; year <= 2050; year++) {
        yearList->addItem(QString::number(year));
    }
    
    // 填充月份列表 - 使用两位数字格式
    for (int month = 1; month <= 12; month++) {
        monthList->addItem(QString("%1").arg(month, 2, 10, QChar('0')));
    }
    
    listLayout->addWidget(yearList);
    listLayout->addWidget(monthList);
    listLayout->addWidget(dayList);
    
    // 创建按钮布局 - 向右移动按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    okButton = new QPushButton("OK");
    cancelButton = new QPushButton("Cancel");
    
    buttonLayout->addStretch();
    buttonLayout->addStretch(); // 额外的stretch让按钮更靠右
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);
    
    // 添加到主布局
    mainLayout->addLayout(listLayout);
    mainLayout->addLayout(buttonLayout);
    
    // 连接信号槽
    connect(yearList, &QListWidget::itemClicked, this, &DatePickerDialog::onYearSelected);
    connect(monthList, &QListWidget::itemClicked, this, &DatePickerDialog::onMonthSelected);
    connect(dayList, &QListWidget::itemClicked, this, &DatePickerDialog::onDaySelected);
    connect(okButton, &QPushButton::clicked, this, &DatePickerDialog::onOkClicked);
    connect(cancelButton, &QPushButton::clicked, this, &DatePickerDialog::onCancelClicked);
}

void DatePickerDialog::updateDaysList()
{
    dayList->clear();
    
    int year = selectedDate.year();
    int month = selectedDate.month();
    int daysInMonth = QDate(year, month, 1).daysInMonth();
    
    // 使用两位数字格式显示日期
    for (int day = 1; day <= daysInMonth; day++) {
        dayList->addItem(QString("%1").arg(day, 2, 10, QChar('0')));
    }
    
    // 选中当前日期
    if (selectedDate.day() <= daysInMonth) {
        dayList->setCurrentRow(selectedDate.day() - 1);
    }
}

void DatePickerDialog::onYearSelected()
{
    if (yearList->currentItem()) {
        int year = yearList->currentItem()->text().toInt();
        selectedDate.setDate(year, selectedDate.month(), selectedDate.day());
        updateDaysList();
    }
}

void DatePickerDialog::onMonthSelected()
{
    if (monthList->currentItem()) {
        int month = monthList->currentItem()->text().toInt(); // 直接解析数字
        selectedDate.setDate(selectedDate.year(), month, selectedDate.day());
        updateDaysList();
    }
}

void DatePickerDialog::onDaySelected()
{
    if (dayList->currentItem()) {
        int day = dayList->currentItem()->text().toInt(); // 直接解析数字
        selectedDate.setDate(selectedDate.year(), selectedDate.month(), day);
    }
}

void DatePickerDialog::onOkClicked()
{
    accept();
}

void DatePickerDialog::onCancelClicked()
{
    reject();
}

QDate DatePickerDialog::getSelectedDate() const
{
    return selectedDate;
}

void DatePickerDialog::setSelectedDate(const QDate &date)
{
    selectedDate = date;
    
    // 设置年份选择
    for (int i = 0; i < yearList->count(); i++) {
        if (yearList->item(i)->text().toInt() == date.year()) {
            yearList->setCurrentRow(i);
            break;
        }
    }
    
    // 设置月份选择 - 根据两位数字格式查找
    QString monthText = QString("%1").arg(date.month(), 2, 10, QChar('0'));
    for (int i = 0; i < monthList->count(); i++) {
        if (monthList->item(i)->text() == monthText) {
            monthList->setCurrentRow(i);
            break;
        }
    }
    
    // 更新并设置日期选择
    updateDaysList();
} 
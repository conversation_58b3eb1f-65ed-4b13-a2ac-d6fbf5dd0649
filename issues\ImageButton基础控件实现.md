# CommonHeader 按钮实现 - 最终版本

## 任务完成总结
用户要求实现 CommonHeader 第二排按钮的图片背景和文字显示功能，最终采用直接实现方案。

## 最终实现方案
**直接在 CommonHeader 中实现分层结构**，不使用封装的基础控件。

### 技术架构
- **QWidget 容器**：外层容器，设置固定尺寸和策略
- **QLabel 背景层**：显示背景图片，设置 `scaledContents=true`
- **QPushButton 前景层**：显示文字和处理交互，完全透明背景

### 关键实现代码
```cpp
// 创建外层 QWidget 容器
QWidget* container = new QWidget();
container->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
container->setMinimumSize(buttonW[i], 70);
container->setMaximumSize(buttonW[i], 70);

// 创建背景 QLabel  
QLabel* bgLabel = new QLabel(container);
bgLabel->setGeometry(0, 0, buttonW[i], 70);
bgLabel->setScaledContents(true);
QPixmap pixmap(imagePath);
bgLabel->setPixmap(pixmap);

// 创建前景 QPushButton
QPushButton* button = new QPushButton(container);
button->setGeometry(0, 0, buttonW[i], 70);
button->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
button->setMinimumSize(buttonW[i], 70);
button->setMaximumSize(buttonW[i], 70);
button->setText(buttonText);
```

### 样式设置
```css
QPushButton {
    background-color: transparent;
    border: none;
    color: white;
    font: 12pt "Arial";
    font-weight: bold;
    text-align: left;
    padding-left: 10px;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 50);
}
QPushButton:pressed {
    background-color: rgba(0, 0, 0, 50);
}
```

## 功能特性
✅ **背景图片显示**：自动拉伸填满按钮区域  
✅ **按钮文字显示**：白色文字，左对齐，10px边距  
✅ **状态切换**：激活状态为灰色文字+白色背景图，非激活为白色文字+红色背景图  
✅ **点击响应**：完整的事件处理和信号发射  
✅ **悬停效果**：半透明白色覆盖层  
✅ **按压效果**：半透明黑色覆盖层  

## 布局规格
- **按钮数量**：6个
- **总宽度**：1024px
- **按钮坐标**：{0, 171, 341, 512, 682, 853}
- **按钮宽度**：{171, 170, 171, 170, 171, 171}
- **按钮高度**：70px
- **按钮标签**：{"Auto sample", "STAT sample", "Fast mode", "QC module", "Database", "Settings"}

## 图片资源
- **非激活状态**：red_*.png（红色背景图）
- **激活状态**：white_*.png（白色背景图）
- **资源路径**：`:/images/common/`

## 用户反馈
- ✅ 图片显示正常
- ✅ 文字显示正常  
- ✅ 点击切换正常
- ✅ 已处理素材白边问题
- ✅ 最终效果满意

## 状态：已完成 ✅
直接实现方案完全满足需求，无需继续开发封装控件。 
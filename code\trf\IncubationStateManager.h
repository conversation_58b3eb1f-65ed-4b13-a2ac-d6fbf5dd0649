#ifndef INCUBATIONSTATEMANAGER_H
#define INCUBATIONSTATEMANAGER_H

#include <QObject>
#include <QTimer>

/**
 * 全局温育状态管理器
 * 用于管理Auto和STAT页面的温育状态，确保在温育期间不能添加新用户
 */
class IncubationStateManager : public QObject
{
    Q_OBJECT

public:
    static IncubationStateManager* getInstance();
    
    // 测试状态查询
    bool isIncubationActive() const { return m_isIncubationActive; }
    bool isAnyTestActive() const { return m_isAnyTestActive; }
    
    // 测试状态控制
    void startTest(const QString& projectType, int rowNumber);
    void stopTest();
    void startIncubation(const QString& projectType);
    void stopIncubation();
    
    // 获取温育信息
    QString getCurrentProject() const { return m_currentProject; }
    int getRemainingSeconds() const { return m_remainingSeconds; }

signals:
    void testStarted(const QString& projectType, int rowNumber);
    void testStopped();
    void incubationStarted(const QString& projectType);
    void incubationStopped();
    void incubationTimeUpdated(int remainingSeconds);

private:
    explicit IncubationStateManager(QObject *parent = nullptr);
    ~IncubationStateManager();
    
    // 禁用拷贝构造和赋值操作
    IncubationStateManager(const IncubationStateManager&) = delete;
    IncubationStateManager& operator=(const IncubationStateManager&) = delete;
    
    static IncubationStateManager* m_instance;
    
    bool m_isIncubationActive;
    bool m_isAnyTestActive;
    QString m_currentProject;
    int m_currentRowNumber;
    int m_remainingSeconds;
    QTimer* m_timer;
    
    int getIncubationTimeForProject(const QString& projectType) const;

private slots:
    void onTimerTimeout();
};

#endif // INCUBATIONSTATEMANAGER_H 
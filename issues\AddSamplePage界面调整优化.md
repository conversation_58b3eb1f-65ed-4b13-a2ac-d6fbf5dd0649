# AddSamplePage界面调整优化

## 任务背景
对AddSamplePage进行多项UI和功能调整，包括布局对齐、背景图片、功能逻辑等。

## 实施计划

### 第一阶段：UI布局和样式调整
1. 第一行header文字右对齐
2. 第二行labels背景图实现  
3. pushButton_add_sample实现

### 第二阶段：功能组布局对齐
4. 按钮大小统一和对齐
5. lineEdit宽度对齐调整

### 第三阶段：功能逻辑实现
6. Gender下拉框设置
7. 功能组交互逻辑实现

## 执行状态
- [x] 任务记录创建
- [x] 第一阶段：UI布局和样式调整
  - [x] 第一行header文字右对齐
  - [x] 第二行labels背景图实现（label_patient_id、label_parameter、label_add_id）✅ 图片正确显示
  - [x] pushButton_add_sample实现 ✅ 背景图片正确显示
  - [x] 其他第二行labels左对齐
- [x] 第二阶段：功能组布局对齐
  - [x] 所有功能组按钮大小统一（93x73px，与数字键盘按钮尺寸一致）
  - [x] 按钮垂直位置精确对齐（170px、243px、316px、389px）
  - [x] lineEdit宽度根据各组按钮列数调整，高度统一为39px
  - [x] parameter组第二列按钮位置微调（x坐标从540调整为536）
- [x] 第三阶段：功能逻辑实现
  - [x] Gender下拉框设置（Male/Female，默认Male）
  - [x] Pre-Dil组交互逻辑（Yes/No按钮点击显示到lineEdit_yes_or_no）
  - [x] Sample组交互逻辑（Blood/Serum/Plasma/Capillary blood按钮点击显示到lineEdit_sample）
  - [x] Parameter组交互逻辑（除Other外的按钮点击显示到lineEdit_parameter）
  - [x] Add_ID组数字键盘逻辑（0-9追加数字，Exit删除最后一位）
  - [x] 特殊处理：换行符文本的正确显示

## 技术要点
- 使用widget容器+背景label+前景控件的层叠方式实现背景图
- 按钮大小以label_add_id组的数字按钮为标准
- lineEdit宽度根据对应按钮组的列数计算 
#ifndef FASTMODEPAGE_H
#define FASTMODEPAGE_H

#include <QWidget>
#include <QScrollArea>
#include <QLabel>
#include <QVBoxLayout>
#include <QScroller>
#include <QVector>
#include <QMap>
#include <QDateTime>
#include <QTimer>

// 前向声明
class TestResult;
class Patient;

namespace Ui {
class FastModePage;
}

// 行数据结构
struct SampleRowData {
    int rowNumber;
    int resultId;        // 数据库TestResult记录的ID，用于删除操作
    QString patient;
    QString parameter;
    QString result;
    QString datetime;
    QString timer;
    QString lot;
    QString cutoff;
    
    SampleRowData() : rowNumber(0), resultId(-1) {}
    SampleRowData(int num, const QString &pat, const QString &param, 
                  const QString &res, const QString &dt, const QString &tim,
                  const QString &l, const QString &co)
        : rowNumber(num), resultId(-1), patient(pat), parameter(param), result(res),
          datetime(dt), timer(tim), lot(l), cutoff(co) {}
    
    // 带resultId的构造函数，用于从数据库加载数据时使用
    SampleRowData(int num, int resId, const QString &pat, const QString &param, 
                  const QString &res, const QString &dt, const QString &tim,
                  const QString &l, const QString &co)
        : rowNumber(num), resultId(resId), patient(pat), parameter(param), result(res),
          datetime(dt), timer(tim), lot(l), cutoff(co) {}
};

// Timer状态数据结构
struct TimerStateData {
    enum Stage {
        STAGE_0 = 0,    // 等待状态
        STAGE_1 = 1,    // 倒计时"Next Sample 'ss'"
        STAGE_2 = 2,    // 提示"80ul vol.\nadded"
        STAGE_3 = 3,    // 孵育倒计时"Incubation\n-mm:ss"
        STAGE_4 = 4,    // 读数等待"Read\n-ss"
        STAGE_5 = 5,    // 完成状态"--:--"或"+mm:ss"
        STAGE_WAITING = 6  // 等待状态（当有其他样本在温育时）
    };

    Stage currentStage;
    int remainingSeconds;           // 剩余秒数
    QDateTime stage4StartTime;      // 阶段4开始时间
    QDateTime incubationStartTime;  // 孵育开始时间
    bool isClickable;              // 是否可点击
    QString projectType;           // 项目类型（CRP、PCT等）
    QString sampleType;            // 样本类型（Blood、Serum等）

    TimerStateData() : currentStage(STAGE_0), remainingSeconds(0), isClickable(false) {}
};

    // 动态行UI结构
    struct DynamicRowUI {
        QWidget *widget;
        QLabel *numberLabel;
        QLabel *patientLabel;
        QLabel *paramLabel;
        QLabel *resultLabel;
        QLabel *datetimeLabel;
        QLabel *timerBgLabel;    // Timer列背景图标签
        QLabel *timerLabel;      // Timer列文字标签
        QLabel *lotLabel;
        QLabel *cutoffLabel;
        
        DynamicRowUI() : widget(nullptr), numberLabel(nullptr), patientLabel(nullptr),
                         paramLabel(nullptr), resultLabel(nullptr), datetimeLabel(nullptr),
                         timerBgLabel(nullptr), timerLabel(nullptr), lotLabel(nullptr), cutoffLabel(nullptr) {}
    };

class FastModePage : public QWidget
{
    Q_OBJECT

public:
    // 列位置和宽度配置
    struct ColumnConfig {
        int x;
        int width;
    };

    explicit FastModePage(QWidget *parent = nullptr);
    ~FastModePage();

    // Methods for managing scrollable table
    void addSampleRow(int rowNumber, const QString &patient, const QString &parameter,
                     const QString &result, const QString &datetime,
                     const QString &timer, const QString &lot, const QString &cutoff);
    void addSampleRow(const SampleRowData &rowData);
    
    // 添加新样本的公共接口（自动选择合适的行号）
    void addNewSample(const QString &patient, const QString &parameter,
                     const QString &result, const QString &datetime,
                     const QString &timer, const QString &lot, const QString &cutoff);
    void addNewSampleToTop(const QString &patient, const QString &parameter,
                          const QString &result, const QString &datetime,
                          const QString &timer, const QString &lot, const QString &cutoff);
    void removeRow(int rowNumber);
    void clearAllRows();
    void updateScrollableContent();
    void generateDemoData(); // Generate random demo data
    
    // 懒加载相关方法
    void loadInitialDataFromDatabase();
    void convertTestResultToRowData(const TestResult& testResult, const Patient& patient, SampleRowData& rowData);
    bool isDataLoaded() const { return m_dataLoaded; }
    
    // 新增的动态行管理方法
    DynamicRowUI* createDataRow(const SampleRowData &rowData);
    void positionRow(DynamicRowUI *rowUI, int rowIndex);
    void applyRowStyles(DynamicRowUI *rowUI);
    void destroyRow(DynamicRowUI *rowUI);
    
    // 辅助方法
    QLabel* createColumnLabel(QWidget *parent, const ColumnConfig &config, const QString &text);
    void updateRowUI(DynamicRowUI *rowUI, const SampleRowData &rowData);
    int getRowDisplayIndex(int rowNumber);
    void repositionAllRows();
    void initializeWithTestData(); // 已废弃
    void initializeEmptyDisplay(); // 初始化空显示状态
    void ensureMinimumRows(); // 确保至少显示5行
    int findFirstEmptyRowOrGetNext(); // 找到第一个空行或获取下一个行号
    int getNextRowNumber(); // 新方法：获取下一个行号用于新数据插入到最上面
    
    // 数据清空后刷新方法
    void refreshAfterDataClear();
    void clearFastModeData();                     // 清空FastMode数据并重置状态
    
    // Timer相关方法（新设计）
    void startTestForRow(int rowNumber);              // 开始测试流程
    void updateTimerUI(int rowNumber);                // 更新Timer列的UI显示
    void advanceTimerStage(int rowNumber);            // 推进Timer到下一阶段
    void handleTimerClick(int rowNumber);             // 处理Timer点击事件
    void startNextWaitingTest();                      // 启动下一个等待的测试
    
    // Timer辅助方法
    bool hasTestsInPreIncubationStages() const;       // 检查是否有阶段1-2的测试
    int getNextWaitingRow() const;                    // 获取下一个等待的行号
    void generateAndUpdateResult(int rowNumber);      // 生成并更新测试结果
    void cleanupInvalidTimerStates();                // 清理无效的Timer状态

    // 项目配置方法
    int getIncubationTimeForProject(const QString& projectType) const;  // 获取项目温育时间
    bool shouldSkipStages01ForProject(const QString& projectType, const QString& sampleType) const;  // 是否跳过阶段0-1
    bool isProjectSampleMatch(const QString& projectType, const QString& sampleType) const;  // 项目样本匹配检查
    bool hasCRPInIncubation() const;  // 检查是否有CRP项目在温育阶段
    void convertStage2ToWaiting(int excludeRowNumber);  // 将其他阶段2样本转为Waiting状态
    void restoreWaitingToStage2();  // 恢复Waiting状态的样本到阶段2

    // 固定结果行管理
    void createFixedResultRow();                            // 创建固定的第5行结果显示
    void updateFixedResultRow();                            // 更新固定结果行显示
    void updateLatestResult(int rowNumber);                 // 更新最新完成的结果
    void deleteSampleRow(int rowNumber);                    // 删除指定的样本行
    void applyFixedResultRowStyles();                       // 应用固定结果行样式
    void updateRowNumbers();                                // 更新行号显示
    void updateFixedResultRowPosition();                    // 更新固定结果行位置
    void ensureFixedResultRowVisible();                     // 确保第五行始终可见
    void createBackgroundMask();                            // 创建背景遮罩
    void updateBackgroundMaskPosition();                    // 更新背景遮罩位置
    void moveCompletedResultToFixedRow(int rowNumber);      // 将完成的结果移动到固定行并删除原行
    
    // 数据访问方法
    int getRowCount() const { return m_rowData.size(); }
    const SampleRowData* getRowData(int rowNumber) const;
    bool hasRow(int rowNumber) const { return m_rowData.contains(rowNumber); }
    
    // 数据库删除方法
    bool deleteTestResultByRowData(const SampleRowData& rowData);  // 通过行数据查找并删除对应的测试结果记录
    
    // 测试方法
    void loadHundredRows(); // 加载100行测试数据（已废弃）

signals:
    void addSampleClicked();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void showEvent(QShowEvent *event) override; // 添加showEvent处理页面显示时的懒加载

private:
    Ui::FastModePage *ui;
    
    // 动态行数据管理
    QMap<int, SampleRowData> m_rowData;                      // 行号 -> 行数据
    QMap<int, DynamicRowUI*> m_dynamicRows;                 // 行号 -> UI组件
    QVector<int> m_visibleRows;                             // 当前可见的行号列表
    
    // Timer状态管理（新增）
    QMap<int, TimerStateData> m_timerStates;                // 行号 -> Timer状态
    QTimer *m_globalTimer;                                  // 全局1秒定时器
    
    // 懒加载标志
    bool m_dataLoaded;                                      // 数据是否已从数据库加载

    // 固定结果行相关
    DynamicRowUI* m_fixedResultRow;                         // 固定的第5行结果显示
    SampleRowData m_latestResult;                           // 最新完成的测试结果
    bool m_hasLatestResult;                                 // 是否有最新结果
    QWidget* m_backgroundMask;                              // 背景遮罩，覆盖固定面板下方区域
    
    // 布局配置常量
    static const int ROW_HEIGHT = 66;           // 行高
    static const int CONTENT_WIDTH = 1012;      // 内容宽度
    static const int HEADER_HEIGHT = 71;        // 表头高度(5+66)
    
    // 列位置和宽度配置
    static const ColumnConfig COLUMN_NUMBER;
    static const ColumnConfig COLUMN_PATIENT;
    static const ColumnConfig COLUMN_PARAMETER;
    static const ColumnConfig COLUMN_RESULT;
    static const ColumnConfig COLUMN_DATETIME;
    static const ColumnConfig COLUMN_TIMER;
    static const ColumnConfig COLUMN_LOT;
    static const ColumnConfig COLUMN_CUTOFF;
    
    // Helper methods
    void setupScrollableTable();
    void enableMouseScrolling();
    void enableTouchScrolling();
    void initializeColumnConfig();
    void calculateContentHeight();
    void updateVisibleRows();
    
public slots:
    void testScrolling(); // For debugging

private slots:
    void onGlobalTimerTick();                               // 全局定时器响应
};

#endif // FASTMODEPAGE_H 
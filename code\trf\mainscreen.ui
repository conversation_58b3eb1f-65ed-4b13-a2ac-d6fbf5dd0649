<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainScreen</class>
 <widget class="QWidget" name="MainScreen">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/* Main Panels */
QWidget#left_panel {
    border-right: 1px solid #d0d0d0;
    background-color: #f0f0f0; /* A neutral light grey */
}

QWidget#right_panel {
    background-color: #555555; /* Dark grey */
}

/* Left Panel Widgets */
#left_panel QPushButton {
    background-color: transparent;
    border: none;
    padding: 2px;
}

#left_panel QToolButton {
    background-color: transparent;
    border: none;
    padding: 1px;
}

/* Button Container */
QWidget#button_container {
    background-color: transparent;
}

#left_panel QPushButton:hover {
    background-color: rgba(255, 255, 255, 25);
    border-radius: 3px;
}

#left_panel QPushButton:pressed {
    background-color: rgba(255, 255, 255, 50);
    border-radius: 3px;
}

#left_panel QToolButton:hover {
    background-color: rgba(255, 255, 255, 25);
    border-radius: 3px;
}

#left_panel QToolButton:pressed {
    background-color: rgba(255, 255, 255, 50);
    border-radius: 3px;
}

#left_panel QLabel {
    color: white;
    font-weight: bold;
}

QLabel#label_time {
    font-size: 24pt;
    background-color: transparent;
    color: white;
    font-weight: bold;
}

QLabel#label_date {
    font-size: 14pt;
    background-color: transparent;
    color: white;
    font-weight: bold;
}

QLabel#label_user_mode {
    background-color: transparent;
    border: none;
    font-weight: bold;
    color: white;
    font-size: 12pt;
}


/* Right Panel Grid */
QGridLayout#right_grid_layout {
	margin: 20px 5px;
	spacing: 8px;
}

#right_panel QPushButton {
	background: transparent;
	border: none;
	border-radius: 8px;
	color: #8f8f91;
    font: 16pt "Segoe UI";
}

#right_panel QPushButton:hover {
	background-color: rgba(255, 255, 255, 50);
}
#right_panel QPushButton:pressed {
	background-color: rgba(0, 0, 0, 50);
}

/* Right Panel Buttons - Individual styles defined per button */
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="left_panel" native="true">
     <property name="minimumSize">
      <size>
       <width>200</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>200</width>
       <height>16777215</height>
      </size>
     </property>
     <widget class="QLabel" name="label">
      <property name="geometry">
       <rect>
        <x>-50</x>
        <y>-51</y>
        <width>291</width>
        <height>721</height>
       </rect>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="pixmap">
       <pixmap resource="resources.qrc">:/images/logo.png</pixmap>
      </property>
      <property name="scaledContents">
       <bool>true</bool>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignTop</set>
      </property>
      <property name="wordWrap">
       <bool>true</bool>
      </property>
      <property name="margin">
       <number>0</number>
      </property>
      <property name="indent">
       <number>0</number>
      </property>
      <property name="openExternalLinks">
       <bool>false</bool>
      </property>
      <property name="textInteractionFlags">
       <set>Qt::TextInteractionFlag::LinksAccessibleByMouse</set>
      </property>
     </widget>
     <widget class="QWidget" name="button_container" native="true">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>390</y>
        <width>150</width>
        <height>100</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_buttons">
       <property name="leftMargin">
        <number>5</number>
       </property>
       <property name="topMargin">
        <number>5</number>
       </property>
       <property name="rightMargin">
        <number>5</number>
       </property>
       <property name="bottomMargin">
        <number>5</number>
       </property>
       <property name="spacing">
        <number>8</number>
       </property>
       <item row="0" column="0">
        <widget class="QToolButton" name="pushButton">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="resources.qrc">
           <normaloff>:/images/icon_calibration.png</normaloff>:/images/icon_calibration.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QToolButton" name="pushButton_3">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="resources.qrc">
           <normaloff>:/images/icon_statistics.png</normaloff>:/images/icon_statistics.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QToolButton" name="pushButton_2">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="resources.qrc">
           <normaloff>:/images/icon_settings_main.png</normaloff>:/images/icon_settings_main.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLabel" name="label_user_mode">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>40</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>80</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string>Admin</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QLabel" name="label_time">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>520</y>
        <width>180</width>
        <height>40</height>
       </rect>
      </property>
      <property name="text">
       <string>12:34:56</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
     <widget class="QLabel" name="label_date">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>560</y>
        <width>180</width>
        <height>30</height>
       </rect>
      </property>
      <property name="text">
       <string>2025-07-14</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="right_panel" native="true">
     <layout class="QGridLayout" name="right_grid_layout">
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>8</number>
      </property>
      <item row="0" column="1" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_qc" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_qc">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/qc_module.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_qc_module">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>388</width>
           <height>191</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>388</width>
           <height>191</height>
          </size>
         </property>
         <property name="text">
          <string>QC Module</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="0" column="0" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_auto" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_auto">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/icon_settings.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_auto_sample">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="text">
          <string>Auto Sample</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="2" column="0" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_fast" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_fast">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/fast_mode.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_fast_mode">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="text">
          <string>Fast Mode</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="1" column="1" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_db" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_db">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/icon_qc.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_database">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="text">
          <string>Database</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="2" column="1" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_settings" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_settings">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/settings.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_settings">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="text">
          <string>Settings</string>
         </property>
        </widget>
       </widget>
      </item>
      <item row="1" column="0" alignment="Qt::AlignCenter">
       <widget class="QWidget" name="widget_stat" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>388</width>
          <height>191</height>
         </size>
        </property>
        <widget class="QLabel" name="bg_label_stat">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="pixmap">
          <pixmap resource="resources.qrc">:/images/icon_settings.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
        </widget>
        <widget class="QPushButton" name="button_stat_sample">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>388</width>
           <height>191</height>
          </rect>
         </property>
         <property name="text">
          <string>STAT Sample</string>
         </property>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>

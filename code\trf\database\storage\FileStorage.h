#ifndef FILESTORAGE_H
#define FILESTORAGE_H

#include <QObject>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QDateTime>
#include <QMutex>
#include <QDebug>
#include "../entities/Patient.h"
#include "../entities/TestResult.h"

/**
 * 文件存储管理类 - SQLite数据库的替代方案
 * 使用JSON文件存储数据，解决SQLite插件问题
 * 提供与数据库相同的接口和功能
 */
class FileStorage : public QObject
{
    Q_OBJECT

public:
    static FileStorage* getInstance();
    
    // 初始化存储系统
    bool initialize();
    
    // 患者管理
    bool savePatient(const Patient& patient);
    QList<Patient> getAllPatients();
    Patient getPatientById(int patientId);
    bool updatePatient(const Patient& patient);
    bool deletePatient(int patientId);
    
    // 测试结果管理
    bool saveTestResult(const TestResult& result);
    QList<TestResult> getAllTestResults();
    QList<TestResult> getTestResultsByPatientId(int patientId);
    QList<TestResult> getTestResultsByMode(const QString& testMode);
    bool deleteTestResult(int resultId);
    
    // 系统配置管理
    bool setConfig(const QString& key, const QString& value);
    QString getConfig(const QString& key, const QString& defaultValue = "");
    
    // 数据清理
    bool clearAllData();
    bool clearPatients();
    bool clearTestResults();
    
    // 数据统计
    int getPatientCount();
    int getTestResultCount();
    
    // 错误信息
    QString getLastError() const { return m_lastError; }

private:
    explicit FileStorage(QObject *parent = nullptr);
    ~FileStorage();
    
    // 禁用拷贝构造和赋值操作
    FileStorage(const FileStorage&) = delete;
    FileStorage& operator=(const FileStorage&) = delete;
    
    // 文件操作
    bool savePatientData();
    bool loadPatientData();
    bool saveTestResultData();
    bool loadTestResultData();
    bool saveConfigData();
    bool loadConfigData();
    
    // 路径管理
    QString getStorageDir();
    QString getPatientFilePath();
    QString getTestResultFilePath();
    QString getConfigFilePath();
    
    // 数据转换
    QJsonObject patientToJson(const Patient& patient);
    Patient patientFromJson(const QJsonObject& json);
    QJsonObject testResultToJson(const TestResult& result);
    TestResult testResultFromJson(const QJsonObject& json);
    
    // 文件安全操作
    bool writeJsonFile(const QString& filePath, const QJsonDocument& doc);
    QJsonDocument readJsonFile(const QString& filePath);
    
    // ID管理
    int getNextPatientId();
    int getNextTestResultId();
    
    static FileStorage* m_instance;
    static QMutex m_mutex;
    
    QList<Patient> m_patients;
    QList<TestResult> m_testResults;
    QMap<QString, QString> m_config;
    
    QString m_storageDir;
    QString m_lastError;
    
    // 数据缓存标记
    bool m_patientsLoaded;
    bool m_testResultsLoaded;
    bool m_configLoaded;
    
    // 数据修改标记（用于优化写入）
    bool m_patientsModified;
    bool m_testResultsModified;
    bool m_configModified;
};

#endif // FILESTORAGE_H 
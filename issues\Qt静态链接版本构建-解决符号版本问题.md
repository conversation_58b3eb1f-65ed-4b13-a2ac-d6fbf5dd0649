# TRF Qt静态链接版本构建 - 解决符号版本问题

## 问题分析

当前ARM二进制在myd-y6ull14x14系统运行时出现：
```
./trf: relocation error: ./trf: symbol qt_resourceFeatureZlib, version Qt_5 not defined in file libQt5Core.so.5 with link time reference
```

## system.txt关键信息分析

### 目标系统配置
- **系统**: Linux myd-y6ull14x14 4.1.15+ armv7l
- **架构**: armv7l (ARMv7 Little Endian)
- **ABI**: Linux 2.6.32 (硬浮点ABI)
- **总库数**: 140个库在缓存中

### Qt5库版本 (精确)
```
libQt5Core.so.5 → libQt5Core.so.5.6.2
libQt5Gui.so.5 → libQt5Gui.so.5.6.2  
libQt5Widgets.so.5 → libQt5Widgets.so.5.6.2
libQt5Sql.so.5 → libQt5Sql.so.5.6.2
libQt5Network.so.5 → libQt5Network.so.5.6.2
libQt5OpenGL.so.5 → libQt5OpenGL.so.5.6.2
libQt5PrintSupport.so.5 → libQt5PrintSupport.so.5.6.2
```

### 关键系统库
```
libc.so.6 (Linux 2.6.32 ABI)
libstdc++.so.6 → libstdc++.so.6.0.21
libgcc_s.so.1 
libpthread.so.0 (Linux 2.6.32)
libm.so.6 (Linux 2.6.32)
libdl.so.2 (Linux 2.6.32)
```

### 图形和输入库
```
libEGL.so.1 → libEGL.so.1.0.0
libGLESv2.so.2 → libGLESv2.so.2.0.0
libts-1.0.so.0 → libts-1.0.so.0.0.0 (TouchScreen)
libpng16.so.16 → libpng16.so.16.21.0
libjpeg.so.62 → libjpeg.so.62.1.0
```

### 缺失的工具
- `ldd`: 命令未找到
- `readelf`: 命令未找到
- `gcc`: 未安装

## 静态链接策略

### 第1层: Qt5核心库 (必须静态链接)
**原因**: 符号版本不匹配导致的核心问题
```
- Qt5Core (静态) - 解决qt_resourceFeatureZlib符号问题
- Qt5Gui (静态) - 避免GUI相关符号冲突
- Qt5Widgets (静态) - 保证Widget组件兼容性
- Qt5Sql (静态) - 内置SQLite，避免驱动依赖
```

### 第2层: 可选静态链接库
**TSlib**: 
- 目标系统有 `libts-1.0.so.0.0.0`
- **建议**: 动态链接，版本匹配

**图像处理库**:
- 目标系统有 `libpng16.so.16.21.0`, `libjpeg.so.62.1.0`
- **建议**: 静态链接PNG/JPEG支持，避免版本差异

**OpenGL ES**:
- 目标系统有 `libEGL.so.1.0.0`, `libGLESv2.so.2.0.0`
- **建议**: 动态链接，硬件相关

### 第3层: 系统库 (保持动态)
**原因**: 与内核ABI强相关，必须使用系统版本
```
- libc.so.6 (Linux 2.6.32 ABI)
- libpthread.so.0 
- libm.so.6
- libdl.so.2
- libstdc++.so.6.0.21 (C++运行时)
- libgcc_s.so.1 (GCC运行时)
```

## 构建配置

### qmake静态链接配置
```bash
CONFIG += staticlib static
DEFINES += QT_STATIC_BUILD
LIBS += -static-libgcc -static-libstdc++

# 静态链接Qt5核心库
QT += core gui widgets sql
QTPLUGIN += qsqlite

# 静态链接图像支持
QTPLUGIN += qpng qjpeg

# 保持动态的系统库
LIBS += -ldl -lpthread -lm
```

### Docker构建环境
```dockerfile
# 使用Ubuntu 16.04 armv7 匹配目标系统ABI
FROM arm32v7/ubuntu:16.04

# 安装Qt5.6.2静态库
RUN apt-get update && apt-get install -y \
    qt5-default \
    qt5-qmake \
    libqt5sql5-sqlite \
    qtbase5-dev \
    qtbase5-dev-tools \
    build-essential

# 编译静态版本Qt5
# 或使用预编译的静态Qt5库
```

### 验证步骤
1. **符号检查**: 使用nm验证Qt符号是否内置
2. **依赖检查**: 确认只依赖系统核心库
3. **ABI验证**: 确保与Linux 2.6.32 ABI兼容
4. **功能测试**: 在目标系统验证基本功能

## 预期结果

### 依赖库清单 (静态链接后)
```
libc.so.6 (Linux 2.6.32)
libstdc++.so.6.0.21  
libgcc_s.so.1
libpthread.so.0
libm.so.6
libdl.so.2
libEGL.so.1.0.0 (可选，如果使用OpenGL)
libGLESv2.so.2.0.0 (可选)
libts-1.0.so.0.0.0 (可选，触摸屏)
```

### 解决的问题
- ✅ qt_resourceFeatureZlib符号版本冲突
- ✅ SQLite驱动依赖问题
- ✅ Qt5库版本不匹配
- ✅ 图像格式支持不稳定

---
**状态**: 待实施
**优先级**: 高
**影响**: 解决根本的符号版本兼容性问题 
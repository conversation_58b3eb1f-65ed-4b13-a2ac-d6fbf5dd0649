{"count": 4, "patients": [{"birthday": "2025-07-27", "created_at": "2025-07-27T19:58:10", "first_name": "Patient", "gender": "Male", "last_name": "Unknown", "patient_id": 1, "patient_name": "Unknown, Patient", "remarks": "", "updated_at": "2025-07-27T20:55:08"}, {"birthday": "2025-07-27", "created_at": "2025-07-27T20:11:02", "first_name": "Patient", "gender": "Male", "last_name": "Unknown", "patient_id": 2, "patient_name": "Unknown, Patient", "remarks": "", "updated_at": "2025-07-27T20:55:08"}, {"birthday": "2025-07-27", "created_at": "2025-07-27T20:41:32", "first_name": "Patient", "gender": "Male", "last_name": "Unknown", "patient_id": 3, "patient_name": "Unknown, Patient", "remarks": "", "updated_at": "2025-07-27T20:55:08"}, {"birthday": "2025-07-27", "created_at": "2025-07-27T20:55:08", "first_name": "Patient", "gender": "Male", "last_name": "Unknown", "patient_id": 4, "patient_name": "Unknown, Patient", "remarks": "", "updated_at": "2025-07-27T20:55:08"}], "timestamp": "2025-07-27T20:55:08", "version": "1.0"}
#include "dilutionfactordialog.h"
#include <QDebug>
#include <QRegularExpression>

DilutionFactorDialog::DilutionFactorDialog(QWidget *parent)
    : QDialog(parent)
    , hasNegative(false)
    , hasDecimal(false)
{
    setupUi();
}

void DilutionFactorDialog::setupUi()
{
    setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint); // 无标题栏
    setFixedSize(350, 280);
    setModal(true);
    
    // 设置深灰色背景
    setStyleSheet("QDialog { background-color: #333333; }"
                  "QLineEdit { background-color: #DDDDDD; color: black; border: 1px solid #666666; "
                  "           font-size: 14pt; padding: 8px; }"
                  "QPushButton { background-color: #555555; color: white; border: 1px solid #777777; "
                  "             font-size: 12pt; font-weight: bold; }"
                  "QPushButton:hover { background-color: #666666; }"
                  "QPushButton:pressed { background-color: #444444; }");
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(10);
    mainLayout->setContentsMargins(15, 15, 15, 15);
    
    // 输入框
    inputEdit = new QLineEdit();
    inputEdit->setPlaceholderText("Dilution factor");
    inputEdit->setReadOnly(true); // 只能通过按钮输入
    inputEdit->setFixedHeight(40);
    
    // 按键布局
    QGridLayout *keypadLayout = createKeypad();
    
    mainLayout->addWidget(inputEdit);
    mainLayout->addLayout(keypadLayout);
}

QGridLayout* DilutionFactorDialog::createKeypad()
{
    QGridLayout *keypadLayout = new QGridLayout();
    keypadLayout->setSpacing(5);
    
    // 初始化按钮
    for (int i = 0; i < 10; i++) {
        numberButtons[i] = new QPushButton(QString::number(i));
        numberButtons[i]->setFixedSize(60, 40);
        connect(numberButtons[i], &QPushButton::clicked, this, &DilutionFactorDialog::onNumberClicked);
    }
    
    decimalButton = new QPushButton(".");
    decimalButton->setFixedSize(60, 40);
    connect(decimalButton, &QPushButton::clicked, this, &DilutionFactorDialog::onDecimalClicked);
    
    negativeButton = new QPushButton("-");
    negativeButton->setFixedSize(60, 40);
    connect(negativeButton, &QPushButton::clicked, this, &DilutionFactorDialog::onNegativeClicked);
    
    deleteButton = new QPushButton("Delete");
    deleteButton->setFixedSize(60, 40);
    connect(deleteButton, &QPushButton::clicked, this, &DilutionFactorDialog::onDeleteClicked);
    
    closeButton = new QPushButton("Close");
    closeButton->setFixedSize(60, 40);
    connect(closeButton, &QPushButton::clicked, this, &DilutionFactorDialog::onCloseClicked);
    
    okButton = new QPushButton("OK");
    okButton->setFixedSize(60, 80); // 两倍高度，占两行
    connect(okButton, &QPushButton::clicked, this, &DilutionFactorDialog::onOkClicked);
    
    // 4x4布局
    // 第一行：7, 8, 9, Close
    keypadLayout->addWidget(numberButtons[7], 0, 0);
    keypadLayout->addWidget(numberButtons[8], 0, 1);
    keypadLayout->addWidget(numberButtons[9], 0, 2);
    keypadLayout->addWidget(closeButton, 0, 3);
    
    // 第二行：4, 5, 6, Delete
    keypadLayout->addWidget(numberButtons[4], 1, 0);
    keypadLayout->addWidget(numberButtons[5], 1, 1);
    keypadLayout->addWidget(numberButtons[6], 1, 2);
    keypadLayout->addWidget(deleteButton, 1, 3);
    
    // 第三行：1, 2, 3, OK (第一半)
    keypadLayout->addWidget(numberButtons[1], 2, 0);
    keypadLayout->addWidget(numberButtons[2], 2, 1);
    keypadLayout->addWidget(numberButtons[3], 2, 2);
    keypadLayout->addWidget(okButton, 2, 3, 2, 1); // 跨两行
    
    // 第四行：0, -, .
    keypadLayout->addWidget(numberButtons[0], 3, 0);
    keypadLayout->addWidget(negativeButton, 3, 1);
    keypadLayout->addWidget(decimalButton, 3, 2);
    // OK按钮继续占用第四行第四列
    
    return keypadLayout;
}

void DilutionFactorDialog::onNumberClicked()
{
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    if (button) {
        QString currentText = inputEdit->text();
        inputEdit->setText(currentText + button->text());
    }
}

void DilutionFactorDialog::onDecimalClicked()
{
    QString currentText = inputEdit->text();
    // 只能添加一个小数点，且不能在开头添加
    if (!hasDecimal && !currentText.isEmpty()) {
        inputEdit->setText(currentText + ".");
        hasDecimal = true;
    }
}

void DilutionFactorDialog::onNegativeClicked()
{
    QString currentText = inputEdit->text();
    if (!hasNegative) {
        // 添加负号到最前面
        inputEdit->setText("-" + currentText);
        hasNegative = true;
    } else {
        // 移除负号
        if (currentText.startsWith("-")) {
            inputEdit->setText(currentText.mid(1));
            hasNegative = false;
        }
    }
}

void DilutionFactorDialog::onDeleteClicked()
{
    QString currentText = inputEdit->text();
    if (!currentText.isEmpty()) {
        QString lastChar = currentText.right(1);
        if (lastChar == ".") {
            hasDecimal = false;
        }
        currentText.chop(1);
        inputEdit->setText(currentText);
        
        // 检查是否还有负号
        hasNegative = currentText.startsWith("-");
    }
}

void DilutionFactorDialog::onCloseClicked()
{
    reject(); // 关闭对话框，不保存
}

void DilutionFactorDialog::onOkClicked()
{
    QString text = inputEdit->text();
    if (isValidNumber(text)) {
        dilutionFactor = text;
        accept();
    } else {
        showWarning();
    }
}

void DilutionFactorDialog::showWarning()
{
    QMessageBox warningBox(this);
    warningBox.setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    warningBox.setIcon(QMessageBox::Warning);
    warningBox.setWindowTitle("Warning");
    warningBox.setText("Input error, please check!");
    warningBox.setStandardButtons(QMessageBox::Ok);
    
    // 设置深灰色样式
    warningBox.setStyleSheet("QMessageBox { background-color: #333333; color: white; border: 2px solid #666666; }"
                            "QMessageBox QLabel { color: white; font-size: 12pt; padding: 10px; }"
                            "QMessageBox QPushButton { background-color: #555555; color: white; "
                            "                          border: 1px solid #777777; padding: 8px 16px; "
                            "                          font-size: 12pt; font-weight: bold; min-width: 60px; }"
                            "QMessageBox QPushButton:hover { background-color: #666666; }"
                            "QMessageBox QPushButton:pressed { background-color: #444444; }");
    
    warningBox.exec();
}

bool DilutionFactorDialog::isValidNumber(const QString &text)
{
    if (text.isEmpty()) {
        return false;
    }
    
    // 使用正则表达式验证数字格式
    QRegularExpression regex("^-?\\d+(\\.\\d+)?$");
    return regex.match(text).hasMatch();
}

QString DilutionFactorDialog::getDilutionFactor() const
{
    return dilutionFactor;
}

void DilutionFactorDialog::setDilutionFactor(const QString &factor)
{
    dilutionFactor = factor;
    inputEdit->setText(factor);
    hasNegative = factor.startsWith("-");
    hasDecimal = factor.contains(".");
} 
# FastMode 等待任务清理修复

## 问题描述 🐛
用户反馈：清空数据后，等待的任务似乎还在，有些逻辑没有清掉。

## 问题分析 🔍

### 根本原因
1. **Timer状态与实际数据不同步**：`m_timerStates` 中的状态可能引用已删除的行
2. **遗留等待逻辑**：Timer方法没有验证行是否真实存在
3. **幽灵状态处理不完整**：全局Timer更新时没有自动清理无效状态

### 影响范围
- `onGlobalTimerTick()`：继续处理已删除行的Timer状态
- `hasTestsInPreIncubationStages()`：可能返回错误的等待状态
- `getNextWaitingRow()`：可能返回已删除的行号
- `startNextWaitingTest()`：可能尝试启动不存在的测试

## 修复方案 ✅

### 1. 全局Timer更新防护
```cpp
void FastModePage::onGlobalTimerTick()
{
    // 首先清理无效的Timer状态
    cleanupInvalidTimerStates();
    
    // 更新所有活跃Timer的倒计时
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        
        // 验证行是否还存在
        if (!m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue; // 跳过不存在的行
        }
        
        // ... 继续正常的Timer逻辑
    }
}
```

### 2. 等待状态检查修复
```cpp
bool FastModePage::hasTestsInPreIncubationStages() const
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        
        // 只检查实际存在的行
        if (!m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue;
        }
        
        TimerStateData::Stage stage = it.value().currentStage;
        if (stage == TimerStateData::STAGE_1 || stage == TimerStateData::STAGE_2) {
            return true;
        }
    }
    return false;
}
```

### 3. 等待行查找修复
```cpp
int FastModePage::getNextWaitingRow() const
{
    QList<int> waitingRows;
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        
        // 只检查实际存在的行
        if (!m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue;
        }
        
        if (it.value().currentStage == TimerStateData::STAGE_0) {
            waitingRows.append(rowNumber);
        }
    }
    // ... 其余逻辑保持不变
}
```

### 4. 增强的数据清空
```cpp
void FastModePage::clearFastModeData()
{
    // 停止所有Timer
    if (m_globalTimer) {
        m_globalTimer->stop();
    }
    
    // 清空Timer状态（关键！）
    m_timerStates.clear();
    
    // 清空所有数据和UI
    clearAllRows();
    
    // 重新启动Timer
    if (m_globalTimer) {
        m_globalTimer->start(1000);
    }
}
```

## 修复效果 ✅

### 修复前问题
- ❌ 清空数据后Timer仍在处理不存在的行
- ❌ 等待任务逻辑可能引用已删除数据
- ❌ 全局Timer更新时可能出现错误
- ❌ 幽灵状态影响新测试的启动

### 修复后状态
- ✅ 清空数据后所有Timer状态完全清除
- ✅ 所有Timer方法都验证行存在性
- ✅ 全局Timer自动清理无效状态
- ✅ 等待任务逻辑完全重置
- ✅ 新测试可以正常启动和排队

## 验证测试 ✅

### 测试步骤
1. 添加多个测试至不同阶段
2. 调用 `clearFastModeData()` 清空
3. 验证所有Timer状态清空
4. 添加新测试验证正常工作

### 预期结果
- 清空后界面显示5个空行
- 无等待任务残留
- 新测试正常进入队列
- Timer系统完全重置

## 总结
通过在所有Timer相关方法中添加行存在性验证，并在全局Timer更新时自动清理无效状态，完全解决了数据清空后等待任务残留的问题。现在FastMode具有完整的生命周期管理，支持干净的数据清空和重置。 
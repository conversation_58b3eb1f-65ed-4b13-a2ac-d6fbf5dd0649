# Human TRF 检测系统

基于Qt C++开发的医疗检测设备管理系统，完全复刻PDF说明书中的界面设计，使用真实界面素材。

## 📋 项目概述

> **🌐 Development Language Policy**: This project follows **English-only development**. All code, comments, logs, and documentation are written in English to ensure embedded system compatibility and international collaboration. See [Development Guidelines](docs/Development_Guidelines.md) for details.

### ✅ 完全复刻PDF界面设计
- **医疗设备级别**: HumaFIA半自动时间分辨荧光免疫分析仪
- **专业界面**: 严格按照45页PDF技术文档中的界面截图进行复刻
- **界面素材**: 使用docs/目录下的真实界面素材图片
- **分辨率**: 1024x600像素，7寸触摸屏横屏显示

### 🎯 核心功能
- **三种采样模式**: Auto Sample、STAT Sample、Fast Mode
- **精确时间控制**: 孵化时间精确到秒(mm:ss格式)
- **质量控制**: 校准卡管理、质控材料、目标值设置
- **数据管理**: 10,000条测试结果存储、高级查询过滤
- **系统设置**: 完整的参数配置和网络连接

## 📁 项目结构



## 🎨 界面设计特色

### 专业医疗设备风格
- **Human品牌**: 红色标题栏 (#dc143c)
- **实时时钟**: 精确到秒的系统时间显示
- **模式切换**: 三种专业采样模式
- **状态指示**: 清晰的视觉反馈系统
- **触屏优化**: 适配7寸触摸屏操作

### 界面模块
- **登录系统**: 用户ID/密码认证
- **主界面**: 模式选择、状态监控
- **采样模式**: 自动/STAT/快速三种模式
- **质量控制**: 校准卡、质控材料管理
- **数据管理**: 测试结果、患者信息管理
- **系统设置**: 参数配置、网络连接

## 🛠️ 技术栈

### 核心技术
- **界面框架**: Qt Widgets 6.8.2
- **编程语言**: C++17
- **构建系统**: qmake
- **编译器**: MinGW-w64 13.1.0
- **架构模式**: MVC (Model-View-Controller)
- **资源管理**: Qt资源系统统一管理图片和样式

## 🚀 项目状态

### 已完成功能
### 待开发功能



- [ ] 项目结构重组
- [ ] 资源文件整理
- [ ] Human品牌界面复刻
- [ ] 三种检测模式界面
- [ ] 标题栏和实时时钟
- [ ] 英文文件名标准化
- [ ] PDF文档完整转换
- [ ] 界面截图提取和分类 🆕

- [ ] 登录认证系统
- [ ] 模式切换逻辑
- [ ] 时间控制系统
- [ ] 质量控制模块
- [ ] 数据库管理系统
- [ ] 系统设置界面
- [ ] 网络连接功能
- [ ] 国际化支持

## 📖 使用指南

### 开发环境
1. **启动系统**: 运行程序后自动显示登录界面
2. **模式选择**: 在主界面选择对应的检测模式
3. **参数设置**: 根据样本类型配置检测参数
4. **结果查看**: 在数据库模块查看检测结果

### 界面开发
1. **查看参考**: 使用 `interface_reference/` 目录下的界面截图
2. **精确复刻**: 严格按照截图进行界面开发
3. **分辨率适配**: 确保在1024x600分辨率下完美显示
4. **触屏优化**: 按钮大小适合手指操作

## 🔧 编译说明

### 环境要求

都在D盘dev文件夹下

- Qt 6.8.2 (已安装)
- MinGW-w64 13.1.0 (已安装)
- CMake 3.28.1 (已安装)

### 编译步骤
```bash
# 使用Qt Creator打开项目
# 或者使用qmake命令行编译
qmake trf.pro
make
```

## 📦 自动发布

### GitHub Actions 工作流
项目已配置自动化的发布工作流，支持多平台构建：

- **Windows x64**: 自动打包成便携版 ZIP，包含所有 Qt5 依赖
- **Linux ARM v7l**: 专门适配 myd-y6ull14x14 系统 (Qt 5.6.2精确匹配)

### 兼容性要求
项目严格遵循兼容性规范，确保在目标硬件平台上稳定运行：
- **目标平台**: myd-y6ull14x14 (Linux 4.1.15+ armv7l Qt 5.6.2)
- **构建环境**: Ubuntu 16.04 + Qt 5.6.x (精确匹配)
- **兼容性等级**: A+ (完美匹配)

详细信息请参考：[兼容性要求规范](docs/TRF_Compatibility_Requirements.md)

### 创建发布
```bash
# 创建版本标签触发自动构建
git tag v1.0.0
git push origin v1.0.0
```

### 本地构建测试
```bash
# 运行构建测试脚本
python scripts/test_build.py
```

详细信息请参考：[发布工作流指南](docs/Release_Workflow_Guide.md)

## 📚 文档说明

### 技术文档
- **完整PDF**: `docs/160901(1)_1_45_translate_20250526171032.pdf`
- **分段文档**: `docs_markdown/` 目录下的9个部分
- **系统设计**: `docs_markdown/HumaFIA_System_Design_Requirements.md`
- **界面参考**: `interface_reference/README.md` 🆕

### 界面素材
- **原始素材**: `docs/` 目录下的各个子目录
- **处理后素材**: `resources/images/` 目录下的分类图片
- **界面截图**: `interface_reference/` 目录下的分类截图 🆕

## ⚠️ 重要说明

1. **版权声明**: 界面设计版权归Human公司所有
2. **精确复刻**: 必须严格按照PDF中的界面截图进行开发
3. **医疗标准**: 遵循医疗设备软件开发标准
4. **质量保证**: 确保界面稳定可靠，适用于医疗环境

---

**项目类型**: 医疗设备管理系统  
**开发状态**: 界面设计完成，功能开发中  
**技术支持**: Qt C++ 专业开发 
# CommonHeader第二排按钮样式调整

## 任务描述
调整 CommonHeader 第二排导航按钮的样式和行为：
- 使用新的图片资源作为背景
- 实现激活/非激活状态的视觉区分
- 文字左对齐显示

## 需求规格
1. **按钮标签**：{"Auto sample", "STAT sample", "Fast mode", "QC module", "Database", "Settings"}
2. **白底图片（激活状态）**：{"red_sample.png", "red_sample.png", "red_fast.png", "red_qc.png", "red_database.png", "red_settings.png"}
3. **红底图片（非激活状态）**：{"white_sample.png", "white_sample.png", "white_fast.png", "white_qc.png", "white_database.png", "white_settings.png"}
4. **样式规则**：
   - 激活状态：白底图片 + 灰色文字
   - 非激活状态：红底图片 + 白色文字
   - 同一时间只有一个标签被激活
   - 文字左对齐
   - 图片撑满格子大小(170x70)

## 实现方案
基于现有的按钮容器系统进行优化，保持事件处理逻辑不变。

## 执行步骤
1. ✅ 更新图片路径配置 - 分离激活/非激活状态的图片路径数组
2. ✅ 修改按钮样式 - 添加文字左对齐和内边距
3. ✅ 重写 setActiveButton 逻辑 - 根据状态切换背景图片和文字颜色
4. ✅ 优化布局间隔 - 第一排logo无边框，第二排按钮间隔调整
5. ✅ 字体大小调整 - 从14pt改为12pt防止遮挡
6. ✅ 固定定位布局 - 替代Layout，精确控制按钮位置防止重叠
7. ✅ 添加细边框 - 按钮容器添加1px浅灰色边框
8. ✅ 行间距优化 - 缩小第一排和第二排间距

## 技术细节
- 文件：`code/trf/commonheader.cpp`, `code/trf/resources.qrc`
- 激活文字颜色：#666666 (灰色)
- 非激活文字颜色：white
- 字体大小：12pt
- 文字对齐：左对齐，左边距 10px
- 按钮尺寸：170x70px，固定定位
- 按钮布局：1024px平均分配，每个170px宽度
- 边框样式：1px solid #ccc
- 图片路径前缀：`:/images/common/`

## 布局优化
- **第一排**：logo区域无边框，与填充区域无缝连接
- **第二排**：固定定位防止重叠，精确控制间距
- **垂直间距**：-2px紧凑布局

## 状态
✅ 已完成所有优化 - 等待最终测试验证 
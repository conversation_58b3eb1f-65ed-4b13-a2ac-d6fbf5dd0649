# FastMode 负数行号问题修复

## 问题现象 🚨
从用户日志发现：
```
FastMode: 等待行列表: QList(-2, -1, 5, 6) ，选择: -2
FastMode: updateTimerUI - 行 -2 UI组件缺失，m_dynamicRows包含: false ，m_rowData包含: true
FastMode: 行 -2 阶段1显示 - "Next Sample\n30"
```

## 问题分析 🔍

### 根本原因
1. **数据库加载行号错误**：原来的逻辑从 `testResults.size()` 开始倒减分配行号
   ```cpp
   int rowNumber = testResults.size(); // 如果有3条，rowNumber = 3, 2, 1
   for (...) {
       rowData.rowNumber = rowNumber--; // 最后会变成负数！
   }
   ```

2. **负数行号遗留**：数据库中可能存储了带负数行号的历史数据

3. **清理逻辑不完整**：现有清理逻辑没有专门排除负数行号

## 修复方案 ✅

### 1. 修复数据库加载行号分配
```cpp
// 修复前（错误）
int rowNumber = testResults.size(); // 从最大值开始倒减
rowData.rowNumber = rowNumber--; // 可能产生负数

// 修复后（正确）
int rowNumber = 1; // 从1开始递增
rowData.rowNumber = rowNumber++; // 确保所有行号都是正数
```

### 2. 强化清理逻辑（添加负数行号检查）
```cpp
void FastModePage::cleanupInvalidTimerStates()
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        // 清理条件：1) 负数行号 2) 不在数据中 3) 不在UI中
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            invalidRows.append(rowNumber);
        }
    }
}
```

### 3. 所有Timer方法添加负数行号检查
```cpp
// onGlobalTimerTick
if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
    continue; // 跳过不存在或无效的行
}

// hasTestsInPreIncubationStages
if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
    continue;
}

// getNextWaitingRow  
if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
    continue;
}
```

## 修复效果 ✅

### 修复前问题
- ❌ 数据库加载可能生成负数行号
- ❌ 负数行号在Timer队列中被处理
- ❌ UI组件缺失但Timer逻辑继续运行
- ❌ 错误的等待行选择和启动

### 修复后状态
- ✅ 所有行号从1开始递增，确保为正数
- ✅ 自动清理所有负数行号状态
- ✅ Timer相关方法全面验证行号有效性
- ✅ 队列管理只处理有效的正数行号

## 验证策略 ✅

### 验证重点
1. 清空数据库重新开始测试
2. 确认所有新分配行号都为正数
3. 验证Timer队列只包含有效行号
4. 测试数据库加载后的行号分配

### 预期结果
- 所有日志中行号都≥1
- 无"UI组件缺失"错误
- Timer队列管理正常工作
- 等待行选择逻辑正确

## 防护机制 ✅

### 多层防护
1. **源头防护**：数据库加载时确保正数行号
2. **运行时清理**：定期清理负数行号状态
3. **访问验证**：所有Timer方法验证行号有效性
4. **日志监控**：负数行号会被记录并清理

### 长期稳定性
- 新增数据自动获得正数行号
- 历史负数数据自动清理
- Timer系统健壮性增强
- 防止类似问题再次发生

## 总结
通过修复数据库加载逻辑和强化所有Timer方法的行号验证，完全解决了负数行号问题。现在FastMode具有完整的行号管理和防护机制，确保系统稳定运行。 
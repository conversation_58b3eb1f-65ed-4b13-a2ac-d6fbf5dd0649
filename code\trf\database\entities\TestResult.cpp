#include "TestResult.h"
#include <QStringList>
#include <QRegularExpression>

TestResult::TestResult()
    : m_resultId(0)
    , m_patientId(0)
    , m_testMode(SourcePageType::AUTO_SAMPLE)
    , m_sampleType("S")
    , m_dilutionFactor(1.0)
    , m_testDateTime(QDateTime::currentDateTime())
    , m_createdAt(QDateTime::currentDateTime())
{
}

TestResult::TestResult(int patientId, SourcePageType testMode, const QString& parameterType,
                       const QString& sampleType, const QString& testResult)
    : m_resultId(0)
    , m_patientId(patientId)
    , m_testMode(testMode)
    , m_parameterType(parameterType)
    , m_sampleType(sampleType)
    , m_testResult(testResult)
    , m_dilutionFactor(1.0)
    , m_testDateTime(QDateTime::currentDateTime())
    , m_createdAt(QDateTime::currentDateTime())
{
}

TestResult::TestResult(int resultId, int patientId, SourcePageType testMode,
                       const QString& parameterType, const QString& sampleType,
                       const QString& testResult, const QString& resultStatus,
                       const QDateTime& testDateTime, const QString& lotNumber,
                       const QString& cutoffValue, double dilutionFactor)
    : m_resultId(resultId)
    , m_patientId(patientId)
    , m_testMode(testMode)
    , m_parameterType(parameterType)
    , m_sampleType(sampleType)
    , m_testResult(testResult)
    , m_resultStatus(resultStatus)
    , m_lotNumber(lotNumber)
    , m_cutoffValue(cutoffValue)
    , m_dilutionFactor(dilutionFactor)
    , m_testDateTime(testDateTime)
    , m_createdAt(QDateTime::currentDateTime())
{
}

QString TestResult::getTestModeString() const
{
    return testModeToString(m_testMode);
}

bool TestResult::isValid() const
{
    // 检查患者ID
    if (m_patientId <= 0) {
        return false;
    }
    
    // 检查必填字段
    if (m_parameterType.trimmed().isEmpty()) {
        return false;
    }
    
    // 验证样本类型
    if (!isValidSampleType(m_sampleType)) {
        return false;
    }
    
    // 验证参数类型
    if (!isValidParameterType(m_parameterType)) {
        return false;
    }
    
    // 验证测试时间
    if (!m_testDateTime.isValid()) {
        return false;
    }
    
    // 验证稀释倍数
    if (m_dilutionFactor <= 0 || m_dilutionFactor > 1000) {
        return false;
    }
    
    return true;
}

QString TestResult::getValidationError() const
{
    QStringList errors;
    
    if (m_patientId <= 0) {
        errors << "患者ID必须大于0";
    }
    
    if (m_parameterType.trimmed().isEmpty()) {
        errors << "参数类型不能为空";
    }
    
    if (!isValidSampleType(m_sampleType)) {
        errors << "样本类型必须为 S(血清)、P(血浆) 或 B(全血)";
    }
    
    if (!isValidParameterType(m_parameterType)) {
        errors << "参数类型格式无效";
    }
    
    if (!m_testDateTime.isValid()) {
        errors << "测试时间无效";
    }
    
    if (m_dilutionFactor <= 0 || m_dilutionFactor > 1000) {
        errors << "稀释倍数必须在 0.001-1000 之间";
    }
    
    return errors.join("; ");
}

QString TestResult::getFormattedDateTime() const
{
    if (!m_testDateTime.isValid()) {
        return "无效时间";
    }
    
    // 按照系统界面格式：yyyy.MM.dd hh:mm, 样本类型
    return QString("%1, %2")
           .arg(m_testDateTime.toString("yyyy.MM.dd hh:mm"))
           .arg(m_sampleType);
}

QString TestResult::getSampleTypeDescription() const
{
    switch (m_sampleType.toUpper().at(0).toLatin1()) {
        case 'S': return "Serum";
        case 'P': return "Plasma"; 
        case 'B': return "Blood";
        default: return "Unknown";
    }
}

bool TestResult::isAbnormal() const
{
    // Check if result is abnormal based on status
    return m_resultStatus.contains("Abnormal", Qt::CaseInsensitive) || 
           m_resultStatus.contains("HIGH") || 
           m_resultStatus.contains("LOW") ||
           m_testResult.startsWith(">") ||
           m_testResult.startsWith("<");
}

QString TestResult::toDisplayString() const
{
    // 转换为界面显示格式，类似现有的表格行数据
    return QString("%1|%2|%3|%4|%5|%6|%7")
           .arg(m_parameterType)
           .arg(m_testResult)
           .arg(getFormattedDateTime())
           .arg(m_testMode == SourcePageType::STAT_SAMPLE ? "--:--" : "Timer")
           .arg(m_lotNumber)
           .arg(m_cutoffValue)
           .arg(QString::number(m_dilutionFactor, 'f', 2));
}

QString TestResult::testModeToString(SourcePageType mode)
{
    switch (mode) {
        case SourcePageType::AUTO_SAMPLE:
            return "AUTO_SAMPLE";
        case SourcePageType::STAT_SAMPLE:
            return "STAT_SAMPLE";
        case SourcePageType::FAST_MODE:
            return "FAST_MODE";
        default:
            return "AUTO_SAMPLE";
    }
}

SourcePageType TestResult::stringToTestMode(const QString& modeStr)
{
    if (modeStr == "AUTO_SAMPLE") {
        return SourcePageType::AUTO_SAMPLE;
    } else if (modeStr == "STAT_SAMPLE") {
        return SourcePageType::STAT_SAMPLE;
    } else if (modeStr == "FAST_MODE") {
        return SourcePageType::FAST_MODE;
    } else {
        return SourcePageType::AUTO_SAMPLE; // 默认值
    }
}

bool TestResult::operator==(const TestResult& other) const
{
    return m_resultId == other.m_resultId &&
           m_patientId == other.m_patientId &&
           m_testMode == other.m_testMode &&
           m_parameterType == other.m_parameterType &&
           m_testDateTime == other.m_testDateTime;
}

bool TestResult::operator!=(const TestResult& other) const
{
    return !(*this == other);
}

QString TestResult::toString() const
{
    return QString("TestResult[ID=%1, PatientID=%2, Mode=%3, Parameter='%4', Result='%5', DateTime='%6']")
           .arg(m_resultId)
           .arg(m_patientId)
           .arg(getTestModeString())
           .arg(m_parameterType)
           .arg(m_testResult)
           .arg(m_testDateTime.toString("yyyy-MM-dd hh:mm:ss"));
}

bool TestResult::isValidSampleType(const QString& sampleType) const
{
    QString upperType = sampleType.toUpper();
    return upperType == "S" || upperType == "P" || upperType == "B";
}

bool TestResult::isValidParameterType(const QString& parameterType) const
{
    if (parameterType.trimmed().isEmpty()) {
        return false;
    }
    
    // 验证常见的参数类型格式，如 "hs-CRP [mg/L]", "PCT [ng/mL]" 等
    QStringList validPatterns = {
        "hs-CRP",
        "CRP", 
        "PCT",
        "IL-6",
        "TNF-α",
        "ESR",
        "WBC",
        "Hb",
        "cTnI",
        "Myo",
        "CK-MB"
    };
    
    // 检查是否包含已知的参数名称
    for (const QString& pattern : validPatterns) {
        if (parameterType.contains(pattern, Qt::CaseInsensitive)) {
            return true;
        }
    }
    
    // 如果不在预定义列表中，但格式合理（包含单位），也认为有效
    QRegularExpression unitPattern(R"(\[.+\])");
    return unitPattern.match(parameterType).hasMatch();
}

QDebug operator<<(QDebug debug, const TestResult& testResult)
{
    debug.nospace() << testResult.toString();
    return debug;
} 
# 160901(1)_1_45_translate_20250526171032 - 第 9 部分

页面 41 - 45

---

## 第 41 页

HumaFIA |用户手册
43
点击关闭，结束数据上传。
Connect根据上传的数据启动连接。
激活设置LIS端口（以太网端口）可确保仪器启动后系统自动开始LIS通信。
如果启用“ 串行端口(PC接口）” 设置，则系统将在仪器启动后自动通过HumaFIA所连接的PC上的HumaFIA数据
线开始数据传输。
如果启用“ 串行端口” 设置，则仪器启动后，系统自动启动串行端口通信。
退出将应用所选设置并关闭窗口。
4.9 服务和维护
HumaFIA分析仪被封装在密封外壳内，测试过程中不会产生液体废物。只要正确处理试剂盒，仪器就不会接触
到血液样本，从而避免样本溢出。唯一的维护工具是标准试剂盒套件，具体说明如下。以下为必须执行的维
护工作：
.清除器械表面的灰尘。
当天所有测试完成后，关闭电源开关。
4.9.1 运行标准试剂盒作为质量检查
标准试剂盒套件是一种服务工具，允许用户检查分析仪的光学系统。建议每天作为仪器的质量检查运行。此
外，如果各种参数的控制超出范围，请联系服务工程师，并使用标准试剂盒套件的所有5个级别来检查分析
仪。还应在每6个月一次的服务维护期间使用。
该套件包含一份规格表，其中列出了标准墨盒的5个级别中的每个级别的参考值。根据级别不同，墨盒编号为
1-5。
标准试剂盒用作对照材料，因此使用QC模块屏幕进行操作。


---

## 第 42 页

HumaFIA |用户手册
44
图4-10 QC模块操作标准试剂盒
第一步，必须上传该标准试剂盒的目标值。一旦将标准试剂盒的校准卡插入分析仪，就会自动完成这一操
作。
图4-11标准试剂盒目标值成功上传
在QC模块屏幕上按下Load Standard Cartridge，以访问以下屏幕。然后按下STD-test，弹出窗口显示标准试
剂盒批次。在Gradient旁边，选择接下来应使用的标准试剂盒水平。
图4-12读取标准卡盒（STD）


---

## 第 43 页

HumaFIA |用户手册
45
按下“ 确定” 开始读取所选级别的标准墨盒。对每个级别重复此过程
对5级标准试剂盒对照材料进行检测，显示了当前读数结果以及上传的目标值范围。如图4-13所示，STD读数
的重现性应该非常高。
图4-13标准样品结果及目标值范围
如果结果在质量标准范围内，则出现消息“ QC通过” 。如果不是，重新运行标准试剂盒或使用新的批次。将
上传的目标值与试剂盒中的目标值表上的值进行比较，以确保已正确上传。如果问题仍然存在，请联系服务
工程师。


---

## 第 44 页

HumaFIA |用户手册
46
5 故障排除
本手册包括所有简单故障的故障排除信息。如果问题仍然存在，请联系当地的售后服务中心、当地代理商或
制造商。本设备附带一份合格证。请妥善保管合格证。用户和技术服务人员必须定期维护分析仪。
故障排除措施列表
故障内容
含义
分析仪启动时的自检失败。
警告621
自检显示存储存在问题
警告620
自检显示连接存在问题
分析仪的一个部件出现故障。确保墨盒滑块为空。
检查警告消息中提到的问题。
如果自检报警仍然存在，请联系服务工程师。
某一水平的控制不在范围内。
更换对照材料并重复对照测量。
如果问题仍然存在，请运行标准墨盒套件或联系服务工程师。
标准滤芯套件不匹配所有5个级别。
光学组件故障，请联系服务工程师。
在自动采样或快速模式下的“ 计时器” 列
中显示红色数字。红色数字如02：11以及
IL或IS标志显示如下警告：
警告300
孵育时间过长。结果不正确。
请重新运行样本。
HumaFIA对孵育时间的监测显示了不正确的时机。IL：孵育时间
过长，IS：孵育时间过短。
使用新的测试盒重复测量。
警告400
控制已过期，请使用新的批次
所用的对照材料已过期。用新的批次替换它，并上传新的目标
值。
警告402
超过24小时未进行控制测量
GLP建议每天使用一次控制。每24小时运行一次控制。
警告403
测试盒已过期，请使用新批次
测试盒批次已过期，用新批次替换，并上传新的校准数据。
警告404
无法记录当前参数。
GLP建议每天使用控制。运行一个控制，当控制成功匹配参数范
围后，软件将允许用户再次运行这个参数。


---

## 第 45 页

HumaFIA |用户手册
47
过去yy天内没有有效的对照测量。
请控制运行！
旧数据消失。
警告501
已达到最大数据量。将开始覆盖旧数据。
请导出/删除数据！
警告501提示数据存储空间不足。如果忽略此消息，将覆盖最早
的资料集。
警告100
环境温度不符合规范。
请在18至28℃的房间内重新运行样品
温度
HumaFIA具有温度传感器，用于监测环境温度。如果温度超出指
定范围，则所有结果都将被标记，并显示警告。由于孵育温度
不当，结果可能不正确。
警告502
结果超出线性范围。
请重新运行预稀释样本（自动/STAT）
HumaFIA提供软件选项，可在自动样本模式和STAT样本模式下自
动计算预稀释样品。
非线性结果超出分析仪质量标准，可能是错误的。重新运行预
稀释样品。
警告611
外部条形码阅读器未连接
检查外部条形码阅读器的电缆连接和状态LED。如需进一步帮
助，请参考外部条形码阅读器的用户手册。
警告612
LIS，以太网连接未激活
警告613
串行端口(PC接口）未激活。
请检查电缆
警告614
串行端口未激活。请检查电缆
检查两侧（分析仪和外部连接器）的电缆和连接。
请联系服务工程师以获得进一步的帮助。
警告615
外置打印机离线，请检查打印机状态
检查分析仪和外部打印机两侧的电缆和连接。请参考打印机用
户手册以获取进一步的帮助。
警告301
预稀释样品结果偏低，请在未稀释模式下
重新运行样品
由于预稀释需要手动移液步骤，因此尽可能使用未稀释样品。
如果结果处于正常线性范围内，则建议将样品作为正常样品再
次运行。
警告610需要维护。请寻求技术支持！
建议每6个月运行一次维护周期。
表5-1故障排除


---


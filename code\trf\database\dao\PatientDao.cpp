#include "PatientDao.h"
#include "../connection/DatabaseConnection.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QDebug>

PatientDao::PatientDao(QObject *parent)
    : QObject(parent)
{
}

int PatientDao::create(const Patient& patient)
{
    if (!validatePatient(patient)) {
        setError("Patient data validation failed: " + patient.getValidationError());
        return -1;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return -1;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        INSERT INTO patients (patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at)
        VALUES (:patient_name, :last_name, :first_name, :birthday, :gender, :remarks, :created_at, :updated_at)
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare insert query: " + query.lastError().text());
        return -1;
    }
    
    bindPatientParameters(query, patient);
    
    if (!query.exec()) {
        setError("Failed to insert patient: " + query.lastError().text());
        return -1;
    }
    
    int newPatientId = query.lastInsertId().toInt();
    qDebug() << "Patient created successfully with ID:" << newPatientId;
    return newPatientId;
}

Patient PatientDao::findById(int patientId)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return Patient();
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT patient_id, patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at
        FROM patients 
        WHERE patient_id = :patient_id
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return Patient();
    }
    
    query.bindValue(":patient_id", patientId);
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return Patient();
    }
    
    if (query.next()) {
        return createPatientFromQuery(query);
    }
    
    setError(QString("Patient with ID %1 not found").arg(patientId));
    return Patient();
}

QList<Patient> PatientDao::findByName(const QString& patientName)
{
    QList<Patient> patients;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return patients;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT patient_id, patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at
        FROM patients 
        WHERE patient_name LIKE :patient_name OR last_name LIKE :patient_name OR first_name LIKE :patient_name
        ORDER BY created_at DESC
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return patients;
    }
    
    QString searchPattern = QString("%%1%").arg(patientName);
    query.bindValue(":patient_name", searchPattern);
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return patients;
    }
    
    while (query.next()) {
        patients.append(createPatientFromQuery(query));
    }
    
    return patients;
}

QList<Patient> PatientDao::findAll(int limit, int offset)
{
    QList<Patient> patients;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return patients;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT patient_id, patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at
        FROM patients 
        ORDER BY created_at DESC
    )";
    
    if (limit > 0) {
        sql += QString(" LIMIT %1").arg(limit);
        if (offset > 0) {
            sql += QString(" OFFSET %1").arg(offset);
        }
    }
    
    if (!query.exec(sql)) {
        setError("Failed to execute select query: " + query.lastError().text());
        return patients;
    }
    
    while (query.next()) {
        patients.append(createPatientFromQuery(query));
    }
    
    return patients;
}

bool PatientDao::update(const Patient& patient)
{
    if (patient.getPatientId() <= 0) {
        setError("Patient ID must be greater than 0 for update");
        return false;
    }
    
    if (!validatePatient(patient)) {
        setError("Patient data validation failed: " + patient.getValidationError());
        return false;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        UPDATE patients 
        SET patient_name = :patient_name, last_name = :last_name, first_name = :first_name,
            birthday = :birthday, gender = :gender, remarks = :remarks, updated_at = :updated_at
        WHERE patient_id = :patient_id
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare update query: " + query.lastError().text());
        return false;
    }
    
    bindPatientParameters(query, patient);
    query.bindValue(":patient_id", patient.getPatientId());
    
    if (!query.exec()) {
        setError("Failed to update patient: " + query.lastError().text());
        return false;
    }
    
    int affectedRows = query.numRowsAffected();
    if (affectedRows == 0) {
        setError(QString("No patient found with ID %1").arg(patient.getPatientId()));
        return false;
    }
    
    qDebug() << "Patient updated successfully, ID:" << patient.getPatientId();
    return true;
}

bool PatientDao::remove(int patientId)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "DELETE FROM patients WHERE patient_id = :patient_id";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare delete query: " + query.lastError().text());
        return false;
    }
    
    query.bindValue(":patient_id", patientId);
    
    if (!query.exec()) {
        setError("Failed to delete patient: " + query.lastError().text());
        return false;
    }
    
    int affectedRows = query.numRowsAffected();
    if (affectedRows == 0) {
        setError(QString("No patient found with ID %1").arg(patientId));
        return false;
    }
    
    qDebug() << "Patient deleted successfully, ID:" << patientId;
    return true;
}

bool PatientDao::exists(int patientId)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "SELECT COUNT(*) FROM patients WHERE patient_id = :patient_id";
    
    if (!query.prepare(sql)) {
        return false;
    }
    
    query.bindValue(":patient_id", patientId);
    
    if (!query.exec() || !query.next()) {
        return false;
    }
    
    return query.value(0).toInt() > 0;
}

int PatientDao::count()
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return -1;
    }
    
    QSqlQuery query(db);
    QString sql = "SELECT COUNT(*) FROM patients";
    
    if (!query.exec(sql)) {
        setError("Failed to count patients: " + query.lastError().text());
        return -1;
    }
    
    if (query.next()) {
        return query.value(0).toInt();
    }
    
    return 0;
}

QList<Patient> PatientDao::search(const QString& keyword, int limit)
{
    QList<Patient> patients;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return patients;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT patient_id, patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at
        FROM patients 
        WHERE patient_name LIKE :keyword 
           OR last_name LIKE :keyword 
           OR first_name LIKE :keyword 
           OR remarks LIKE :keyword
        ORDER BY created_at DESC
        LIMIT :limit
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare search query: " + query.lastError().text());
        return patients;
    }
    
    QString searchPattern = QString("%%1%").arg(keyword);
    query.bindValue(":keyword", searchPattern);
    query.bindValue(":limit", limit);
    
    if (!query.exec()) {
        setError("Failed to execute search query: " + query.lastError().text());
        return patients;
    }
    
    while (query.next()) {
        patients.append(createPatientFromQuery(query));
    }
    
    return patients;
}

bool PatientDao::isNameExists(const QString& lastName, const QString& firstName, int excludePatientId)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT COUNT(*) FROM patients 
        WHERE last_name = :last_name AND first_name = :first_name
    )";
    
    if (excludePatientId > 0) {
        sql += " AND patient_id != :exclude_id";
    }
    
    if (!query.prepare(sql)) {
        return false;
    }
    
    query.bindValue(":last_name", lastName);
    query.bindValue(":first_name", firstName);
    if (excludePatientId > 0) {
        query.bindValue(":exclude_id", excludePatientId);
    }
    
    if (!query.exec() || !query.next()) {
        return false;
    }
    
    return query.value(0).toInt() > 0;
}

QList<Patient> PatientDao::getRecentPatients(int limit)
{
    QList<Patient> patients;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return patients;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT patient_id, patient_name, last_name, first_name, birthday, gender, remarks, created_at, updated_at
        FROM patients 
        ORDER BY created_at DESC
        LIMIT :limit
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare recent patients query: " + query.lastError().text());
        return patients;
    }
    
    query.bindValue(":limit", limit);
    
    if (!query.exec()) {
        setError("Failed to execute recent patients query: " + query.lastError().text());
        return patients;
    }
    
    while (query.next()) {
        patients.append(createPatientFromQuery(query));
    }
    
    return patients;
}

QString PatientDao::getLastError() const
{
    return m_lastError;
}

Patient PatientDao::createPatientFromQuery(const QSqlQuery& query)
{
    Patient patient;
    patient.setPatientId(query.value("patient_id").toInt());
    patient.setPatientName(query.value("patient_name").toString());
    patient.setLastName(query.value("last_name").toString());
    patient.setFirstName(query.value("first_name").toString());
    patient.setBirthday(query.value("birthday").toDate());
    patient.setGender(query.value("gender").toString());
    patient.setRemarks(query.value("remarks").toString());
    patient.setCreatedAt(query.value("created_at").toDateTime());
    patient.setUpdatedAt(query.value("updated_at").toDateTime());
    
    return patient;
}

bool PatientDao::validatePatient(const Patient& patient)
{
    return patient.isValid();
}

void PatientDao::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "PatientDao Error:" << error;
}

bool PatientDao::deleteAllPatients()
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "DELETE FROM patients";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare delete all query: " + query.lastError().text());
        return false;
    }
    
    if (!query.exec()) {
        setError("Failed to execute delete all query: " + query.lastError().text());
        return false;
    }
    
    qDebug() << "Successfully deleted" << query.numRowsAffected() << "patients";
    return true;
}

void PatientDao::bindPatientParameters(QSqlQuery& query, const Patient& patient)
{
    query.bindValue(":patient_name", patient.getFormattedName());
    query.bindValue(":last_name", patient.getLastName());
    query.bindValue(":first_name", patient.getFirstName());
    query.bindValue(":birthday", patient.getBirthday());
    query.bindValue(":gender", patient.getGender());
    query.bindValue(":remarks", patient.getRemarks());
    query.bindValue(":created_at", QDateTime::currentDateTime());
    query.bindValue(":updated_at", QDateTime::currentDateTime());
} 
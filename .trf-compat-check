# TRF 兼容性检查配置
# 此文件用于自动化工具检查代码兼容性

[target_platform]
name = "myd-y6ull14x14"
os = "Linux"
kernel = "4.1.15+"
arch = "armv7l"
qt_version = "5.6.2"

[qt_compatibility]
min_version = "5.6.0"
target_version = "5.6.2"
max_version = "5.15.99"
deprecated_before = "0x050600"

[forbidden_apis]
# Qt 5.10+ APIs to avoid
QRandomGenerator = "use qrand() instead"
QStringView = "use QString instead"
qOverload = "use traditional overload syntax"

[required_defines]
QT_DISABLE_DEPRECATED_BEFORE = "0x050600"
TARGET_SYSTEM_MYD_Y6ULL14X14 = "1"

[build_environment]
docker_image = "arm32v7/ubuntu:16.04"
compiler = "gcc-5.4+"
qt_package = "qt5-default"

[validation_scripts]
system_check = "./check.sh"
compatibility_check = "python3 scripts/verify_system_compatibility.py"
build_test = "python3 scripts/test_build.py"

[documentation]
requirements = "docs/TRF_Compatibility_Requirements.md"
issues = "issues/myd-y6ull14x14精确系统匹配优化完成.md" 
@echo off
REM TRF Windows 编译测试脚本
echo ============================================
echo TRF Windows 编译测试
echo ============================================

REM 检查Qt环境
echo 检查Qt环境...
qmake --version
if %ERRORLEVEL% neq 0 (
    echo 错误: qmake未找到，请确保Qt已正确安装并添加到PATH
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\..\code\trf"
if not exist "trf.pro" (
    echo 错误: 未找到trf.pro文件
    pause
    exit /b 1
)

echo 当前目录: %CD%
echo.

REM 清理之前的构建
echo 清理之前的构建...
if exist "Makefile*" del /Q "Makefile*"
if exist "release" rmdir /S /Q "release"
if exist "debug" rmdir /S /Q "debug"

REM 运行qmake
echo 运行qmake...
qmake trf.pro CONFIG+=release "QMAKE_CXXFLAGS+=/MP /O2"
if %ERRORLEVEL% neq 0 (
    echo 错误: qmake执行失败
    pause
    exit /b 1
)

REM 检查Makefile生成
if exist "Makefile.Release" (
    echo ✓ Makefile.Release 生成成功
) else (
    echo ✗ Makefile.Release 未生成
    pause
    exit /b 1
)

REM 编译项目
echo 开始编译...
nmake
if %ERRORLEVEL% neq 0 (
    echo 错误: 编译失败
    echo.
    echo 常见问题检查:
    echo 1. 确保Qt版本兼容 (建议Qt 5.15.x)
    echo 2. 检查QTextCodec相关错误 (已修复)
    echo 3. 确保MSVC编译器已正确配置
    pause
    exit /b 1
)

REM 检查编译结果
if exist "release\trf.exe" (
    echo ✓ 编译成功! trf.exe已生成
    dir "release\trf.exe"
) else (
    echo ✗ 编译失败，未找到trf.exe
    pause
    exit /b 1
)

echo.
echo ============================================
echo 编译测试完成
echo ============================================
echo 可执行文件位置: %CD%\release\trf.exe
echo.
echo 提示: 在发布前请运行 windeployqt 打包依赖
echo 命令: windeployqt --release --sql release\trf.exe
echo.
pause 
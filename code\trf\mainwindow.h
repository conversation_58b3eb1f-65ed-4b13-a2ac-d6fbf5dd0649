#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QStackedWidget>
#include "addsamplepage.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainScreen;
class BasePage;
class AddSamplePage;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void navigateToPage(int index);
    void returnToHome();
    void navigateToAddSamplePage();
    void navigateToAddSamplePageWithType(SourcePageType sourceType);
    void onAddSamplePageExitRequested();
    void onDataAddedToDatabase(SourcePageType sourceType, const QString& patientName, const QString& patientId, 
                              const QString& parameter, const QString& sampleType, const QString& result,
                              const QString& datetime, const QString& lot, const QString& cutoff);
    void onDataCleared(); // Handle data cleared signal
    
    // Navigation helper slots for Qt 5.6 compatibility
    void navigateToAutoSample() { navigateToPage(1); }
    void navigateToStatSample() { navigateToPage(2); }
    void navigateToFastMode() { navigateToPage(3); }
    void navigateToQcModule() { navigateToPage(4); }
    void navigateToDatabase() { navigateToPage(5); }
    void navigateToSettings() { navigateToPage(6); }
    void navigateToStatSampleAdd() { navigateToAddSamplePageWithType(SourcePageType::STAT_SAMPLE); }
    void navigateToFastModeAdd() { navigateToAddSamplePageWithType(SourcePageType::FAST_MODE); }

private:
    void setupConnections();

private:
    Ui::MainWindow *ui;
    QStackedWidget *stackedWidget;
    
    // Page 0: MainScreen
    MainScreen *mainScreen;
    
    // Pages 1-6: Mode pages
    BasePage *autoSamplePage;
    BasePage *statSamplePage;
    BasePage *fastModePage;
    BasePage *qcModulePage;
    BasePage *databasePage;
    BasePage *settingsPage;
    BasePage *addSampleBasePage;
    
    SourcePageType currentAddSampleSourceType;
};
#endif // MAINWINDOW_H

<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LoginScreen</class>
 <widget class="QWidget" name="LoginScreen">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HumaFIA Login</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#loginFrame {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #CCCCCC;
    border-radius: 10px;
}

QLabel {
    font: 16pt &quot;Segoe UI&quot;;
    color: #333333;
}

QLineEdit {
    font: 16pt &quot;Segoe UI&quot;;
    border: 1px solid #AAAAAA;
    border-radius: 4px;
    padding: 4px;
}

QPushButton {
    font: bold 16pt &quot;Segoe UI&quot;;
    color: #333333;
    background-color: white;
    border: 2px solid #CCCCCC;
    border-radius: 5px;
	padding: 8px;
}

QPushButton:hover {
    background-color: #F5F5F5;
    border-color: #999999;
}

QPushButton:pressed {
    background-color: #E8E8E8;
    border-color: #666666;
}
</string>
  </property>
  <widget class="QFrame" name="loginFrame">
   <property name="geometry">
    <rect>
     <x>262</x>
     <y>150</y>
     <width>500</width>
     <height>300</height>
    </rect>
   </property>
   <property name="frameShape">
    <enum>QFrame::Shape::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Shadow::Raised</enum>
   </property>
   <widget class="QLabel" name="userLabel">
    <property name="geometry">
     <rect>
      <x>60</x>
      <y>50</y>
      <width>100</width>
      <height>40</height>
     </rect>
    </property>
    <property name="text">
     <string>User</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
    </property>
   </widget>
   <widget class="QLineEdit" name="userInput">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>50</y>
      <width>260</width>
      <height>40</height>
     </rect>
    </property>
    <property name="text">
     <string>admin</string>
    </property>
   </widget>
   <widget class="QLabel" name="passwordLabel">
    <property name="geometry">
     <rect>
      <x>60</x>
      <y>120</y>
      <width>100</width>
      <height>40</height>
     </rect>
    </property>
    <property name="text">
     <string>Password</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
    </property>
   </widget>
   <widget class="QLineEdit" name="passwordInput">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>120</y>
      <width>260</width>
      <height>40</height>
     </rect>
    </property>
    <property name="echoMode">
     <enum>QLineEdit::EchoMode::Password</enum>
    </property>
   </widget>
   <widget class="QPushButton" name="clearButton">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>200</y>
      <width>120</width>
      <height>50</height>
     </rect>
    </property>
    <property name="text">
     <string>Clear</string>
    </property>
   </widget>
   <widget class="QPushButton" name="loginButton">
    <property name="geometry">
     <rect>
      <x>320</x>
      <y>200</y>
      <width>120</width>
      <height>50</height>
     </rect>
    </property>
    <property name="text">
     <string>Login</string>
    </property>
   </widget>
  </widget>
  <widget class="QProgressBar" name="progressBar">
   <property name="geometry">
    <rect>
     <x>300</x>
     <y>530</y>
     <width>424</width>
     <height>25</height>
    </rect>
   </property>
   <property name="value">
    <number>0</number>
   </property>
   <property name="textVisible">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true">QProgressBar {
    border: 1px solid #CCCCCC;
    border-radius: 5px;
    background-color: #F0F0F0;
}

QProgressBar::chunk {
    background-color: #AAAAAA;
    border-radius: 4px;
}</string>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>

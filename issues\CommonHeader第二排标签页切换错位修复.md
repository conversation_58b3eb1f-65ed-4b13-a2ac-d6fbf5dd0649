# CommonHeader第二排标签页切换错位修复

## 问题描述
CommonHeader 的第二排标签页切换存在错位问题，点击某个标签页时激活的是前一个标签页。

## 问题根因分析
索引映射不一致导致的双重转换问题：

### 原始流程（错误）
1. 用户点击按钮1 (STAT sample)
2. CommonHeader::onButtonClicked(1) → setActiveButton(1) ✓
3. emit navigationClicked(1) → MainWindow::navigateToPage(1)
4. MainWindow::setActiveButton(1-1=0) → 激活按钮0 (Auto sample) ✗

### 索引系统混乱
- **CommonHeader 按钮索引**：0-5 (Auto sample=0, STAT sample=1, ...)
- **MainWindow 页面索引**：1-6 (autoSamplePage=1, statSamplePage=2, ...)
- **问题**：CommonHeader 直接发送按钮索引，但 MainWindow 期望页面索引

## 解决方案
在 CommonHeader::onButtonClicked 中进行索引转换，让信号携带正确的页面索引。

### 修复后流程（正确）
1. 用户点击按钮1 (STAT sample)
2. CommonHeader::onButtonClicked(1) → setActiveButton(1) ✓
3. emit navigationClicked(1+1=2) → MainWindow::navigateToPage(2)
4. MainWindow::setActiveButton(2-1=1) → 激活按钮1 (STAT sample) ✓

## 修改内容

### 文件：code/trf/commonheader.cpp
```cpp
void CommonHeader::onButtonClicked(int index)
{
    setActiveButton(index);
    // Convert button index (0-5) to page index (1-6) for MainWindow navigation
    emit navigationClicked(index + 1);
}
```

**修改说明**：
- 原来：`emit navigationClicked(index);`
- 修改为：`emit navigationClicked(index + 1);`
- 目的：将按钮索引(0-5)转换为页面索引(1-6)

## 验证结果
修复后的索引对应关系：
- 点击 "Auto sample" (按钮0) → 发送信号1 → 激活按钮0 ✓
- 点击 "STAT sample" (按钮1) → 发送信号2 → 激活按钮1 ✓  
- 点击 "Fast mode" (按钮2) → 发送信号3 → 激活按钮2 ✓
- 点击 "QC module" (按钮3) → 发送信号4 → 激活按钮3 ✓
- 点击 "Database" (按钮4) → 发送信号5 → 激活按钮4 ✓
- 点击 "Settings" (按钮5) → 发送信号6 → 激活按钮5 ✓

## 状态
✅ 已完成修复 - 标签页切换现在工作正常 
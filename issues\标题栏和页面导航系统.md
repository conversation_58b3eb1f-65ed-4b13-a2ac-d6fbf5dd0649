# 标题栏和页面导航系统开发

## 任务概述
基于干净的服务器版本，开发6个模式页面共用的标题栏组件和页面导航系统。

## 设计要求
- 页面分辨率：1024×600像素
- 标题栏总高度：120px (第一排50px + 第二排70px)
- 医疗设备专业风格，Human品牌红色主色调
- 第二排按钮采用MainScreen的堆叠图片文字方案

## 已完成的开发内容

### 1. CommonHeader组件 (commonheader.h/cpp)
- **第一排 (50px)**：Logo区域 + 温度显示 + 日期时间 + 状态区
- **第二排 (70px)**：6个导航按钮，每个170×70px
- **按钮实现**：QWidget容器 + QLabel背景图 + QPushButton文字
- **状态管理**：支持setActiveButton()切换激活状态
- **事件处理**：发射navigationClicked(int)信号

### 2. BasePage基类 (basepage.h/cpp)
- 为6个模式页面提供统一的基类
- 包含CommonHeader和内容区域
- 提供getHeader()方法供MainWindow访问

### 3. MainWindow更新 (mainwindow.h/cpp)
- 使用QStackedWidget管理页面切换
- 创建6个BasePage实例对应6个模式
- 实现页面导航逻辑：navigateToPage()和returnToHome()
- 连接header信号处理页面切换

### 4. 资源文件更新 (resources.qrc)
- 添加main_auto.png等6个主界面图片资源
- 支持标题栏按钮显示

### 5. 项目文件更新 (trf.pro)
- 添加commonheader.cpp/h和basepage.cpp/h到编译列表

## 技术特点

### 标题栏布局
```
第一排: [HUMAN Logo 150px] [24°C 100px] [2024.01.15 12:34:56 240px] [状态区 534px]
第二排: [Auto sample 170px] [STAT sample 170px] [Fast mode 170px] [QC module 170px] [Database 170px] [Settings 170px]
```

### 按钮状态
- **非激活状态**：背景图片 + 白色文字
- **激活状态**：背景图片 + 红色文字 (#dc143c)

### 页面结构
```
Page 0: MainScreen (原主界面)
Page 1: AutoSamplePage (自动采样模式)  
Page 2: StatSamplePage (STAT采样模式)
Page 3: FastModePage (快速模式)
Page 4: QcModulePage (质控模块)
Page 5: DatabasePage (数据库)
Page 6: SettingsPage (设置)
```

### 6. MainScreen导航连接 (mainwindow.cpp更新)
- 添加MainScreen 6个按钮的点击事件连接
- Auto Sample → Page 1, STAT Sample → Page 2, Fast Mode → Page 3
- QC Module → Page 4, Database → Page 5, Settings → Page 6
- 支持从模式页面标题栏返回主界面

## 导航流程
```
MainScreen (Page 0)
    ↓ 点击6个按钮之一
Mode Pages (Page 1-6)
    ↓ 点击标题栏其他按钮
其他Mode Pages
    ↓ 特殊处理
返回MainScreen
```

## 下一步开发
1. ✅ 为MainScreen添加到模式页面的导航连接 (已完成)
2. 在各个模式页面的内容区域开发具体功能
3. 完善状态指示器功能
4. ✅ 添加返回主界面的功能 (已完成)

## 文件清单
- commonheader.h/cpp - 标题栏组件
- basepage.h/cpp - 页面基类
- mainwindow.h/cpp - 主窗口(已更新)
- resources.qrc - 资源文件(已更新)
- trf.pro - 项目文件(已更新)

## 遵循的设计标准
- 基于PDF文档医疗设备界面标准
- 采用MainScreen成功的按钮实现方案
- 支持1024×600分辨率完美显示
- 保持专业医疗设备外观 
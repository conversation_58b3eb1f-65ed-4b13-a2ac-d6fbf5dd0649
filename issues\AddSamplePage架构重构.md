# AddSamplePage 架构重构

## 需求背景
用户要求触发 AddSample 按钮后的页面应该是 CommonHeader + AddSamplePage，总尺寸保持 1024*600。

## 问题分析
原始实现中 AddSamplePage 是独立页面，缺少 CommonHeader，与其他6个模式页面的架构不一致。

### 原始架构
- **Pages 1-6**：BasePage(CommonHeader + 内容区域)
- **AddSamplePage**：直接独立页面，无 CommonHeader

### 架构不一致问题
- 缺少标题栏导航功能
- 代码结构不统一
- 维护困难

## 最佳实践方案
将 AddSamplePage 包装到 BasePage 中，实现架构统一。

### 优势
- ✅ **架构一致性**：所有页面使用统一的 BasePage 模式
- ✅ **代码复用**：复用现有的 CommonHeader 逻辑  
- ✅ **导航完整性**：AddSamplePage 获得完整的标题栏导航功能
- ✅ **维护性**：统一的页面管理方式
- ✅ **扩展性**：符合软件架构的 DRY 原则

## 实现内容

### 1. 更新页面创建逻辑 (mainwindow.cpp)
```cpp
// 原来：直接添加 AddSamplePage
addSamplePage = new AddSamplePage(this);
stackedWidget->addWidget(addSamplePage);

// 现在：包装到 BasePage 中
addSampleBasePage = new BasePage(this);
addSampleBasePage->setContentWidget(new AddSamplePage(this));
stackedWidget->addWidget(addSampleBasePage);
```

### 2. 更新头文件声明 (mainwindow.h)
```cpp
// 原来：独立页面指针
AddSamplePage *addSamplePage;

// 现在：BasePage 指针
BasePage *addSampleBasePage;
```

### 3. 添加导航连接 (mainwindow.cpp)
```cpp
// 连接 AddSamplePage 的 CommonHeader 导航
connect(addSampleBasePage->getHeader(), &CommonHeader::navigationClicked, 
        this, &MainWindow::navigateToPage);
```

### 4. 更新导航逻辑 (mainwindow.cpp)
```cpp
void MainWindow::navigateToAddSamplePage()
{
    // 设置 Auto sample 按钮为激活状态
    addSampleBasePage->getHeader()->setActiveButton(0);
    stackedWidget->setCurrentIndex(7);
}

void MainWindow::navigateToPage(int index)
{
    // 扩展边界检查支持第7页
    if (index < 1 || index > 7) { // 原来是 > 6
        qWarning() << "Invalid page index:" << index;
        return;
    }
    
    // 区分处理常规页面和 AddSamplePage
    if (index <= 6) {
        page->getHeader()->setActiveButton(index - 1);
    } else if (index == 7) {
        page->getHeader()->setActiveButton(0); // Auto sample
    }
}
```

## 尺寸验证
- **CommonHeader**：1024 × 125px
- **AddSamplePage 内容区域**：1024 × 475px
- **总计**：1024 × 600px ✓

## 功能增强
- ✅ AddSamplePage 现在拥有完整的 CommonHeader 导航
- ✅ 可以通过标题栏按钮切换到其他页面
- ✅ 保持与 Auto Sample 的逻辑关联（激活 Auto sample 按钮）
- ✅ 架构统一，维护性提升

## 状态
✅ 已完成重构 - AddSamplePage 现在拥有完整的导航功能 
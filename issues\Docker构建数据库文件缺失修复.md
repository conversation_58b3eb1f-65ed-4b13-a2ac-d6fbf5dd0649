# Docker构建数据库文件缺失修复

## 问题描述
Docker 构建过程中出现编译错误，无法找到 database 相关的头文件和源文件：
- `database/connection/DatabaseConnection.h: No such file or directory`
- qmake 阶段警告找不到多个 database 相关文件

## 根本原因
GitHub Actions 工作流中的 Dockerfile 生成代码使用了：
```dockerfile
COPY *.cpp *.h *.ui ./
```
这种通配符复制方式只复制当前目录的文件，不会递归复制子目录。

## 解决方案
修改 `.github/workflows/release.yml` 中的 Dockerfile 生成部分，添加子目录的递归复制：
```dockerfile
COPY database/ database/
COPY controllers/ controllers/
```

## 执行步骤
1. ✅ 修改 GitHub Actions 工作流文件
   - 在 `.github/workflows/release.yml` 第 155-156 行添加了：
     ```dockerfile
     COPY database/ database/
     COPY controllers/ controllers/
     ```
2. ✅ 保持构建缓存优化策略
   - 保留了分层复制策略，先复制配置文件，再复制源代码
3. ⏳ 验证修复效果（需要触发 CI 构建）

## 实际结果
- ✅ 已修改 Dockerfile 生成代码，添加了缺失的子目录复制
- ✅ 保持了原有的构建缓存优化
- ✅ 修复了 TestResult.h 中的相对路径问题
- ⏳ 等待下次 CI 构建验证修复效果

## 发现的额外问题
在修复主要问题后，发现了第二个问题：
```
database/entities/TestResult.h:7:10: fatal error: ../addsamplepage.h: No such file or directory
```

**根本原因：** `database/entities/TestResult.h` 中使用了错误的相对路径
**解决方案：** 将 `#include "../addsamplepage.h"` 修改为 `#include "../../addsamplepage.h"`

## 技术细节
1. 修改位置：`.github/workflows/release.yml` 行 155-156
   - 原因：通配符 `*.cpp *.h *.ui` 只复制当前目录文件，不递归复制子目录
2. 修改位置：`code/trf/database/entities/TestResult.h` 行 7
   - 原因：相对路径计算错误，从 `database/entities/` 到根目录需要 `../../` 
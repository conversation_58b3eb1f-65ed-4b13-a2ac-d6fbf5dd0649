root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# ./check.sh 
=== TRF 绯荤粺鍏煎鎬ф鏌===
棰勬湡鐩爣: Linux myd-y6ull14x14 4.1.15+ armv7l
褰撳墠绯荤粺: Linux myd-y6ull14x14 4.1.15+ #4 SMP PREEMPT Wed Jul 2 20:20:33 CST 2025 armv7l armv7l armv7l GNU/Linux

鉁鏋舵瀯鍏煎: armv7l

妫€鏌t5杩愯鏃跺簱:
鉁libQt5Core.so.5
鉁libQt5Gui.so.5
鉁libQt5Widgets.so.5
鉁libQt5Sql.so.5

妫€鏌QLite椹卞姩:
鉁Qt5 SQL搴撳瓨鍦紝SQLite鏀寔鍙敤

妫€鏌ュ浘褰㈢郴缁熸敮鎸
                        鉁甯х紦鍐茶澶/dev/fb0 瀛樺湪

妫€鏌ヨЕ鎽歌緭鍏ユ敮鎸
                       鉁瑙︽懜杈撳叆璁惧 /dev/input/event0 瀛樺湪
鉁tslib瑙︽懜搴撳瓨鍦

妫€鏌penGL鏀寔:
鉁EGL搴撳瓨鍦
鉁OpenGL ES 2.0搴撳瓨鍦

鉁绯荤粺鍏煎锛屽彲浠ヨ繍琛./run.sh
root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# 
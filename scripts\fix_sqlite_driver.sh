#!/bin/bash

echo "=== TRF SQLite Driver Fix Script ==="
echo "适用于 myd-y6ull14x14 系统的 SQLite 驱动修复"
echo "============================================"

# 设置应用目录
APP_DIR="/mnt/sd/TRF-********-Linux-ARM-v7l"
cd "$APP_DIR" || exit 1

echo "当前目录: $(pwd)"

# 步骤1: 检查系统 Qt 插件
echo -e "\n=== 步骤1: 检查系统 Qt 插件 ==="
echo "查找系统 Qt5 插件目录..."
find /usr -name "qt5" -type d 2>/dev/null | head -5

echo -e "\n检查 Qt5 插件子目录..."
if [ -d "/usr/lib/qt5/plugins" ]; then
    echo "✓ 找到 Qt5 插件目录: /usr/lib/qt5/plugins"
    ls -la /usr/lib/qt5/plugins/
else
    echo "❌ 未找到标准 Qt5 插件目录"
    echo "尝试查找其他位置..."
    find /usr -name "plugins" -type d | grep qt
fi

# 查找 SQLite 驱动
echo -e "\n查找 SQLite 驱动文件..."
find /usr -name "*sqlite*" -name "*.so" 2>/dev/null

# 步骤2: 创建插件目录结构
echo -e "\n=== 步骤2: 创建应用插件目录 ==="
mkdir -p "$APP_DIR/plugins/sqldrivers"
mkdir -p "$APP_DIR/plugins/platforms"
echo "✓ 创建插件目录结构"

# 步骤3: 复制 SQLite 驱动
echo -e "\n=== 步骤3: 复制 SQLite 驱动 ==="

# 查找并复制 SQLite 驱动
SQLITE_DRIVER=""
for location in "/usr/lib/qt5/plugins/sqldrivers" "/usr/lib/plugins/sqldrivers" "/usr/lib/qt5/sqldrivers"; do
    if [ -f "$location/libqsqlite.so" ]; then
        SQLITE_DRIVER="$location/libqsqlite.so"
        break
    fi
done

if [ -n "$SQLITE_DRIVER" ]; then
    echo "✓ 找到 SQLite 驱动: $SQLITE_DRIVER"
    cp "$SQLITE_DRIVER" "$APP_DIR/plugins/sqldrivers/"
    echo "✓ 驱动已复制到应用目录"
else
    echo "❌ 未找到 SQLite 驱动，尝试其他方法..."
    # 尝试从系统库中查找
    find /usr/lib -name "*sqlite*.so" | while read file; do
        echo "发现可能的驱动: $file"
    done
fi

# 复制平台插件
echo -e "\n复制平台插件..."
for location in "/usr/lib/qt5/plugins/platforms" "/usr/lib/plugins/platforms"; do
    if [ -d "$location" ]; then
        echo "✓ 找到平台插件目录: $location"
        cp -r "$location"/* "$APP_DIR/plugins/platforms/" 2>/dev/null
        break
    fi
done

# 步骤4: 验证复制结果
echo -e "\n=== 步骤4: 验证插件复制 ==="
echo "应用插件目录内容:"
find "$APP_DIR/plugins" -name "*.so" | head -10

# 步骤5: 创建修复的启动脚本
echo -e "\n=== 步骤5: 创建修复启动脚本 ==="
cat > "$APP_DIR/run_fixed.sh" << 'EOF'
#!/bin/bash

# TRF 修复版启动脚本
echo "TRF ARM launch script (SQLite Fixed) - $(date)"
echo "========================="

APP_DIR="/mnt/sd/TRF-********-Linux-ARM-v7l"
cd "$APP_DIR"

# 设置 Qt 插件路径
export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins"
export QML2_IMPORT_PATH="$APP_DIR/qml:/usr/lib/qt5/qml"

# 设置 SQLite 驱动路径
export QT_SQL_DRIVERS="$APP_DIR/plugins/sqldrivers"

# 设置字体和语言
export LC_ALL=C
export LANG=C

# 设置触摸屏
export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
export QT_QPA_PLATFORM="linuxfb:fb=/dev/fb0"

echo "Qt 插件路径: $QT_PLUGIN_PATH"
echo "SQLite 驱动路径: $QT_SQL_DRIVERS"

# 启动应用
echo "启动 TRF 应用..."
./trf
EOF

chmod +x "$APP_DIR/run_fixed.sh"
echo "✓ 创建修复启动脚本: run_fixed.sh"

echo -e "\n=== 修复完成 ==="
echo "请执行以下命令测试:"
echo "cd $APP_DIR && ./run_fixed.sh"
echo "============================================" 
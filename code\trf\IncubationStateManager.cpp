#include "IncubationStateManager.h"
#include <QDebug>
#include <QTimer>

IncubationStateManager* IncubationStateManager::m_instance = nullptr;

IncubationStateManager* IncubationStateManager::getInstance()
{
    if (m_instance == nullptr) {
        m_instance = new IncubationStateManager();
    }
    return m_instance;
}

IncubationStateManager::IncubationStateManager(QObject *parent)
    : QObject(parent)
    , m_isIncubationActive(false)
    , m_isAnyTestActive(false)
    , m_currentRowNumber(-1)
    , m_remainingSeconds(0)
    , m_timer(new QTimer(this))
{
    m_timer->setInterval(1000); // 1秒间隔
    connect(m_timer, &QTimer::timeout, this, &IncubationStateManager::onTimerTimeout);
    
    qDebug() << "IncubationStateManager: Global incubation state manager initialized";
}

IncubationStateManager::~IncubationStateManager()
{
    if (m_timer->isActive()) {
        m_timer->stop();
    }
}

void IncubationStateManager::startTest(const QString& projectType, int rowNumber)
{
    if (m_isAnyTestActive) {
        qDebug() << "IncubationStateManager: Test already active for project:" << m_currentProject << "row:" << m_currentRowNumber;
        return;
    }
    
    m_isAnyTestActive = true;
    m_currentProject = projectType;
    m_currentRowNumber = rowNumber;
    
    qDebug() << "IncubationStateManager: Started test for project:" << projectType << "row:" << rowNumber;
    
    emit testStarted(projectType, rowNumber);
}

void IncubationStateManager::stopTest()
{
    if (!m_isAnyTestActive) {
        return;
    }
    
    m_isAnyTestActive = false;
    QString previousProject = m_currentProject;
    int previousRow = m_currentRowNumber;
    m_currentProject.clear();
    m_currentRowNumber = -1;
    
    qDebug() << "IncubationStateManager: Stopped test for project:" << previousProject << "row:" << previousRow;
    
    emit testStopped();
}

void IncubationStateManager::startIncubation(const QString& projectType)
{
    if (m_isIncubationActive) {
        qDebug() << "IncubationStateManager: Incubation already active for project:" << m_currentProject;
        return;
    }
    
    m_isIncubationActive = true;
    m_currentProject = projectType;
    m_remainingSeconds = getIncubationTimeForProject(projectType);
    
    m_timer->start();
    
    qDebug() << "IncubationStateManager: Started incubation for project:" << projectType 
             << "duration:" << m_remainingSeconds << "seconds";
    
    emit incubationStarted(projectType);
}

void IncubationStateManager::stopIncubation()
{
    if (!m_isIncubationActive) {
        return;
    }
    
    m_isIncubationActive = false;
    QString previousProject = m_currentProject;
    m_currentProject.clear();
    m_remainingSeconds = 0;
    
    if (m_timer->isActive()) {
        m_timer->stop();
    }
    
    qDebug() << "IncubationStateManager: Stopped incubation for project:" << previousProject;
    
    emit incubationStopped();
}

int IncubationStateManager::getIncubationTimeForProject(const QString& projectType) const
{
    // CRP项目温育时间设置
    if (projectType.contains("CRP", Qt::CaseInsensitive)) {
        return 90; // 1分半，90秒
    }
    
    // 其他项目的默认温育时间
    return 60; // 1分钟
}

void IncubationStateManager::onTimerTimeout()
{
    if (!m_isIncubationActive) {
        m_timer->stop();
        return;
    }
    
    if (m_remainingSeconds > 0) {
        m_remainingSeconds--;
        emit incubationTimeUpdated(m_remainingSeconds);
        
        if (m_remainingSeconds <= 0) {
            // 温育时间结束，自动停止
            stopIncubation();
        }
    }
} 
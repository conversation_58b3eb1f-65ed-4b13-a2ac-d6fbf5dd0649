# FastModePage 实现

## 任务目标

基于 `AutoSamplePage` 创建一个新的 `FastModePage`，并进行以下调整：
- 在最左边增加一列，用于显示从1开始的序号。
- 在 "Patient, ID" 表头前增加一个新图标 (`images/fast_mode/fast_header_1.png`)。
- 重新调整表头和列的宽度，保持总宽度不变。
- "Add Sample" 按钮与 "Patient, ID" 列对齐。
- 序号列的背景色设置为深灰色。

## 计划步骤

1.  **文件创建与项目配置**
    -   复制 `autosamplepage.h` -> `fastmodepage.h`
    -   复制 `autosamplepage.cpp` -> `fastmodepage.cpp`
    -   复制 `autosamplepage.ui` -> `fastmodepage.ui`

2.  **更新 `.pro` 文件**
    -   在 `trf.pro` 文件中添加对 `fastmodepage.h`, `fastmodepage.cpp`, `fastmodepage.ui` 的引用。

3.  **修改 `fastmodepage.h`**
    -   类名改为 `FastModePage`。
    -   更新头文件保护符。
    -   `addSampleRow` 函数声明增加 `int rowNumber` 参数。

4.  **修改 `fastmodepage.cpp`**
    -   将 `AutoSamplePage` 的引用替换为 `FastModePage`。
    -   更新 `addSampleRow` 函数实现以匹配新的声明。

5.  **修改 `fastmodepage.ui`**
    -   **类名**: 改为 `FastModePage`。
    -   **新增列**: 添加序号列表头和单元格 (`QLabel`)。
    -   **新增图标**: 在 "Patient, ID" 表头中添加图标 `QLabel`。
    -   **布局调整**: 调整所有表头和数据行的 `QWidget` 和 `QLabel` 的几何属性（位置和大小）。
    -   **按钮对齐**: 调整 "Add Sample" 按钮的位置。
    -   **样式**: 为序号列添加深灰色背景样式。

6.  **在 `mainscreen` 中集成 `FastModePage`**
    -   在 `mainscreen.h` 和 `mainscreen.cpp` 中包含、实例化 `FastModePage`。
    -   将其添加到 `QStackedWidget`。
    -   实现 "Fast mode" 导航按钮的点击事件，以切换到该页面。

7.  **任务清单归档**
    -   将此计划保存到 `issues/FastModePage实现.md`。 
#include "statsamplepage.h"
#include "ui_statsamplepage.h"
#include <QWheelEvent>
#include <QScrollBar>
#include <QApplication>
#include <QTimer>
#include <QDebug>
#include <QScroller>
#include <QtMath>
#include <QFontMetrics>
#include <QShowEvent>  // Add QShowEvent include

// Add data storage related includes
#include "database/storage/FileStorage.h"
#include "database/entities/TestResult.h"
#include "database/entities/Patient.h"

// Column configuration definition - StatSample specific
const StatSamplePage::ColumnConfig StatSamplePage::PATIENT_COL = {0, 190};
const StatSamplePage::ColumnConfig StatSamplePage::PARAMETER_COL = {190, 152};
const StatSamplePage::ColumnConfig StatSamplePage::RESULT_COL = {342, 114};
const StatSamplePage::ColumnConfig StatSamplePage::DATETIME_COL = {456, 190};
const StatSamplePage::ColumnConfig StatSamplePage::TIMER_COL = {646, 114};
const StatSamplePage::ColumnConfig StatSamplePage::LOT_COL = {760, 114};
const StatSamplePage::ColumnConfig StatSamplePage::CUTOFF_COL = {874, 138};

StatSamplePage::StatSamplePage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::StatSamplePage),
    m_scrollContent(nullptr),
    m_scrollArea(nullptr),
    m_dataLoaded(false)
{
    ui->setupUi(this);
    
    // 添加版本号确保更新生效
    qDebug() << "StatSamplePage V3.2 - Elastic Bounce with Boundary Protection";
    
    // Connect the add sample button to the new signal
    connect(ui->button_add_sample, &QPushButton::clicked, this, &StatSamplePage::addSampleClicked);
    
    // Setup the scrollable table
    setupScrollableTable();
    enableMouseScrolling();
    enableTouchScrolling();
    
    // 不再预生成测试数据，等待AddSample添加
    // initializeWithTestData(); // 已移除
    // loadHundredRows(); // 已移除
    
    // 初始化空显示状态
    initializeEmptyDisplay();
}

StatSamplePage::~StatSamplePage()
{
    clearAllRows();
    delete ui;
}

void StatSamplePage::setupScrollableTable()
{
    // 获取现有的scrollArea
    m_scrollArea = ui->scrollArea;
    
    // 创建或获取滚动内容容器
    m_scrollContent = ui->scrollAreaWidgetContents;
    if (!m_scrollContent) {
        m_scrollContent = new QWidget();
        m_scrollArea->setWidget(m_scrollContent);
    }
    
    // 配置滚动区域属性
    m_scrollArea->setWidgetResizable(false);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setMouseTracking(true);
    m_scrollArea->setFocusPolicy(Qt::WheelFocus);
    
    // 配置平滑滚动
    m_scrollArea->verticalScrollBar()->setSingleStep(33); // 每次滚动半行高度
    m_scrollArea->verticalScrollBar()->setPageStep(66);   // 每页滚动一行高度
    
    // 连接滚动事件
    connect(m_scrollArea->verticalScrollBar(), &QScrollBar::valueChanged,
            this, &StatSamplePage::onScrollValueChanged);
    
    // 设置事件过滤器
    m_scrollArea->installEventFilter(this);
    m_scrollContent->installEventFilter(this);
    this->installEventFilter(this);
}

void StatSamplePage::enableMouseScrolling()
{
    // Mouse scrolling is handled by eventFilter
}

void StatSamplePage::enableTouchScrolling()
{
    // Enable kinetic scrolling for touch devices
    QScroller::grabGesture(m_scrollArea, QScroller::LeftMouseButtonGesture);
    
    // Configure scrolling properties for better touch experience
    QScrollerProperties properties = QScroller::scroller(m_scrollArea)->scrollerProperties();
    
    // 基础滚动参数 - 与AutoSample一致
    properties.setScrollMetric(QScrollerProperties::DragVelocitySmoothingFactor, 0.02);
    properties.setScrollMetric(QScrollerProperties::MinimumVelocity, 0.0);
    properties.setScrollMetric(QScrollerProperties::MaximumVelocity, 0.5);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickMaximumTime, 0.4);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickSpeedupFactor, 1.2);
    properties.setScrollMetric(QScrollerProperties::SnapPositionRatio, 0.2);
    properties.setScrollMetric(QScrollerProperties::MaximumClickThroughVelocity, 0);
    properties.setScrollMetric(QScrollerProperties::DragStartDistance, 0.001);
    properties.setScrollMetric(QScrollerProperties::MousePressEventDelay, 0.1);
    
    // 关键修复：恢复垂直弹性滚动（用户喜欢），但禁用水平
    // 这与AutoSample的成功配置一致
    properties.setScrollMetric(QScrollerProperties::HorizontalOvershootPolicy, QScrollerProperties::OvershootAlwaysOff);
    properties.setScrollMetric(QScrollerProperties::VerticalOvershootPolicy, QScrollerProperties::OvershootWhenScrollable);
    
    // 弹性参数调优 - 控制弹性范围，避免拉过头
    properties.setScrollMetric(QScrollerProperties::OvershootDragResistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootDragDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollTime, 0.4);
    
    // 设置减速参数
    properties.setScrollMetric(QScrollerProperties::DecelerationFactor, 0.85);
    properties.setScrollMetric(QScrollerProperties::FrameRate, QScrollerProperties::Fps30);
    
    // Apply the properties
    QScroller::scroller(m_scrollArea)->setScrollerProperties(properties);
    
    // 关键修复：添加状态监听，防止拉过头后数据显示不全
    connect(QScroller::scroller(m_scrollArea), &QScroller::stateChanged, 
            this, [this](QScroller::State newState) {
        if (newState == QScroller::Inactive) {
            // 滚动结束时检查并修正边界
            QScrollBar *scrollBar = m_scrollArea->verticalScrollBar();
            int currentValue = scrollBar->value();
            int minValue = scrollBar->minimum();
            int maxValue = scrollBar->maximum();
            
            // 如果超出边界，强制回到边界内
            if (currentValue < minValue) {
                scrollBar->setValue(minValue);
                qDebug() << "StatSample: Correcting upper boundary, from" << currentValue << "to" << minValue;
            } else if (currentValue > maxValue) {
                scrollBar->setValue(maxValue);
                qDebug() << "StatSample: Correcting lower boundary, from" << currentValue << "to" << maxValue;
            }
        }
    });
    
    qDebug() << "StatSample: Touch scrolling enabled with vertical elastic bounce and boundary protection";
}

void StatSamplePage::addSampleRow(const QString &patient, const QString &parameter, 
                                  const QString &result, const QString &datetime,
                                  const QString &timer, const QString &lot, const QString &cutoff)
{
    // 新数据应该添加到最上面，获取新的行号
    int newRowNumber = getNextRowNumber();
    StatSampleRowData rowData(newRowNumber, patient, parameter, result, datetime, timer, lot, cutoff);
    addSampleRow(rowData);
}

void StatSamplePage::addSampleRow(const StatSampleRowData &rowData)
{
    // 添加或更新行数据
    m_rowData[rowData.rowNumber] = rowData;
    
    // 如果UI行已存在，更新它；否则创建新的
    if (m_dynamicRows.contains(rowData.rowNumber)) {
        updateRowUI(m_dynamicRows[rowData.rowNumber], rowData);
    } else {
        StatSampleDynamicRowUI *rowUI = createDataRow(rowData);
        if (rowUI) {
            m_dynamicRows[rowData.rowNumber] = rowUI;
        }
    }
    
    // 如果添加的是实际数据（非空行），重新排列行位置和确保最少5行
    if (!rowData.patient.isEmpty() || !rowData.parameter.isEmpty() || 
        !rowData.result.isEmpty() || !rowData.datetime.isEmpty()) {
        repositionAllRows(); // 重新排列行位置（按时间倒序）
        ensureMinimumRows(); // 确保至少显示5行
    }
    
    // 更新滚动内容
    updateScrollableContent();
}

void StatSamplePage::removeRow(int rowNumber)
{
    if (!m_rowData.contains(rowNumber)) {
        return;
    }
    
    // 移除数据
    m_rowData.remove(rowNumber);
    
    // 如果有UI，销毁它
    if (m_dynamicRows.contains(rowNumber)) {
        destroyRow(m_dynamicRows[rowNumber]);
        m_dynamicRows.remove(rowNumber);
    }
    
    // 重新排列剩余行的位置
    repositionAllRows();
    updateScrollableContent();
}

void StatSamplePage::clearAllRows()
{
    // 销毁所有可见行UI
    for (auto it = m_dynamicRows.begin(); it != m_dynamicRows.end(); ++it) {
        destroyRow(it.value());
    }
    m_dynamicRows.clear();
    
    // 清除数据
    m_rowData.clear();
    
    // 更新显示
    updateScrollableContent();
}

StatSampleDynamicRowUI* StatSamplePage::createDataRow(const StatSampleRowData& data)
{
    StatSampleDynamicRowUI* rowUI = new StatSampleDynamicRowUI();
    
    // 创建行容器widget - 按照AutoSample模式
    rowUI->widget = new QWidget(ui->scrollAreaWidgetContents);
    rowUI->widget->setObjectName(QString("widget_stat_r%1").arg(data.rowNumber));
    rowUI->widget->resize(CONTENT_WIDTH, ROW_HEIGHT);
    
    // 创建各列的标签 - 按照AutoSample模式
    rowUI->patientLabel = createColumnLabel(rowUI->widget, PATIENT_COL, data.patient);
    rowUI->parameterLabel = createColumnLabel(rowUI->widget, PARAMETER_COL, data.parameter);
    rowUI->resultLabel = createColumnLabel(rowUI->widget, RESULT_COL, data.result);
    rowUI->datetimeLabel = createColumnLabel(rowUI->widget, DATETIME_COL, data.datetime);
    rowUI->timerLabel = createColumnLabel(rowUI->widget, TIMER_COL, data.timer);
    rowUI->lotLabel = createColumnLabel(rowUI->widget, LOT_COL, data.lot);
    rowUI->cutoffLabel = createColumnLabel(rowUI->widget, CUTOFF_COL, data.cutoff);
    
    // 应用样式
    applyRowStyles(rowUI);
    
    // 设置位置 - 按照AutoSample模式
    int rowIndex = getRowDisplayIndex(data.rowNumber);
    positionRow(rowUI, rowIndex);
    
    // 显示行
    rowUI->widget->show();
    
    return rowUI;
}

QLabel* StatSamplePage::createColumnLabel(QWidget *parent, const ColumnConfig &config, const QString &text)
{
    QLabel *label = new QLabel(parent);
    label->setGeometry(config.x, 0, config.width, ROW_HEIGHT);
    label->setText(text);
    
    // V3.1新增：添加文本自动换行和优化处理
    label->setWordWrap(true);                    // 启用自动换行
    label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);  // 左对齐，垂直居中
    label->setTextFormat(Qt::PlainText);         // 使用纯文本格式提高性能
    label->setTextInteractionFlags(Qt::TextSelectableByMouse); // 允许文本选择
    
    // 设置省略号策略（当文本过长时）
    QFontMetrics metrics(label->font());
    QString elidedText = metrics.elidedText(text, Qt::ElideRight, config.width - 4); // 减去4像素边距
    if (elidedText != text) {
        label->setToolTip(text); // 完整文本作为提示
    }
    
    return label;
}

void StatSamplePage::updateRowUI(StatSampleDynamicRowUI *rowUI, const StatSampleRowData &data)
{
    if (!rowUI) return;
    
    // 更新各列内容 - 按照AutoSample模式
    if (rowUI->patientLabel) rowUI->patientLabel->setText(data.patient);
    if (rowUI->parameterLabel) rowUI->parameterLabel->setText(data.parameter);
    if (rowUI->resultLabel) rowUI->resultLabel->setText(data.result);
    if (rowUI->datetimeLabel) rowUI->datetimeLabel->setText(data.datetime);
    if (rowUI->timerLabel) rowUI->timerLabel->setText(data.timer);
    if (rowUI->lotLabel) rowUI->lotLabel->setText(data.lot);
    if (rowUI->cutoffLabel) rowUI->cutoffLabel->setText(data.cutoff);
}

void StatSamplePage::positionRow(StatSampleDynamicRowUI* rowUI, int visualIndex)
{
    if (!rowUI || !rowUI->widget) return;
    
    int yPos = visualIndex * ROW_HEIGHT;
    // StatSample行从x=0开始，无需x偏移
    rowUI->widget->move(0, yPos);
}

void StatSamplePage::applyRowStyles(StatSampleDynamicRowUI* rowUI)
{
    if (!rowUI) return;
    
    // 通用样式（支持自动换行）
    QString baseStyle = "QLabel { "
                       "border: 1px solid black; "
                       "padding: 2px; "
                       "background-color: white; "
                       "color: #333333; "
                       "font-size: 10pt; "
                       "line-height: 1.2; "
                       "}";
    
    QString patientParamStyle = "QLabel { "
                               "border: 1px solid black; "
                               "padding: 2px; "
                               "background-color: white; "
                               "color: #333333; "
                               "font-size: 10pt; "
                               "line-height: 1.2; "
                               "}";
    
    QString resultStyle = "QLabel { "
                         "border: 1px solid black; "
                         "padding: 2px; "
                         "background-color: white; "
                         "color: #D32F2F; "
                         "font-weight: bold; "
                         "font-size: 10pt; "
                         "line-height: 1.2; "
                         "}";
    
    // 应用样式并启用自动换行
    if (rowUI->patientLabel) {
        rowUI->patientLabel->setStyleSheet(patientParamStyle);
        rowUI->patientLabel->setAlignment(Qt::AlignCenter);
        rowUI->patientLabel->setWordWrap(true);
    }
    
    if (rowUI->parameterLabel) {
        rowUI->parameterLabel->setStyleSheet(patientParamStyle);
        rowUI->parameterLabel->setAlignment(Qt::AlignCenter);
        rowUI->parameterLabel->setWordWrap(true);
    }
    
    if (rowUI->resultLabel) {
        rowUI->resultLabel->setStyleSheet(resultStyle);
        rowUI->resultLabel->setAlignment(Qt::AlignCenter);
        rowUI->resultLabel->setWordWrap(true);
    }
    
    if (rowUI->datetimeLabel) {
        rowUI->datetimeLabel->setStyleSheet(baseStyle);
        rowUI->datetimeLabel->setAlignment(Qt::AlignCenter);
        rowUI->datetimeLabel->setWordWrap(true);
    }
    
    if (rowUI->timerLabel) {
        rowUI->timerLabel->setStyleSheet(baseStyle);
        rowUI->timerLabel->setAlignment(Qt::AlignCenter);
        rowUI->timerLabel->setWordWrap(true);
    }
    
    if (rowUI->lotLabel) {
        rowUI->lotLabel->setStyleSheet(baseStyle);
        rowUI->lotLabel->setAlignment(Qt::AlignCenter);
        rowUI->lotLabel->setWordWrap(true);
    }
    
    if (rowUI->cutoffLabel) {
        rowUI->cutoffLabel->setStyleSheet(baseStyle);
        rowUI->cutoffLabel->setAlignment(Qt::AlignCenter);
        rowUI->cutoffLabel->setWordWrap(true);
    }
}

void StatSamplePage::destroyRow(StatSampleDynamicRowUI* rowUI)
{
    if (!rowUI) return;
    
    // 删除容器widget（会自动删除子控件）
    delete rowUI->widget;
    
    // 删除结构体
    delete rowUI;
}

int StatSamplePage::getRowDisplayIndex(int rowNumber)
{
    // 获取行的显示索引（按行号排序）
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end());
    
    return sortedRowNumbers.indexOf(rowNumber);
}

void StatSamplePage::updateVisibleRows()
{
    if (!ui->scrollArea) return;
    
    // 获取滚动区域的可见范围 - 完全按照AutoSample模式
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    int scrollTop = scrollBar->value();
    int viewportHeight = ui->scrollArea->viewport()->height();
    
    // 获取排序后的行号列表 - 关键修复：确保有数据的行排在前面，空行排在后面
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const StatSampleRowData& rowDataA = m_rowData[a];
        const StatSampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    QVector<int> newVisibleRows;
    
    if (scrollTop == 0) {
        // 特殊处理：滚动位置为0时，固定显示前5行
        for (int i = 0; i < qMin(5, sortedRowNumbers.size()); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    } else {
        // 正常的虚拟滚动逻辑
        int firstVisibleRow = qMax(0, (scrollTop / ROW_HEIGHT) - 1); 
        int visibleRowCount = qMin((viewportHeight / ROW_HEIGHT) + 3, 7);
        int lastVisibleRow = qMin(sortedRowNumbers.size() - 1, firstVisibleRow + visibleRowCount - 1);
        
        for (int i = firstVisibleRow; i <= lastVisibleRow && i < sortedRowNumbers.size(); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    }
    
    // 隐藏不再可见的行
    for (int rowNumber : m_visibleRows) {
        if (!newVisibleRows.contains(rowNumber)) {
            if (m_dynamicRows.contains(rowNumber)) {
                m_dynamicRows[rowNumber]->widget->hide();
            }
        }
    }
    
    // 显示新可见的行
    for (int rowNumber : newVisibleRows) {
        if (m_dynamicRows.contains(rowNumber)) {
            if (!m_dynamicRows[rowNumber]->widget->isVisible()) {
                m_dynamicRows[rowNumber]->widget->show();
            }
        }
    }
    
    m_visibleRows = newVisibleRows;
    
    qDebug() << "StatSample V3.1: Scroll position:" << scrollTop << "Visible rows:" << m_visibleRows.size() 
             << "out of" << m_rowData.size() << "total";
}

void StatSamplePage::updateScrollableContent()
{
    if (!m_scrollContent) return;
    
    // 计算内容总高度：行数 * 行高 - 按照AutoSample模式
    int totalRows = m_rowData.size();
    
    // 关键修复：确保至少5行的内容高度，这样向下拉拽有正确的边界
    // 这是之前成功实现的关键逻辑
    int minRows = 5;
    int effectiveRows = qMax(minRows, totalRows);
    int contentHeight = effectiveRows * ROW_HEIGHT;
    
    // 更新滚动区域内容大小
    ui->scrollAreaWidgetContents->setMinimumHeight(contentHeight);
    ui->scrollAreaWidgetContents->setMaximumHeight(contentHeight);
    ui->scrollAreaWidgetContents->resize(CONTENT_WIDTH, contentHeight);
    
    // 强制更新滚动条范围
    ui->scrollArea->updateGeometry();
    
    // 确保滚动位置不超出新的边界
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    if (scrollBar->value() > scrollBar->maximum()) {
        scrollBar->setValue(scrollBar->maximum());
    }
    
    qDebug() << "StatSample V3.1: Updated scroll content: totalRows=" << totalRows 
             << "effectiveRows=" << effectiveRows << "contentHeight=" << contentHeight 
             << "scrollRange=" << scrollBar->minimum() << "to" << scrollBar->maximum();
}

void StatSamplePage::repositionAllRows()
{
    // 重新排列所有行的位置 - 使用与updateVisibleRows相同的排序逻辑
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const StatSampleRowData& rowDataA = m_rowData[a];
        const StatSampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    for (int i = 0; i < sortedRowNumbers.size(); ++i) {
        int rowNumber = sortedRowNumbers[i];
        if (m_dynamicRows.contains(rowNumber)) {
            positionRow(m_dynamicRows[rowNumber], i);
        }
    }
}

void StatSamplePage::onScrollValueChanged(int value)
{
    Q_UNUSED(value)
    updateVisibleRows();
}

void StatSamplePage::initializeWithTestData()
{
    // 清除现有数据
    clearAllRows();
    
    // 添加基础测试数据
    addSampleRow("Johnson, Alice, 22202301030012", "hs-CRP [mg/L]", ">10.00", "2023.01.03 17:23, S", "--:--", "20220501", "<3.00");
    addSampleRow("Smith, Bob, 22202301030013", "CRP [mg/L]", "70.33", "2023.01.03 17:23, S", "--:--", "20220501", "<3.00");
    addSampleRow("Williams, Carol, 22202301030014", "hs-CRP [mg/L]", ">10.00", "2023.01.03 17:23, S", "--:--", "20220501", "<3.00");
    addSampleRow("Brown, David, 22202301030015", "CRP [mg/L]", "70.33", "2023.01.03 17:23, S", "--:--", "20220501", "<3.00");
    addSampleRow("Davis, Eve, 22202301030016", "CRP [mg/L]", "70.33", "2023.01.03 17:23, S", "--:--", "20220501", "<3.00");
}

void StatSamplePage::loadHundredRows()
{
    // 生成100行测试数据
    for (int i = 6; i <= 100; ++i) {
        QString patient = QString("Patient %1, ID %2").arg(i).arg(22202301030000 + i);
        QString parameter = (i % 2 == 0) ? "CRP [mg/L]" : "hs-CRP [mg/L]";
        QString result = QString::number((i * 1.23) + 5.0, 'f', 2);
        QString datetime = QString("2023.01.%1 %2:%3, S")
                          .arg((i % 30) + 1, 2, 10, QChar('0'))
                          .arg((i % 24), 2, 10, QChar('0'))
                          .arg((i * 7) % 60, 2, 10, QChar('0'));
        QString timer = "--:--";
        QString lot = QString("202205%1").arg((i % 10), 2, 10, QChar('0'));
        QString cutoff = "<3.00";
        
        addSampleRow(patient, parameter, result, datetime, timer, lot, cutoff);
    }
}

void StatSamplePage::initializeEmptyDisplay()
{
    // Set scroll position to 0 (top)
    ui->scrollArea->verticalScrollBar()->setValue(0);
    
    // Clear all data
    clearAllRows();
    
    // Ensure at least 5 rows are displayed (all empty rows)
    ensureMinimumRows();
    
    qDebug() << "StatSample: Initialize at least 5 rows display, all empty rows";
}

void StatSamplePage::ensureMinimumRows()
{
    const int MIN_ROWS = 5;
    
    // Count rows with data
    int dataRows = 0;
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());
    
    for (int rowNumber : sortedRowNumbers) {
        const StatSampleRowData& rowData = m_rowData[rowNumber];
        if (!rowData.patient.isEmpty() || !rowData.parameter.isEmpty() || 
            !rowData.result.isEmpty() || !rowData.datetime.isEmpty()) {
            dataRows++;
        }
    }
    
    // If data rows are less than minimum, add empty rows
    if (dataRows < MIN_ROWS) {
        int emptyRowsNeeded = MIN_ROWS - dataRows;
        
        // Find the minimum row number for adding empty rows (ensure empty rows are displayed below)
        int minRowNumber = 1;
        if (!m_rowData.isEmpty()) {
            QList<int> allRowNumbers = m_rowData.keys();
            minRowNumber = *std::min_element(allRowNumbers.begin(), allRowNumbers.end());
            if (minRowNumber > 1) {
                minRowNumber = 1; // Start adding empty rows from 1
            }
        }
        
        // Add empty rows using smaller row numbers - Key fix: directly manipulate data to avoid recursive calls to addSampleRow
        for (int i = 0; i < emptyRowsNeeded; ++i) {
            int emptyRowNumber = minRowNumber - i - 1;
            if (emptyRowNumber <= 0) {
                emptyRowNumber = -(i + 1); // Use negative row numbers to ensure they are placed at the bottom
            }
            
            if (!m_rowData.contains(emptyRowNumber)) {
                // Directly add data, don't call addSampleRow to avoid recursion
                StatSampleRowData emptyRowData(emptyRowNumber, "", "", "", "", "", "", "");
                m_rowData[emptyRowNumber] = emptyRowData;
                
                // Directly create UI
                StatSampleDynamicRowUI *rowUI = createDataRow(emptyRowData);
                if (rowUI) {
                    m_dynamicRows[emptyRowNumber] = rowUI;
                }
            }
        }
    }
    
    // Reposition all rows
    repositionAllRows();
    
    // Update scroll content
    updateScrollableContent();
    
    // Key fix: ensure visible rows are updated so empty rows are displayed correctly
    updateVisibleRows();
    
    qDebug() << "StatSample: Ensure minimum" << MIN_ROWS << "rows, data rows:" << dataRows 
             << ", total rows:" << m_rowData.size();
}

int StatSamplePage::findFirstEmptyRowOrGetNext()
{
    // Iterate through existing rows to find the first empty row
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end());
    
    for (int rowNumber : sortedRowNumbers) {
        const StatSampleRowData& rowData = m_rowData[rowNumber];
        if (rowData.patient.isEmpty() && rowData.parameter.isEmpty() && 
            rowData.result.isEmpty() && rowData.datetime.isEmpty()) {
            // Found an empty row
            qDebug() << "StatSample: Found empty row" << rowNumber << "for replacement";
            return rowNumber;
        }
    }
    
    // No empty row found, return the next row number
    int nextRowNumber = m_rowData.size() + 1;
    qDebug() << "StatSample: No empty row found, using new row number" << nextRowNumber;
    return nextRowNumber;
}

int StatSamplePage::getNextRowNumber()
{
    // Get the current maximum row number, new row number = maximum row number + 1
    // This ensures that new data has a larger row number, which will be displayed at the top in sorting
    int maxRowNumber = 0;
    for (auto it = m_rowData.constBegin(); it != m_rowData.constEnd(); ++it) {
        maxRowNumber = qMax(maxRowNumber, it.key());
    }
    return maxRowNumber + 1;
}

void StatSamplePage::loadInitialDataFromDatabase()
{
    if (m_dataLoaded) {
        qDebug() << "StatSample: Data already loaded, skipping duplicate load";
        return;
    }
    
    qDebug() << "StatSample: Starting to load initial data from database...";
    
    // Use FileStorage system
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        qDebug() << "StatSample: Failed to initialize file storage:" << storage->getLastError();
        m_dataLoaded = true;
        ensureMinimumRows();
        return;
    }
    
    // Query the last 20 test results for both AUTO_SAMPLE and STAT_SAMPLE (shared data)
    QList<TestResult> autoResults = storage->getTestResultsByMode("AUTO_SAMPLE");
    QList<TestResult> statResults = storage->getTestResultsByMode("STAT_SAMPLE");
    QList<TestResult> testResults = autoResults + statResults;
    
    // Sort by test datetime descending and limit to 20
    std::sort(testResults.begin(), testResults.end(), [](const TestResult& a, const TestResult& b) {
        return a.getTestDateTime() > b.getTestDateTime();
    });
    if (testResults.size() > 20) {
        testResults = testResults.mid(0, 20);
    }
    
    if (testResults.isEmpty()) {
        qDebug() << "StatSample: No StatSample data in the database";
        m_dataLoaded = true;
        ensureMinimumRows(); // Ensure at least 5 empty rows are displayed
        return;
    }
    
    qDebug() << "StatSample: Loaded" << testResults.size() << "test results from database";
    
    // Clear existing data
    clearAllRows();
    
    // Convert and add data (testResults are already sorted by time descending)
    int rowNumber = testResults.size(); // Start from the maximum row number to ensure the latest data is at the top
    for (const TestResult& testResult : testResults) {
        // Get patient information
        Patient patient = storage->getPatientById(testResult.getPatientId());
        
        // Convert to row data
        StatSampleRowData rowData;
        convertTestResultToRowData(testResult, patient, rowData);
        rowData.rowNumber = rowNumber--;
        
        // Add to the page
        addSampleRow(rowData);
    }
    
    // Ensure at least 5 rows are displayed
    ensureMinimumRows();
    
    // Reposition rows
    repositionAllRows();
    
    // Update scroll content
    updateScrollableContent();
    
    m_dataLoaded = true;
    qDebug() << "StatSample: Initial data loading completed";
}

void StatSamplePage::convertTestResultToRowData(const TestResult& testResult, const Patient& patient, StatSampleRowData& rowData)
{
    // Format patient information: LastName, FirstName, ID
    QString patientInfo;
    if (patient.isValid()) {
        QString lastName = patient.getLastName().isEmpty() ? "" : patient.getLastName();
        QString firstName = patient.getFirstName().isEmpty() ? "" : patient.getFirstName();
        patientInfo = QString("%1,%2,%3")
                     .arg(lastName)
                     .arg(firstName)
                     .arg(testResult.getPatientId());
    } else {
        // 显示空白而不是"Unknown,Patient"
        patientInfo = QString(",%1").arg(testResult.getPatientId());
    }
    
    // Format date and time: YYYY.MM.DD HH:MM, Sample Type
    QString datetime = testResult.getTestDateTime().toString("yyyy.MM.dd hh:mm") + 
                      ", " + testResult.getSampleType();
    
    // Set row data
    rowData.patient = patientInfo;
    rowData.parameter = testResult.getParameterType();
    rowData.result = testResult.getTestResult();
    rowData.datetime = datetime;
    rowData.timer = "--:--"; // Fixed display
    rowData.lot = testResult.getLotNumber();
    rowData.cutoff = testResult.getCutoffValue();
}

void StatSamplePage::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // Load data when the page is first displayed
    if (!m_dataLoaded) {
        qDebug() << "StatSample: Page first displayed, triggering data load";
        loadInitialDataFromDatabase();
    }
}

void StatSamplePage::refreshAfterDataClear()
{
    qDebug() << "StatSample: Received data clear signal, starting page refresh";
    
    // Reset data loading status
    m_dataLoaded = false;
    
    // Clear all UI and data
    clearAllRows();
    
    // Initialize empty display state (display 5 empty rows)
    initializeEmptyDisplay();
    
    qDebug() << "StatSample: Page refresh completed, showing empty state";
}

bool StatSamplePage::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::Wheel) {
        QWheelEvent *wheelEvent = static_cast<QWheelEvent*>(event);
        
        // Handle wheel scrolling - 采用AutoSample的双精度处理方式
        if (obj == m_scrollArea || obj == m_scrollContent || obj == this) {
            QScrollBar *scrollBar = m_scrollArea->verticalScrollBar();
            
            // Calculate scroll amount - use direct pixel delta if available
            int pixelDelta = wheelEvent->pixelDelta().y();
            int angleDelta = wheelEvent->angleDelta().y();
            
            int scrollAmount = 0;
            if (!pixelDelta) {
                // Use angle delta if pixel delta not available
                scrollAmount = -angleDelta / 8; // Convert from 1/8 degree to pixels
            } else {
                scrollAmount = -pixelDelta;
            }
            
            // Apply scrolling with bounds checking
            int currentValue = scrollBar->value();
            int newValue = currentValue + scrollAmount;
            newValue = qMax(scrollBar->minimum(), qMin(scrollBar->maximum(), newValue));
            
            // 添加调试信息
            qDebug() << "StatSample V3.1: Wheel scroll - pixelDelta:" << pixelDelta 
                     << "angleDelta:" << angleDelta << "scrollAmount:" << scrollAmount 
                     << "from:" << currentValue << "to:" << newValue;
            
            scrollBar->setValue(newValue);
            
            // Virtual scrolling will be triggered by valueChanged signal
            
            return true; // Event handled
        }
    }
    
    return QWidget::eventFilter(obj, event);
}

void StatSamplePage::generateDemoData()
{
    // This method can be used for compatibility
    // Current implementation uses initializeWithTestData and loadHundredRows
}

void StatSamplePage::testScrolling()
{
    // Commented out automatic scrolling test to allow users to see the top row
    // if (m_scrollArea && m_scrollArea->verticalScrollBar()) {
    //     int maxScroll = m_scrollArea->verticalScrollBar()->maximum();
    //     int targetValue = maxScroll / 2; // Scroll to the middle position
    //     m_scrollArea->verticalScrollBar()->setValue(targetValue);
    //     qDebug() << "StatSamplePage: Testing scroll to position" << targetValue << "Max:" << maxScroll;
    // }
} 
#ifndef DATABASECONNECTION_H
#define DATABASECONNECTION_H

#include <QtSql/QSqlDatabase>
#include <QtSql/QSqlQuery>
#include <QtSql/QSqlError>
#include <QObject>
#include <QString>
#include <QDebug>

/**
 * 数据库连接管理类
 * 负责SQLite数据库的连接管理、初始化和关闭
 * 单例模式，确保全局只有一个数据库连接
 */
class DatabaseConnection : public QObject
{
    Q_OBJECT

public:
    /**
     * 获取数据库连接实例
     * @return QSqlDatabase 数据库连接对象
     */
    static QSqlDatabase getConnection();
    
    /**
     * 初始化数据库
     * 创建数据库文件、表结构、索引等
     * @return bool 初始化是否成功
     */
    static bool initializeDatabase();
    
    /**
     * 关闭数据库连接
     */
    static void closeConnection();
    
    /**
     * 检查数据库连接状态
     * @return bool 连接是否有效
     */
    static bool isConnected();
    
    /**
     * 获取最后的错误信息
     * @return QString 错误描述
     */
    static QString getLastError();

private:
    DatabaseConnection() = default;
    ~DatabaseConnection() = default;
    
    // 禁用拷贝构造和赋值操作
    DatabaseConnection(const DatabaseConnection&) = delete;
    DatabaseConnection& operator=(const DatabaseConnection&) = delete;
    
    /**
     * 创建数据库表结构
     * @return bool 创建是否成功
     */
    static bool createTables();
    
    /**
     * 创建索引以优化查询性能
     * @return bool 创建是否成功
     */
    static bool createIndexes();
    
    /**
     * 插入初始配置数据
     * @return bool 插入是否成功
     */
    static bool insertInitialData();
    
    static QSqlDatabase m_database;
    static QString m_lastError;
    static const QString DATABASE_NAME;
    static const QString CONNECTION_NAME;
};

#endif // DATABASECONNECTION_H 
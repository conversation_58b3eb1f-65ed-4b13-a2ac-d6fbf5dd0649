#ifndef LOGINSCREEN_H
#define LOGINSCREEN_H

#include <QWidget>
#include <QTimer>

namespace Ui {
class LoginScreen;
}

class LoginScreen : public QWidget
{
    Q_OBJECT

public:
    explicit LoginScreen(QWidget *parent = nullptr);
    ~LoginScreen();

signals:
    void loginSuccessful();

private slots:
    void on_loginButton_clicked();
    void on_clearButton_clicked();
    void updateLoadingProgress();

protected:
    void resizeEvent(QResizeEvent *event) override;

private:
    Ui::LoginScreen *ui;
    QTimer *loadingTimer;
    int loadingProgress;
    
    void updateBackground();
    void startLoadingSequence();
    void showLoginInterface();
};

#endif // LOGINSCREEN_H 
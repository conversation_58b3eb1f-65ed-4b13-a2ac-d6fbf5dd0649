#include "addsamplepage.h"
#include "ui_addsamplepage.h"
#include "datepickerdialog.h"
#include "dilutionfactordialog.h"
#include "controllers/TestResultController.h"
#include "database/storage/FileStorage.h"
#include "IncubationStateManager.h"
#include <QPushButton>
#include <QComboBox>
#include <QToolButton>
#include <QIcon>
#include <QDebug>
#include <QPixmap>
#include <QList>
#include <QMessageBox>
#include <QDateTime>
#include <QTimer>

AddSamplePage::AddSamplePage(QWidget *parent, SourcePageType sourceType) :
    QWidget(parent),
    ui(new Ui::AddSamplePage),
    m_sourcePageType(sourceType),
    m_testResultController(new TestResultController(this)),
    m_waitingForTestCompletion(false)
{
    ui->setupUi(this);
    
    // 设置Gender下拉框选项
    ui->comboBox_gender->addItem("Male");
    ui->comboBox_gender->addItem("Female");
    ui->comboBox_gender->setCurrentText("Male");
    
    // 连接信号槽
    setupConnections();
    
    // 连接数据库操作信号槽
    connect(m_testResultController, &TestResultController::dataInserted,
            this, &AddSamplePage::onDataInserted);
    connect(m_testResultController, &TestResultController::insertFailed,
            this, &AddSamplePage::onInsertFailed);
            
    // 连接测试完成监听
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    connect(incubationManager, &IncubationStateManager::testStopped,
            this, &AddSamplePage::onTestCompleted);
    
    // 根据来源页面设置按钮样式
    setupButtonsForSourceType();
}

AddSamplePage::~AddSamplePage()
{
    delete ui;
}

void AddSamplePage::setupConnections()
{
    // Pre-Dil组
    connect(ui->pushButton_yes, &QPushButton::clicked, this, &AddSamplePage::onYesClicked);
    connect(ui->pushButton_no, &QPushButton::clicked, this, &AddSamplePage::onNoClicked);
    
    // Sample组
    connect(ui->pushButton_blood, &QPushButton::clicked, this, &AddSamplePage::onSampleButtonClicked);
    connect(ui->pushButton_serum, &QPushButton::clicked, this, &AddSamplePage::onSampleButtonClicked);
    connect(ui->pushButton_plasma, &QPushButton::clicked, this, &AddSamplePage::onSampleButtonClicked);
    connect(ui->pushButton_capillary, &QPushButton::clicked, this, &AddSamplePage::onSampleButtonClicked);
    
    // Parameter组 - 除了Other按钮外的所有按钮
    connect(ui->pushButton_hscrp, &QPushButton::clicked, this, &AddSamplePage::onParameterButtonClicked);
    connect(ui->pushButton_pct, &QPushButton::clicked, this, &AddSamplePage::onParameterButtonClicked);
    connect(ui->pushButton_ctni, &QPushButton::clicked, this, &AddSamplePage::onParameterButtonClicked);
    // Other按钮不连接，因为需求说除other外才显示text
    
    // 数字键盘
    connect(ui->pushButton_0, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_1, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_2, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_3, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_4, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_5, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_6, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_7, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_8, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_9, &QPushButton::clicked, this, &AddSamplePage::onNumberButtonClicked);
    connect(ui->pushButton_exit, &QPushButton::clicked, this, &AddSamplePage::onExitButtonClicked);
    
    // Insert按钮
    connect(ui->button_insert, &QPushButton::clicked, this, &AddSamplePage::onInsertButtonClicked);
    
    // Cancel button - Clear Add ID input box (Qt 5.6 compatible)
    connect(ui->button_cancel, &QPushButton::clicked, this, &AddSamplePage::onCancelClicked);
    
    // Delete button - Delete last character from Add ID input box (Qt 5.6 compatible)
    connect(ui->button_delete, &QPushButton::clicked, this, &AddSamplePage::onDeleteClicked);
    
    // Birthday输入框点击事件 - 使用事件过滤器
    ui->lineEdit_birthday->installEventFilter(this);
}

void AddSamplePage::onYesClicked()
{
    DilutionFactorDialog dialog(this);
    
    if (dialog.exec() == QDialog::Accepted) {
        QString dilutionFactor = dialog.getDilutionFactor();
        ui->lineEdit_yes_or_no->setText("Yes 1:" + dilutionFactor);
        qDebug() << "Dilution factor selected:" << dilutionFactor;
    }
    // 如果Cancel或Close，不修改输入框内容
}

void AddSamplePage::onNoClicked()
{
    ui->lineEdit_yes_or_no->setText("No");
}

void AddSamplePage::onSampleButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (button) {
        // 处理Capillary blood按钮的特殊情况（有换行符）
        QString text = button->text();
        text.replace("\n", " "); // 将换行符替换为空格
        ui->lineEdit_sample->setPlainText(text);
    }
}

void AddSamplePage::onParameterButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (button && button != ui->pushButton_other) {
        // 处理hs-CRP/CRP按钮的特殊情况（有换行符）
        QString text = button->text();
        text.replace("\n", ""); // 移除换行符
        ui->lineEdit_parameter->setText(text);
    }
}

void AddSamplePage::onNumberButtonClicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (button) {
        QString currentText = ui->lineEdit_add_id->text();
        ui->lineEdit_add_id->setText(currentText + button->text());
    }
}

void AddSamplePage::onExitButtonClicked()
{
    emit exitRequested();
}

void AddSamplePage::setupButtonsForSourceType()
{
    // 为没有背景图的按钮设置深灰色背景
    setDarkBackgroundForButtons();
    
    // 根据来源页面设置button_insert的文字和背景图
    QString insertText;
    QString insertIconPath;
    
    switch (m_sourcePageType) {
        case SourcePageType::AUTO_SAMPLE:
            insertText = "Insert\ncartridge\nfor\nincubation";
            insertIconPath = ":/images/add_sample/insert.png";
            break;
        case SourcePageType::STAT_SAMPLE:
            insertText = "Read\nincubated\ncartridge";
            insertIconPath = ":/images/add_sample/read.png";
            break;
        case SourcePageType::FAST_MODE:
            insertText = "Add test\nto Fast\nmode list";
            insertIconPath = ":/images/add_sample/fast.png";
            break;
    }
    
    ui->button_insert->setText(insertText);
    ui->bg_label_insert->setPixmap(QPixmap(insertIconPath).scaled(84, 66, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}

void AddSamplePage::setDarkBackgroundForButtons()
{
    // 为没有背景图的按钮设置深灰色背景
    QString darkButtonStyle = "QPushButton { "
                               "background-color: #666666;"
                               "border: 1px solid #555555;"
                               "border-radius: 3px;"
                               "color: white;"
                               "font-weight: bold;"
                               "padding: 0px;"
                               "margin: 1px;"
                               "}"
                               "QPushButton:hover { "
                               "background-color: #777777;"
                               "}"
                               "QPushButton:pressed { "
                               "background-color: #555555;"
                               "}";
    
    // 遍历所有QPushButton，为没有背景图的设置深灰色背景
    QList<QPushButton*> buttons = findChildren<QPushButton*>();
    for (QPushButton* button : buttons) {
        // 跳过已经有背景图的按钮
        if (button == ui->pushButton_other ||
            button == ui->pushButton_exit ||
            button == ui->button_cancel ||
            button == ui->button_delete ||
            button == ui->button_insert ||
            button == ui->pushButton_add_sample) {
            continue;
        }
        
        // 为其他按钮设置深灰色背景
        if (!button->text().isEmpty() || 
            button->objectName().contains("empty")) {
            button->setStyleSheet(darkButtonStyle);
            // 设置固定尺寸为84x66，参考数字按钮大小
            button->setFixedSize(84, 66);
        }
    }
}

void AddSamplePage::onInsertButtonClicked()
{
    qDebug() << "Insert button clicked for mode:" << static_cast<int>(m_sourcePageType);
    
    // 检查是否有测试正在进行中 - 阻止添加新用户
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    if (incubationManager->isAnyTestActive()) {
        QString projectType = incubationManager->getCurrentProject();
        QString message;
        
        if (incubationManager->isIncubationActive()) {
            int remainingTime = incubationManager->getRemainingSeconds();
            int minutes = remainingTime / 60;
            int seconds = remainingTime % 60;
            
            message = QString("温育正在进行中，无法添加新用户。\n\n当前项目：%1\n剩余时间：%2:%3\n\n请等待温育完成后再添加用户。")
                             .arg(projectType)
                             .arg(minutes)
                             .arg(seconds, 2, 10, QChar('0'));
        } else {
            message = QString("有测试正在进行中，无法添加新用户。\n\n当前项目：%1\n\n请等待测试完成后再添加用户。")
                             .arg(projectType);
        }
        
        QMessageBox::warning(this, "测试进行中", message);
        qDebug() << "Insert blocked: Test active for project:" << projectType;
        return;
    }
    
    // 收集表单数据
    AddSampleFormData formData = collectFormData();
    
    // 调用控制器处理数据插入
    bool success = m_testResultController->handleInsertButtonClick(m_sourcePageType, formData);
    
    if (!success) {
        qDebug() << "Failed to process insert request immediately";
    }
    
    // 成功/失败的反馈将通过信号槽异步处理
}

bool AddSamplePage::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == ui->lineEdit_birthday && event->type() == QEvent::MouseButtonPress) {
        onBirthdayClicked();
        return true; // 事件已处理
    }
    return QWidget::eventFilter(watched, event);
}

void AddSamplePage::onBirthdayClicked()
{
    DatePickerDialog dialog(this);
    
    // 如果输入框已有日期，尝试解析并设置为初始值
    QString currentText = ui->lineEdit_birthday->text();
    if (!currentText.isEmpty()) {
        QDate currentDate = QDate::fromString(currentText, "dd.MM.yyyy");
        if (currentDate.isValid()) {
            dialog.setSelectedDate(currentDate);
        }
    }
    
    if (dialog.exec() == QDialog::Accepted) {
        QDate selectedDate = dialog.getSelectedDate();
        ui->lineEdit_birthday->setText(selectedDate.toString("dd.MM.yyyy"));
        qDebug() << "Birthday selected:" << selectedDate.toString("dd.MM.yyyy");
    } else {
        // Cancel被点击 - 清空输入框
        ui->lineEdit_birthday->clear();
        qDebug() << "Birthday selection cancelled - text cleared";
    }
}

AddSampleFormData AddSamplePage::collectFormData()
{
    AddSampleFormData formData;
    
    // 基本信息
    formData.addId = ui->lineEdit_add_id->text().trimmed();
    formData.lastName = ui->lineEdit_last_name->text().trimmed();
    formData.firstName = ui->lineEdit_name->text().trimmed(); // UI中是lineEdit_name
    
    // 处理生日字段 - 从QString转换为QDate
    QString birthdayText = ui->lineEdit_birthday->text().trimmed();
    if (!birthdayText.isEmpty()) {
        QDate parsedDate = QDate::fromString(birthdayText, "dd.MM.yyyy");
        if (parsedDate.isValid()) {
            formData.birthday = parsedDate;
        } else {
            // 如果解析失败，设置为无效日期
            formData.birthday = QDate();
        }
    } else {
        formData.birthday = QDate();
    }
    
    formData.gender = ui->comboBox_gender->currentText();
    formData.remarks = ""; // UI中没有remark字段，设置为空
    
    // 测试参数
    formData.parameterType = ui->lineEdit_parameter->text().trimmed(); // 使用parameterType字段
    
    // 将样本全称转换为缩写
    QString sampleFullName = ui->lineEdit_sample->toPlainText().trimmed();
    formData.sampleType = convertSampleTypeToAbbreviation(sampleFullName);
    
    formData.preDilution = ui->lineEdit_yes_or_no->text().trimmed();
    
    qDebug() << "Form data collected:" 
             << "Add ID:" << formData.addId
             << "Name:" << formData.firstName << formData.lastName
             << "Birthday:" << formData.birthday.toString("dd.MM.yyyy")
             << "Parameter:" << formData.parameterType
             << "Sample:" << formData.sampleType;
    
    return formData;
}

void AddSamplePage::onDataInserted(int resultId, SourcePageType sourceType)
{
    // 从文件存储获取完整的测试结果数据
    FileStorage* storage = FileStorage::getInstance();
    QList<TestResult> allResults = storage->getAllTestResults();
    TestResult testResult;
    for (const TestResult& r : allResults) {
        if (r.getResultId() == resultId) {
            testResult = r;
            break;
        }
    }
    
    if (testResult.getResultId() <= 0) {
        qWarning() << "Cannot retrieve test result from database, ID:" << resultId;
        // 使用表单数据作为后备方案
        QString lastName = ui->lineEdit_last_name->text().trimmed();
        QString firstName = ui->lineEdit_name->text().trimmed();
        QString patientName = "";
        if (!lastName.isEmpty() || !firstName.isEmpty()) {
            patientName = QString("%1, %2").arg(lastName, firstName);
        }
        QString patientId = ui->lineEdit_add_id->text().trimmed();
        QString parameter = ui->lineEdit_parameter->text().trimmed();
        QString sampleType = convertSampleTypeToAbbreviation(ui->lineEdit_sample->toPlainText().trimmed());
        QString datetime = QDateTime::currentDateTime().toString("yyyy.MM.dd hh:mm") + ", " + sampleType;
        QString result = "Error";
        QString lot = QDate::currentDate().toString("yyyyMMdd");
        QString cutoff = "<3.00";
        
        emit dataAddedToDatabase(sourceType, patientName, patientId, parameter, sampleType, result, datetime, lot, cutoff);
        return;
    }
    
    // 从文件存储获取患者信息
    Patient patient = storage->getPatientById(testResult.getPatientId());
    
    // 构造显示格式的Patient ID字段："姓,名，ID"
    QString lastName = patient.getLastName();
    QString firstName = patient.getFirstName();
    QString addId = ui->lineEdit_add_id->text().trimmed();
    QString finalId = addId.isEmpty() ? QString::number(resultId) : addId;
    
    // Format patient display ID (using English punctuation)
    QString displayPatientId;
    // 始终使用格式"姓,名,ID"，即使姓名为空也显示逗号
    displayPatientId = QString("%1,%2,%3").arg(lastName, firstName, finalId);
    
    // 使用数据库中的实际数据
    QString parameter = testResult.getParameterType();
    QString sampleType = testResult.getSampleType();
    QString result = testResult.getTestResult(); // 这是真实的测试结果
    QString datetime = testResult.getFormattedDateTime(); // 格式化的日期时间
    QString lot = testResult.getLotNumber();
    QString cutoff = testResult.getCutoffValue();
    
    QString message;
    switch (m_sourcePageType) {
        case SourcePageType::AUTO_SAMPLE:
            message = QString("Sample successfully added to Auto mode queue!\nTest Result ID: %1").arg(resultId);
            break;
        case SourcePageType::STAT_SAMPLE:
            message = QString("Sample successfully added to STAT mode queue!\nTest Result ID: %1").arg(resultId);
            break;
        case SourcePageType::FAST_MODE:
            message = QString("Sample successfully added to Fast mode queue!\nTest Result ID: %1").arg(resultId);
            break;
    }
    
    // 发出数据添加信号，通知MainWindow更新对应页面
    QString patientName = "";
    if (!lastName.isEmpty() || !firstName.isEmpty()) {
        patientName = QString("%1, %2").arg(lastName, firstName);
    }
    emit dataAddedToDatabase(m_sourcePageType, patientName, displayPatientId, parameter, 
                            sampleType, result, datetime, lot, cutoff);
    
    // 显示成功消息
    QMessageBox::information(this, "Operation Successful", message);
    
    // 清空表单为下一次操作做准备
    ui->lineEdit_add_id->clear();
    ui->lineEdit_last_name->clear();
    ui->lineEdit_name->clear();
    ui->lineEdit_birthday->clear();
    ui->lineEdit_parameter->clear();
    ui->lineEdit_sample->clear();
    ui->lineEdit_yes_or_no->clear();
    ui->comboBox_gender->setCurrentText("Male");
    
    qDebug() << "Data insertion successful, signal emitted, form cleared";
    
    // 检查是否需要等待测试完成
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    if (incubationManager->isAnyTestActive()) {
        m_waitingForTestCompletion = true;
        qDebug() << "Waiting for test completion before exiting AddSample page";
        
        // 启动定时检查
        QTimer::singleShot(1000, this, &AddSamplePage::checkTestCompletion);
    } else {
        // 立即退出AddSample页面
        emit exitRequested();
    }
}

void AddSamplePage::onInsertFailed(const QString& error, SourcePageType sourceType)
{
    Q_UNUSED(sourceType)
    
    QString title = "Data Insert Failed";
    QString message = QString("Unable to save sample data:\n%1\n\nPlease check input information and try again.").arg(error);
    
    // Show error message
    QMessageBox::warning(this, title, message);
    
    qDebug() << "Data insertion failed:" << error;
}

QString AddSamplePage::convertSampleTypeToAbbreviation(const QString& fullName)
{
    QString trimmedName = fullName.trimmed();
    
    // 转换全称到缩写
    if (trimmedName.contains("Blood", Qt::CaseInsensitive)) {
        return "B";
    } else if (trimmedName.contains("Serum", Qt::CaseInsensitive)) {
        return "S";
    } else if (trimmedName.contains("Plasma", Qt::CaseInsensitive)) {
        return "P";
    } else if (trimmedName.contains("Capillary", Qt::CaseInsensitive)) {
        return "B"; // Capillary blood 也归类为 B（全血）
    }
    
    // If unrecognizable, return default value
    qDebug() << "Unknown sample type:" << fullName << "- using default 'S'";
    return "S"; // Default to serum
}

void AddSamplePage::onTestCompleted()
{
    if (m_waitingForTestCompletion) {
        m_waitingForTestCompletion = false;
        qDebug() << "Test completed, exiting AddSample page";
        emit exitRequested();
    }
}

void AddSamplePage::checkTestCompletion()
{
    if (!m_waitingForTestCompletion) {
        return;
    }
    
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    if (!incubationManager->isAnyTestActive()) {
        // 测试已完成
        onTestCompleted();
    } else {
        // 继续等待，1秒后再检查
        QTimer::singleShot(1000, this, &AddSamplePage::checkTestCompletion);
    }
}

void AddSamplePage::onCancelClicked()
{
    ui->lineEdit_add_id->clear();
    qDebug() << "Cancel button clicked - Add ID cleared";
}

void AddSamplePage::onDeleteClicked()
{
    QString currentText = ui->lineEdit_add_id->text();
    if (!currentText.isEmpty()) {
        currentText.chop(1); // Delete last character
        ui->lineEdit_add_id->setText(currentText);
    }
    qDebug() << "Delete button clicked - Last character removed";
} 
# 160901(1)_1_45_translate_20250526171032 - 第 7 部分

页面 31 - 35

---

## 第 31 页

HumaFIA |用户手册
33
注：仅显示参数校准卡，此处不显示标准试剂盒。
3.4 数据库接口
前几章介绍了如何在三种采样模式下收集测试结果。接下来的部分将介绍如何检索记录的结果和相关的信
息。
注：数据库非常大，并非所有数据都相关。第4.7章“ 数据库中数据字段的选择” 描述了如何设置要显示的数
据。
注：数据库非常大，并非所有数据都相关。第3.6.4章描述了如何设置要显示的数据。
数据库显示在设置/数据选择中定义的所有记录数据。
3.4.1 一般业务
通过使用向上翻页和向下翻页按钮，可以访问更多数据集。从左向右滑动屏幕可显示更多数据。
如果需要导出或删除某些数据，请首先使用以下描述的过滤器功能：
“ 导出全部” 按钮将导出通过筛选器选择的所有数据。如果未应用筛选器，则将所有数据导出到USB。
“ 删除全部” 按钮将删除通过筛选器选择的所有数据。如果未应用筛选器，则将删除所有数据。小心点！
某些字段，例如“ 打印或导出” 列中的字段，默认设置为“ 否” 。按该字段可将此设置更改为“ 是” 。
要删除完整数据集，请按患者姓名旁边的废纸篓图标。
图3-13数据库中记录结果的审查


---

## 第 32 页

HumaFIA |用户手册
34
3.4.2 数据库过滤器功能
要选择用于存储、打印、导出或比较等操作的数据集，最好只选择感兴趣的那些数据集。按“ 搜索” 可进入
以下屏幕，该屏幕显示所有可能的搜索功能/筛选：
图3-14数据库中的过滤器功能
使用过滤器，用户可以选择第一行中的日期/时间1和第二行中的日期/时间2（年、月、日、小时、分钟）之
间记录的所有样本。
此外，还有一个筛选器可以选择以下一个或多个标准。
。姓，名
患者ID
。参数
。结果标志
计时器（关于正确/错误读数时间的评论）
全部
.样品类型
。已过期或未过期的读数
。标记出超出线性范围的结果


---

## 第 33 页

HumaFIA |用户手册
35
4 高级操作
警告：以下操作仅适用于有经验的用户。如果修改设置，警告可能会消失，数据将不再显示，或临床参考值
会改变。这可能导致错误标记和结果解释。因此，建议只有在用户完全理解设置的情况下才进行更改。
4.1 设置界面
在主菜单中点击设置，进入下面的显示界面。
图4-1设置屏幕，显示了可修改的设置组
可以为以下设置组定义设置：
-测试程序：设置警报，定义如何生成ID，或是否打印每个结果
-一般：日期、时间和温度的格式，以及语言
-参数：首先显示的参数，创建自定义参数和参考编号
-标志：选择应显示的标志
-一般(2)：运行软件更新，启用自动警告
-数据选择：数据库中显示哪些数据字段
-重置出厂设置：删除所有数据和设置，以恢复出厂设置
-连接性：定义通信信道的IP地址和端口
注：通过向上滑动右侧按钮，可以找到“ 连接” 按钮。它位于“ 重置出厂设置” 按钮下方。
每个模块在下面的单独章节中进行解释。


---

## 第 34 页

HumaFIA |用户手册
36
4.2 测试程序的设置
图4-2声音报警、患者ID设置及打印
可以定义声光报警器。每个报警器都可以启用/禁用。通过在激活字段中添加一个数字，可以选择报警器的类
型：
-1表示短蜂鸣
-2表示长蜂鸣
-3声短鸣表示2次
-4表示非常长的蜂鸣声
启用患者ID功能会自动为每位新患者分配一个连续的患者ID。此外，还可以设置患者ID前几位的前缀数字。
这些数字与其余部分之间用下划线隔开。例如，如果用户设置101作为前缀数字，那么第一位患者将被分配ID
101_1，第二位患者101_2，依此类推。这有助于日后更轻松地选择和过滤数据。通过“ 每日自动重置前缀”
选项，自动递增的数字每天早上从1开始——即自动重置功能。在这个例子中，每天早晨第一位患者的ID将是
101_1。
如果启用“ 自动打印每个结果（热打印机）” ，则可在生成结果后在集成热打印机上自动打印结果。
如果已激活“ 自动打印（外部打印机）上的所有结果” ，则会激活向外部打印机打印的功能。但是，只有加
载了外部打印机的打印机驱动程序后，此选项才可用。此功能当前不可用。
退出将应用所选设置并关闭窗口。


---

## 第 35 页

HumaFIA |用户手册
37
4.3 一般（日期、时间、语言、亮度、温度、删除）
图4-3设置日期、时间、语言、亮度、温度和删除警告
日期格式可以设置为YYYY. MM. DD或DD. MM.YYYY。D代表日，M代表月，Y代表年。
时间格式可以设置为hh：mm（0-24h）或hh：mm，AM/PM。h代表小时，m代表分钟。
亮度可通过+增加或减少
如果分析仪上安装了所需语言，可以更改语言。如果需要其他语言，请要求服务工程师上传。分析仪一次只
能支持5种语言。
温度格式可以设置为摄氏度（.C）或华氏度（.F）。长按“ 温度” 标题几秒钟，会进入一个屏幕，用户可以
在此调整温度。如果屏幕上显示的温度过高，可以通过降低温度来调整；反之亦然。例如，输入-4以将温度
降低4摄氏度。按下“ 设置” 应用温度调整，然后按“ 返回” 按钮回到常规设置屏幕。
删除
。数据：通过警告确认：如果启用此选项，将显示警告以确认删除数据。
.测试/控制确认选项可启用与测试和控制的使用相关的所有警告，例如过期的试剂提示。
退出将应用所选设置并关闭窗口。


---


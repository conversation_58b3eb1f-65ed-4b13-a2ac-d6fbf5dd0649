# FastMode Timer队列管理优化

## 任务目标
实现严格的队列管理机制，确保：
- 阶段0：等待状态，不显示文字
- 同一时间最多只有一个测试处于阶段1
- 只有当前测试进入温育阶段（阶段3）时，下一个等待的测试才能进入阶段1
- 多个测试按先进先出（FIFO）顺序执行

## 修改计划

### 1. 修改startTestForRow方法
- 当前：只检查阶段3（孵育阶段）
- 改为：检查阶段1-4的任何活跃测试
- 确保同一时间只有一个测试可以离开阶段0

### 2. 修改handleTimerClick方法
- 在阶段2→3转换时立即调用startNextWaitingTest()
- 确保孵育开始的瞬间就启动下一个等待的测试

### 3. 优化startNextWaitingTest方法
- 确保按照测试添加顺序（rowNumber从小到大）启动
- 添加额外的安全检查

### 4. 测试验证
- 添加多个测试验证队列行为
- 确认阶段0显示空白但保持边框

## 实施状态
- [x] 任务创建
- [x] startTestForRow修改 - 检查阶段1-4的活跃测试
- [x] handleTimerClick修改 - 阶段2→3转换时启动下一个测试
- [x] startNextWaitingTest优化 - FIFO队列顺序，安全检查
- [ ] 功能测试

## 核心修改

### 1. startTestForRow方法
```cpp
// 检查是否有任何测试正在活跃状态（阶段1-4）
bool hasActiveTest = false;
for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
    if (it.key() != rowNumber) {
        TimerStateData::Stage stage = it.value().currentStage;
        if (stage >= TimerStateData::STAGE_1 && stage <= TimerStateData::STAGE_4) {
            hasActiveTest = true;
            break;
        }
    }
}
```

### 2. handleTimerClick方法 - 阶段2处理
```cpp
case TimerStateData::STAGE_2:
    // 阶段2点击 → 进入阶段3孵育
    timerState.currentStage = TimerStateData::STAGE_3;
    // ... 其他设置 ...
    
    // 立即启动下一个等待的测试（阶段2→3时）
    startNextWaitingTest();
    break;
```

### 3. startNextWaitingTest方法
- 双重安全检查：确保没有活跃测试才启动新测试
- FIFO队列：按行号从小到大排序
- 详细日志记录队列状态

## 技术细节
文件：`fastmodepage.cpp`, `fastmodepage.h`
关键方法：`startTestForRow`, `handleTimerClick`, `startNextWaitingTest` 
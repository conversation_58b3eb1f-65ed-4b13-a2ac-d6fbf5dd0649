# AutoSamplePage 背景色显示修复

## 问题描述
用户反映 AutoSamplePage 的背景色设置为深灰色 (#7f7f7f)，但运行时显示为浅灰色，与预期不符。

## 问题分析

### 根本原因
AutoSamplePage 被包装到 BasePage 后，出现了样式层级冲突：

1. **AutoSamplePage 设置**：`background-color: #7f7f7f;` (深灰色)
2. **BasePage 覆盖**：`contentArea->setStyleSheet("background-color: transparent;")` (透明)

### Widget 层级结构
```
BasePage
├── CommonHeader (125px)
└── contentArea (transparent) ← 问题所在
    └── AutoSamplePage (#7f7f7f) ← 被透明背景影响
```

### 显示效果
用户看到的"浅灰色"实际是：
- 系统默认窗口背景色
- 透明背景导致的混合显示效果

## 解决方案

移除 BasePage contentArea 的透明背景设置，让内容页面的背景色设置正常生效。

### 修改内容
**文件**：`code/trf/basepage.cpp`

```cpp
// 原来：强制设置透明背景
contentArea = new QWidget();
contentArea->setStyleSheet("background-color: transparent;");

// 修改后：移除透明背景设置
contentArea = new QWidget();
```

## 影响评估

### 当前使用 BasePage 的页面分析：
1. **AutoSamplePage** → ✅ **受益**：正确显示深灰色背景
2. **StatSamplePage** → ✅ **无影响**：没设置背景色，显示系统默认
3. **FastModePage** → ✅ **无影响**：没设置背景色，显示系统默认  
4. **QcModulePage** → ✅ **无影响**：空页面，无内容
5. **DatabasePage** → ✅ **无影响**：空页面，无内容
6. **SettingsPage** → ✅ **无影响**：空页面，无内容
7. **AddSamplePage** → ✅ **无影响**：没设置背景色，显示系统默认

### 安全性确认
- ✅ 无负面影响
- ✅ 只有正面改进
- ✅ 符合预期设计

## 预期效果
- ✅ AutoSamplePage 正确显示深灰色背景 #7f7f7f
- ✅ 其他页面保持正常显示
- ✅ 内容页面背景色设置得到尊重

## 状态
✅ 已完成修复 - AutoSamplePage 背景色现在正确显示 
#!/bin/bash

# TRF SQLite 驱动快速修复脚本
# 适用于 myd-y6ull14x14 系统

echo "=== TRF SQLite 快速修复 ==="

APP_DIR="/mnt/sd/TRF-1.0.0.26-Linux-ARM-v7l"

# 创建插件目录
mkdir -p "$APP_DIR/plugins/sqldrivers"

# 查找并复制 SQLite 驱动
if [ -f "/usr/lib/qt5/plugins/sqldrivers/libqsqlite.so" ]; then
    cp "/usr/lib/qt5/plugins/sqldrivers/libqsqlite.so" "$APP_DIR/plugins/sqldrivers/"
    echo "✓ SQLite 驱动已复制"
else
    echo "❌ 未找到 SQLite 驱动在标准位置"
    echo "手动查找..."
    find /usr -name "libqsqlite.so" -type f 2>/dev/null | head -1 | while read driver; do
        if [ -f "$driver" ]; then
            cp "$driver" "$APP_DIR/plugins/sqldrivers/"
            echo "✓ SQLite 驱动已从 $driver 复制"
        fi
    done
fi

# 创建简单的启动脚本
cat > "$APP_DIR/run_simple.sh" << 'EOF'
#!/bin/bash
APP_DIR="/mnt/sd/TRF-1.0.0.26-Linux-ARM-v7l"
cd "$APP_DIR"
export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins"
export LC_ALL=C
./trf
EOF

chmod +x "$APP_DIR/run_simple.sh"
echo "✓ 创建简化启动脚本"
echo ""
echo "修复完成！请运行: cd $APP_DIR && ./run_simple.sh" 
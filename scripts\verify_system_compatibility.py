#!/usr/bin/env python3
"""
TRF 系统兼容性验证脚本 - 专门验证 myd-y6ull14x14 系统匹配度
用于验证构建的TRF程序是否与目标系统精确匹配
"""

import os
import sys
import subprocess
import platform
import json
from pathlib import Path

# 目标系统规格 - 基于实际 system.txt 分析
TARGET_SYSTEM = {
    "os": "Linux",
    "hostname": "myd-y6ull14x14", 
    "kernel_version": "4.1.15+",
    "architecture": "armv7l",
    "qt_version": "5.6.2",
    "required_qt_libs": [
        "libQt5Core.so.5.6.2",
        "libQt5Gui.so.5.6.2", 
        "libQt5Widgets.so.5.6.2",
        "libQt5Network.so.5.6.2",
        "libQt5Sql.so.5.6.2",
        "libQt5Concurrent.so.5.6.2",
        "libQt5DBus.so.5.6.2",
        "libQt5Xml.so.5.6.2",
        "libQt5Test.so.5.6.2"
    ],
    "graphics_libs": [
        "libEGL.so.1",
        "libGLESv2.so.2"
    ],
    "touch_libs": [
        "libts-1.0.so.0"
    ],
    "devices": [
        "/dev/fb0",           # 帧缓冲设备
        "/dev/input/event1"   # iMX6UL TouchScreen Controller
    ]
}

def get_system_info():
    """获取当前系统信息"""
    try:
        return {
            "os": platform.system(),
            "hostname": platform.node(),
            "kernel": platform.release(),
            "architecture": platform.machine(),
            "platform": platform.platform()
        }
    except Exception as e:
        print(f"警告: 无法获取系统信息: {e}")
        return {}

def check_qt_libraries():
    """检查Qt库版本匹配度"""
    print("检查Qt库版本精确匹配...")
    score = 0
    max_score = 100
    
    try:
        # 使用ldconfig检查库
        result = subprocess.run(['ldconfig', '-p'], 
                              capture_output=True, text=True, check=False)
        
        if result.returncode != 0:
            print("  警告: ldconfig命令不可用，跳过库版本检查")
            return 50  # 给予部分分数
            
        ldconfig_output = result.stdout
        
        # 检查必需的Qt库
        found_libs = 0
        version_match = 0
        
        for lib in TARGET_SYSTEM["required_qt_libs"]:
            lib_base = lib.split('.so.')[0] + '.so.5'  # 如 libQt5Core.so.5
            
            if lib_base in ldconfig_output:
                found_libs += 1
                print(f"  ✓ 找到 {lib_base}")
                
                # 尝试检查精确版本匹配
                if "5.6." in ldconfig_output:
                    version_match += 1
                    print(f"    → 版本匹配 Qt 5.6.x")
                else:
                    print(f"    → 版本未确认或不匹配")
            else:
                print(f"  ✗ 缺失 {lib_base}")
        
        # 计算分数
        lib_score = (found_libs / len(TARGET_SYSTEM["required_qt_libs"])) * 70
        version_score = (version_match / len(TARGET_SYSTEM["required_qt_libs"])) * 30
        score = int(lib_score + version_score)
        
        print(f"  Qt库匹配度: {found_libs}/{len(TARGET_SYSTEM['required_qt_libs'])} 库找到")
        print(f"  版本匹配度: {version_match}/{len(TARGET_SYSTEM['required_qt_libs'])} 版本匹配")
        
    except Exception as e:
        print(f"  错误: Qt库检查失败: {e}")
        score = 0
        
    return score

def check_hardware_devices():
    """检查硬件设备匹配度"""
    print("检查myd-y6ull14x14硬件设备...")
    score = 0
    
    for device in TARGET_SYSTEM["devices"]:
        if os.path.exists(device):
            score += 25
            if device == "/dev/input/event1":
                print(f"  ✓ iMX6UL TouchScreen Controller {device} 存在")
            else:
                print(f"  ✓ 设备 {device} 存在")
        else:
            print(f"  ✗ 设备 {device} 不存在")
    
    return score

def check_graphics_support():
    """检查图形支持库"""
    print("检查EGL/OpenGL ES支持...")
    score = 0
    
    try:
        result = subprocess.run(['ldconfig', '-p'], 
                              capture_output=True, text=True, check=False)
        
        if result.returncode == 0:
            ldconfig_output = result.stdout
            
            for lib in TARGET_SYSTEM["graphics_libs"]:
                if lib in ldconfig_output:
                    score += 25
                    print(f"  ✓ 图形库 {lib} 存在")
                else:
                    print(f"  ✗ 图形库 {lib} 缺失")
        else:
            print("  警告: 无法检查图形库")
            
    except Exception as e:
        print(f"  错误: 图形库检查失败: {e}")
        
    return score

def main():
    """主验证函数"""
    print("=" * 60)
    print("TRF 系统兼容性验证 - myd-y6ull14x14 精确匹配检查")
    print("=" * 60)
    
    # 获取系统信息
    system_info = get_system_info()
    print(f"当前系统: {system_info.get('platform', '未知')}")
    print(f"期望系统: Linux myd-y6ull14x14 4.1.15+ armv7l Qt 5.6.2")
    print()
    
    total_score = 0
    max_total_score = 400
    
    # 架构检查
    print("1. 检查系统架构...")
    if system_info.get("architecture") == TARGET_SYSTEM["architecture"]:
        arch_score = 100
        print(f"  ✓ 架构匹配: {system_info['architecture']}")
    else:
        arch_score = 0
        print(f"  ✗ 架构不匹配: {system_info.get('architecture', '未知')} (期望: {TARGET_SYSTEM['architecture']})")
    
    total_score += arch_score
    print()
    
    # Qt库检查
    print("2. 检查Qt库版本...")
    qt_score = check_qt_libraries()
    total_score += qt_score
    print()
    
    # 硬件设备检查  
    print("3. 检查硬件设备...")
    hw_score = check_hardware_devices()
    total_score += hw_score
    print()
    
    # 图形支持检查
    print("4. 检查图形支持...")
    gfx_score = check_graphics_support()
    total_score += gfx_score
    print()
    
    # 总评
    print("=" * 60)
    print("系统匹配度总评")
    print("=" * 60)
    print(f"总分: {total_score} / {max_total_score}")
    
    percentage = (total_score / max_total_score) * 100
    
    if percentage >= 85:
        grade = "A+"
        status = "✅ 完美匹配"
        recommendation = "系统完全匹配目标，可以直接运行"
    elif percentage >= 70:
        grade = "A"
        status = "✅ 高度匹配" 
        recommendation = "系统高度兼容，强烈推荐使用"
    elif percentage >= 55:
        grade = "B"
        status = "○ 基本兼容"
        recommendation = "系统基本兼容，可以使用但可能需要调试"
    elif percentage >= 40:
        grade = "C"
        status = "△ 部分兼容"
        recommendation = "系统部分兼容，可能遇到问题"
    else:
        grade = "F"
        status = "✗ 兼容性较差"
        recommendation = "系统兼容性较差，不建议使用"
    
    print(f"匹配等级: {grade} ({percentage:.1f}%)")
    print(f"兼容状态: {status}")
    print(f"建议: {recommendation}")
    print()
    
    # 详细建议
    if percentage >= 70:
        print("✅ 这是专门为 myd-y6ull14x14 系统构建的精确匹配版本")
        print("✅ 使用 Ubuntu 16.04 + Qt 5.6.x 构建环境以确保最大兼容性")
    else:
        print("🔧 优化建议:")
        if qt_score < 70:
            print("  - 安装或更新Qt5库: apt-get install qt5-default libqt5sql5-sqlite")
        if hw_score < 50:
            print("  - 检查硬件设备配置，特别是触摸屏和帧缓冲")
        if gfx_score < 50:
            print("  - 检查EGL/OpenGL ES库安装")
    
    print()
    print("验证完成")
    
    # 返回适当的退出代码
    if percentage >= 55:
        return 0  # 成功
    else:
        return 1  # 兼容性问题

if __name__ == "__main__":
    sys.exit(main()) 
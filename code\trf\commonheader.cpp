#include "commonheader.h"
#include <QPixmap>
#include <QDebug> // Added for qDebug

CommonHeader::CommonHeader(QWidget *parent) 
    : QWidget(parent)
    , m_activeButtonIndex(-1)
    , m_navigationEnabled(true) // 初始化为启用状态
{
    setFixedSize(1024, 125); // 增加高度以适应间距
    setStyleSheet("CommonHeader { border: 2px solid #da0019; }");
    
    // Initialize button data
    m_buttonTexts[0] = "Auto sample";
    m_buttonTexts[1] = "STAT sample";
    m_buttonTexts[2] = "Fast mode";
    m_buttonTexts[3] = "QC module";
    m_buttonTexts[4] = "Database";
    m_buttonTexts[5] = "Settings";
    
    m_iconPathsInactive[0] = ":/images/common/red_sample.png";
    m_iconPathsInactive[1] = ":/images/common/red_sample.png";
    m_iconPathsInactive[2] = ":/images/common/red_fast.png";
    m_iconPathsInactive[3] = ":/images/common/red_qc.png";
    m_iconPathsInactive[4] = ":/images/common/red_database.png";
    m_iconPathsInactive[5] = ":/images/common/red_settings.png";
    
    m_iconPathsActive[0] = ":/images/common/white_sample.png";
    m_iconPathsActive[1] = ":/images/common/white_sample.png";
    m_iconPathsActive[2] = ":/images/common/white_fast.png";
    m_iconPathsActive[3] = ":/images/common/white_qc.png";
    m_iconPathsActive[4] = ":/images/common/white_database.png";
    m_iconPathsActive[5] = ":/images/common/white_settings.png";
    
    setupUi();
    
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &CommonHeader::updateDateTime);
    timer->start(1000);
    updateDateTime();
}

CommonHeader::~CommonHeader() {}

void CommonHeader::setupUi()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(5); // 增加第一排和第二排之间的间距

    // Row 1 - Logo, Temperature, DateTime
    QWidget *row1Widget = new QWidget();
    row1Widget->setFixedHeight(50);
    QHBoxLayout *row1Layout = new QHBoxLayout(row1Widget);
    row1Layout->setContentsMargins(0, 0, 0, 0);
    row1Layout->setSpacing(0);

    // Logo图片区域 - 改为可点击的按钮
    logoButton = new QPushButton();
    logoButton->setFixedSize(200, 50);
    logoButton->setFlat(true); // 去除按钮边框
    logoButton->setStyleSheet("QPushButton { "
                             "background-color: #da0019; "
                             "border: none; "
                             "padding: 0px; "
                             "} "
                             "QPushButton:hover { "
                             "background-color: #c50017; "
                             "} "
                             "QPushButton:pressed { "
                             "background-color: #a0001a; "
                             "}");
    
    QPixmap logoPixmap(":/images/logo_humafia.png");
    logoButton->setIcon(QIcon(logoPixmap));
    logoButton->setIconSize(logoPixmap.scaled(200, 50, Qt::KeepAspectRatio, Qt::SmoothTransformation).size());
    
    // 连接logo点击事件
    connect(logoButton, &QPushButton::clicked, this, &CommonHeader::onLogoClicked);
    
    // 填充区域
    QLabel* fillerLabel = new QLabel();
    fillerLabel->setFixedWidth(483);
    fillerLabel->setStyleSheet("background-color: #da0019; border: 1px solid #ccc;");
    
    // 日期时间区域
    dateTimeLabel = new QLabel();
    dateTimeLabel->setFixedWidth(256);
    dateTimeLabel->setAlignment(Qt::AlignCenter);
    dateTimeLabel->setStyleSheet("background-color: #da0019; border: 1px solid #ccc; font-size: 14px; color: white;");
    
    // 温度区域
    temperatureLabel = new QLabel("25°C");
    temperatureLabel->setFixedWidth(85);
    temperatureLabel->setAlignment(Qt::AlignCenter);
    temperatureLabel->setStyleSheet("background-color: #da0019; border: 1px solid #ccc; font-size: 14px; color: white;");

    row1Layout->addWidget(logoButton);
    row1Layout->addWidget(fillerLabel);
    row1Layout->addWidget(temperatureLabel);
    row1Layout->addWidget(dateTimeLabel);

    // Row 2 - Navigation buttons 
    QWidget *row2Widget = new QWidget();
    row2Widget->setFixedHeight(70);

    createNavButtons();
    
    // Set parent and position for all button containers - 1016px布局，左右各4px边距
    int buttonX[] = {4, 173, 342, 511, 680, 849};
    int buttonW[] = {169, 169, 169, 169, 169, 171};
    
    for (int i = 0; i < s_buttonCount; ++i) {
        m_buttonContainers[i]->setParent(row2Widget);
        // 在设置父控件后重新设置几何 - 不占满高度，给边框留空间
        m_buttonContainers[i]->setGeometry(buttonX[i], 0, buttonW[i], 70);
        m_buttonContainers[i]->show(); // Ensure visibility
    }

    mainLayout->addWidget(row1Widget);
    mainLayout->addWidget(row2Widget);
}

void CommonHeader::createNavButtons()
{
    // 直接实现分层结构（工作版本）- 1016px布局，左右各4px边距
    // int buttonX[] = {4, 173, 342, 511, 680, 849}; // 未使用，已注释
    int buttonW[] = {169, 169, 169, 169, 169, 171};
    
    for (int i = 0; i < s_buttonCount; ++i) {
        // 创建外层 QWidget 容器并设置边框
        m_buttonContainers[i] = new QWidget();
        m_buttonContainers[i]->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        m_buttonContainers[i]->setMinimumSize(buttonW[i], 70);
        m_buttonContainers[i]->setMaximumSize(buttonW[i], 70);
        
        // 给容器设置边框 - 区分边界按钮和中间按钮
        QString borderStyle;
        if (i == 0) {
            // 最左边按钮：保持左边红色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-right-color: white; }";
        } else if (i == s_buttonCount - 1) {
            // 最右边按钮：保持右边红色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-left-color: white; }";
        } else {
            // 中间按钮：左右都是白色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-left-color: white; border-right-color: white; }";
        }
        m_buttonContainers[i]->setStyleSheet(borderStyle);
        
        // 创建背景 QLabel
        m_buttonBackgrounds[i] = new QLabel(m_buttonContainers[i]);
        m_buttonBackgrounds[i]->setGeometry(0, 0, buttonW[i], 70);
        m_buttonBackgrounds[i]->setScaledContents(true);
        
        // 设置背景图片
        QPixmap pixmap(m_iconPathsInactive[i]);
        if (!pixmap.isNull()) {
            m_buttonBackgrounds[i]->setPixmap(pixmap);
        }
        
        // 创建前景 QPushButton
        m_buttons[i] = new QPushButton(m_buttonContainers[i]);
        m_buttons[i]->setGeometry(0, 0, buttonW[i], 70);
        m_buttons[i]->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
        m_buttons[i]->setMinimumSize(buttonW[i], 70);
        m_buttons[i]->setMaximumSize(buttonW[i], 70);
        m_buttons[i]->setText(m_buttonTexts[i]);
        
        // 设置按钮样式 (边框由容器提供)
        m_buttons[i]->setStyleSheet(
            "QPushButton {"
            "    background-color: transparent;"
            "    border: none;"
            "    color: white;"
            "    font: 12pt \"Arial\";"
            "    font-weight: bold;"
            "    text-align: left;"
            "    padding-left: 10px;"
            "}"
            "QPushButton:hover {"
            "    background-color: rgba(255, 255, 255, 50);"
            "}"
            "QPushButton:pressed {"
            "    background-color: rgba(0, 0, 0, 50);"
            "}"
        );
        
        // 连接点击信号
        connect(m_buttons[i], &QPushButton::clicked, [this, i](){
            onButtonClicked(i);
        });
    }
}

void CommonHeader::onButtonClicked(int index)
{
    // 如果导航被禁用，直接返回，不做任何处理
    if (!m_navigationEnabled) {
        qDebug() << "Navigation disabled - button click ignored";
        return;
    }
    
    setActiveButton(index);
    // Convert button index (0-5) to page index (1-6) for MainWindow navigation
    emit navigationClicked(index + 1);
}

void CommonHeader::updateDateTime()
{
    dateTimeLabel->setText(QDateTime::currentDateTime().toString("yyyy.MM.dd  hh:mm:ss"));
}

void CommonHeader::onLogoClicked()
{
    // 如果导航被禁用，直接返回，不做任何处理
    if (!m_navigationEnabled) {
        qDebug() << "Navigation disabled - logo click ignored";
        return;
    }
    
    qDebug() << "Logo clicked - navigating to home";
    emit homeClicked();
}

void CommonHeader::setActiveButton(int index)
{
    m_activeButtonIndex = index;
    
    for (int i = 0; i < s_buttonCount; ++i) {
        // 设置统一的边框样式 - 区分边界按钮和中间按钮
        QString borderStyle;
        if (i == 0) {
            // 最左边按钮：保持左边红色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-right-color: white; }";
        } else if (i == s_buttonCount - 1) {
            // 最右边按钮：保持右边红色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-left-color: white; }";
        } else {
            // 中间按钮：左右都是白色边框
            borderStyle = "QWidget { border: 1px solid #da0019; border-left-color: white; border-right-color: white; }";
        }
        m_buttonContainers[i]->setStyleSheet(borderStyle);
        
        if (i == index && index >= 0) {
            // Active: white background image, gray text
            QPixmap pixmap(m_iconPathsActive[i]);
            if (!pixmap.isNull()) {
                m_buttonBackgrounds[i]->setPixmap(pixmap);
            }
            m_buttons[i]->setStyleSheet(m_buttons[i]->styleSheet().replace("color: white;", "color: #666666;"));
        } else {
            // Inactive: red background image, white text
            QPixmap pixmap(m_iconPathsInactive[i]);
            if (!pixmap.isNull()) {
                m_buttonBackgrounds[i]->setPixmap(pixmap);
            }
            m_buttons[i]->setStyleSheet(m_buttons[i]->styleSheet().replace("color: #666666;", "color: white;"));
        }
    }
} 

void CommonHeader::setNavigationEnabled(bool enabled)
{
    m_navigationEnabled = enabled;
    qDebug() << "Navigation enabled:" << enabled;
} 

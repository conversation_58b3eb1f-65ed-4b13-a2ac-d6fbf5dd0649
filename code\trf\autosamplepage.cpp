#include "autosamplepage.h"
#include "ui_autosamplepage.h"
#include <QWheelEvent>
#include <QScrollBar>
#include <QApplication>
#include <QTimer>
#include <QDebug>
#include <QScroller>
#include <QElapsedTimer>
#include <QFontMetrics>
#include <algorithm>
#include <QShowEvent>  // 添加QShowEvent的include

// 添加数据存储相关includes
#include "database/storage/FileStorage.h"
#include "database/entities/TestResult.h"
#include "database/entities/Patient.h"
#include "IncubationStateManager.h"

// AutoSample列配置常量定义
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_PATIENT = {0, 190};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_PARAMETER = {190, 152};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_RESULT = {342, 114};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_DATETIME = {456, 190};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_TIMER = {646, 114};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_LOT = {760, 114};
const AutoSamplePage::AutoSampleColumnConfig AutoSamplePage::COLUMN_CUTOFF = {874, 138};

AutoSamplePage::AutoSamplePage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::AutoSamplePage),
    m_globalTimer(nullptr),
    m_dataLoaded(false)
{
    ui->setupUi(this);
    
    qDebug() << "AutoSamplePage: Initializing with Timer system support";
    
    // Connect the add sample button to the new signal
    connect(ui->button_add_sample, &QPushButton::clicked, this, &AutoSamplePage::addSampleClicked);
    
    // Setup the scrollable table
    setupScrollableTable();
    enableMouseScrolling();
    enableTouchScrolling();
    
    // 初始化Timer系统
    m_globalTimer = new QTimer(this);
    m_globalTimer->setInterval(1000); // 1秒间隔
    connect(m_globalTimer, &QTimer::timeout, this, [this]() {
        // 定期更新所有Timer状态
        for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
            int rowNumber = it.key();
            AutoTimerStateData& timerState = it.value();
            
            // 验证行是否仍然存在
            if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
                continue;
            }
            
            bool needsUpdate = false;
            
            switch (timerState.currentStage) {
                case AutoTimerStateData::STAGE_1:
                    // 阶段1: 等待板条插入，5秒后直接进入温育阶段
                    if (timerState.remainingSeconds > 0) {
                        timerState.remainingSeconds--;
                        needsUpdate = true;

                        if (timerState.remainingSeconds <= 0) {
                            // 板条插入等待完成，直接进入温育阶段（跳过阶段2）
                            timerState.currentStage = AutoTimerStateData::STAGE_3;
                            timerState.remainingSeconds = getIncubationTimeForProject(timerState.projectType);
                            timerState.incubationStartTime = QDateTime::currentDateTime();
                            timerState.isClickable = false;

                            // 启动全局温育状态 - 阻止新用户添加
                            IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
                            incubationManager->startIncubation(timerState.projectType);

                            // 进入温育时，清空cutoff和datetime显示
                            if (m_rowData.contains(rowNumber)) {
                                m_rowData[rowNumber].cutoff = "";
                                m_rowData[rowNumber].datetime = "";
                                // 更新UI显示
                                if (m_dynamicRows.contains(rowNumber)) {
                                    updateRowUI(m_dynamicRows[rowNumber], m_rowData[rowNumber]);
                                }
                            }

                            qDebug() << "AutoSample: Row" << rowNumber << "board insertion completed, directly entering incubation stage with" << timerState.remainingSeconds << "seconds";
                        }
                    }
                    break;
                    
                case AutoTimerStateData::STAGE_2:
                    // 阶段2已被跳过，直接从阶段1进入阶段3（温育）
                    // 这个case不应该被执行到
                    qWarning() << "AutoSample: Unexpected STAGE_2 execution for row" << rowNumber;
                    break;
                    
                case AutoTimerStateData::STAGE_3:
                    // 阶段3: 温育倒计时
                    if (timerState.remainingSeconds > 0) {
                        timerState.remainingSeconds--;
                        needsUpdate = true;
                        
                        if (timerState.remainingSeconds <= 0) {
                            // 自动进入阶段4（读数）
                            timerState.currentStage = AutoTimerStateData::STAGE_4;
                            timerState.remainingSeconds = 10; // 10秒读数时间
                            timerState.stage4StartTime = QDateTime::currentDateTime();
                            timerState.isClickable = false;
                            
                            // 停止全局温育状态 - 允许新用户添加
                            IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
                            incubationManager->stopIncubation();
                            
                            qDebug() << "AutoSample: Row" << rowNumber << "entering reading stage, stopped global incubation state";
                        }
                    }
                    break;
                    
                case AutoTimerStateData::STAGE_4:
                    // 阶段4: 读数倒计时
                    if (timerState.remainingSeconds > 0) {
                        timerState.remainingSeconds--;
                        needsUpdate = true;
                        
                        if (timerState.remainingSeconds <= 0) {
                            // 生成结果并进入完成状态
                            generateAndUpdateResult(rowNumber);
                            timerState.currentStage = AutoTimerStateData::STAGE_5;
                            timerState.isClickable = false;
                            
                            // 停止全局测试状态 - 允许新用户添加
                            IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
                            incubationManager->stopTest();
                            
                            qDebug() << "AutoSample: Row" << rowNumber << "test completed, stopped test state";
                        }
                    }
                    break;
                    
                default:
                    break;
            }
            
            if (needsUpdate) {
                updateTimerUI(rowNumber);
            }
        }
    });
    m_globalTimer->start();
    
    // 不再预生成测试数据，等待AddSample添加
    // loadHundredRows(); // 已移除
    
    // 初始化空显示状态
    initializeEmptyDisplay();
}

AutoSamplePage::~AutoSamplePage()
{
    // Clean up dynamic rows
    clearAllRows();
    delete ui;
}

void AutoSamplePage::setupScrollableTable()
{
    // Configure scroll area properties
    ui->scrollArea->setWidgetResizable(false);
    ui->scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    ui->scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    // Enable mouse tracking and focus
    ui->scrollArea->setMouseTracking(true);
    ui->scrollArea->setFocusPolicy(Qt::WheelFocus);
    
    // Configure smooth scrolling
    ui->scrollArea->verticalScrollBar()->setSingleStep(33); // 每次滚动半行高度
    ui->scrollArea->verticalScrollBar()->setPageStep(66);   // 每页滚动一行高度
    
    // 移除硬编码的内容大小设置，让updateScrollableContent完全控制
    // ui->scrollAreaWidgetContents->setFixedSize(1012, 338); // 已移除
    
    // Connect scroll bar value changed signal for virtual scrolling
    connect(ui->scrollArea->verticalScrollBar(), &QScrollBar::valueChanged,
            this, &AutoSamplePage::updateVisibleRows);
    
    // Force update to ensure proper scrollbar range
    ui->scrollArea->updateGeometry();
}

void AutoSamplePage::enableMouseScrolling()
{
    // Install event filter to handle mouse wheel events
    ui->scrollArea->installEventFilter(this);
    ui->scrollAreaWidgetContents->installEventFilter(this);
    this->installEventFilter(this);
    
    // Set wheel focus to enable wheel events
    ui->scrollArea->setFocusPolicy(Qt::WheelFocus);
    this->setFocusPolicy(Qt::WheelFocus);
}

void AutoSamplePage::updateScrollableContent()
{
    // 计算内容总高度：行数 * 行高
    int totalRows = m_rowData.size();
    
    // 关键修复：确保至少5行的内容高度，这样向下拉拽有正确的边界
    // 这是之前成功实现的关键逻辑
    int minRows = 5;
    int effectiveRows = qMax(minRows, totalRows);
    int contentHeight = effectiveRows * ROW_HEIGHT;
    
    // 更新滚动区域内容大小
    ui->scrollAreaWidgetContents->setMinimumHeight(contentHeight);
    ui->scrollAreaWidgetContents->setMaximumHeight(contentHeight);
    ui->scrollAreaWidgetContents->resize(CONTENT_WIDTH, contentHeight);
    
    // 强制更新滚动条范围
    ui->scrollArea->updateGeometry();
    
    // 确保滚动位置不超出新的边界
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    if (scrollBar->value() > scrollBar->maximum()) {
        scrollBar->setValue(scrollBar->maximum());
    }
    
    qDebug() << "AutoSample: Updated scroll content: totalRows=" << totalRows 
             << "effectiveRows=" << effectiveRows << "contentHeight=" << contentHeight 
             << "scrollRange=" << scrollBar->minimum() << "to" << scrollBar->maximum();
}

// 添加虚拟滚动相关方法
void AutoSamplePage::updateVisibleRows()
{
    if (!ui->scrollArea) return;
    
    // 获取滚动区域的可见范围
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    int scrollTop = scrollBar->value();
    int viewportHeight = ui->scrollArea->viewport()->height();
    
    // 获取排序后的行号列表 - 关键修复：确保有数据的行排在前面，空行排在后面
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const AutoSampleRowData& rowDataA = m_rowData[a];
        const AutoSampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    QVector<int> newVisibleRows;
    
    if (scrollTop == 0) {
        // 特殊处理：滚动位置为0时，固定显示前5行
        for (int i = 0; i < qMin(5, sortedRowNumbers.size()); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    } else {
        // 正常的虚拟滚动逻辑
        int firstVisibleRow = qMax(0, (scrollTop / ROW_HEIGHT) - 1); 
        int visibleRowCount = qMin((viewportHeight / ROW_HEIGHT) + 3, 7);
        int lastVisibleRow = qMin(sortedRowNumbers.size() - 1, firstVisibleRow + visibleRowCount - 1);
        
        for (int i = firstVisibleRow; i <= lastVisibleRow && i < sortedRowNumbers.size(); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    }
    
    // 隐藏不再可见的行
    for (int rowNumber : m_visibleRows) {
        if (!newVisibleRows.contains(rowNumber)) {
            if (m_dynamicRows.contains(rowNumber)) {
                m_dynamicRows[rowNumber]->widget->hide();
            }
        }
    }
    
    // 显示新可见的行
    for (int rowNumber : newVisibleRows) {
        if (m_dynamicRows.contains(rowNumber)) {
            if (!m_dynamicRows[rowNumber]->widget->isVisible()) {
                m_dynamicRows[rowNumber]->widget->show();
            }
        }
    }
    
    m_visibleRows = newVisibleRows;
    
    qDebug() << "AutoSample: Scroll position:" << scrollTop << "Visible rows:" << m_visibleRows.size() 
             << "out of" << m_rowData.size() << "total";
}

bool AutoSamplePage::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::Wheel) {
        QWheelEvent *wheelEvent = static_cast<QWheelEvent*>(event);
        
        // Handle wheel scrolling
        if (obj == ui->scrollArea || obj == ui->scrollAreaWidgetContents || obj == this) {
            QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
            
            // Calculate scroll amount - use direct pixel delta if available
            int pixelDelta = wheelEvent->pixelDelta().y();
            int angleDelta = wheelEvent->angleDelta().y();
            
            int scrollAmount = 0;
            if (!pixelDelta) {
                // Use angle delta if pixel delta not available
                scrollAmount = -angleDelta / 8; // Convert from 1/8 degree to pixels
            } else {
                scrollAmount = -pixelDelta;
            }
            
            // Apply scrolling with bounds checking
            int currentValue = scrollBar->value();
            int newValue = currentValue + scrollAmount;
            newValue = qMax(scrollBar->minimum(), qMin(scrollBar->maximum(), newValue));
            
            scrollBar->setValue(newValue);
            
            // Virtual scrolling will be triggered by valueChanged signal
            
            return true; // Event handled
        }
    }
    
    return QWidget::eventFilter(obj, event);
}

void AutoSamplePage::addSampleRow(const QString &patient, const QString &parameter,
                                 const QString &result, const QString &datetime,
                                 const QString &timer, const QString &lot, const QString &cutoff)
{
    // 新数据应该添加到最上面，获取新的行号
    int newRowNumber = getNextRowNumber();
    AutoSampleRowData rowData(newRowNumber, patient, parameter, result, datetime, timer, lot, cutoff);
    addSampleRow(rowData);
}

void AutoSamplePage::addSampleRow(const AutoSampleRowData &rowData)
{
    qDebug() << "AutoSample: addSampleRow called for row:" << rowData.rowNumber
             << "parameter:" << rowData.parameter << "patient:" << rowData.patient;

    // 添加或更新行数据
    m_rowData[rowData.rowNumber] = rowData;

    // 如果UI行已存在，更新它；否则创建新的
    if (m_dynamicRows.contains(rowData.rowNumber)) {
        qDebug() << "AutoSample: Updating existing UI for row:" << rowData.rowNumber;
        updateRowUI(m_dynamicRows[rowData.rowNumber], rowData);
    } else {
        qDebug() << "AutoSample: Creating new UI for row:" << rowData.rowNumber;
        AutoSampleDynamicRowUI *rowUI = createDataRow(rowData);
        if (rowUI) {
            m_dynamicRows[rowData.rowNumber] = rowUI;
            qDebug() << "AutoSample: Successfully created UI for row:" << rowData.rowNumber;
        } else {
            qDebug() << "AutoSample: Failed to create UI for row:" << rowData.rowNumber;
        }
    }
    
    // 如果添加的是实际数据（非空行），重新排列行位置和确保最少5行
    if (!rowData.patient.isEmpty() || !rowData.parameter.isEmpty() || 
        !rowData.result.isEmpty() || !rowData.datetime.isEmpty()) {
        repositionAllRows(); // 重新排列行位置（按时间倒序）
        ensureMinimumRows();
    }
    
    // 更新滚动内容
    updateScrollableContent();
}

void AutoSamplePage::removeRow(int rowNumber)
{
    // 移除数据
    if (m_rowData.contains(rowNumber)) {
        m_rowData.remove(rowNumber);
    }
    
    // 销毁UI
    if (m_dynamicRows.contains(rowNumber)) {
        destroyRow(m_dynamicRows[rowNumber]);
        m_dynamicRows.remove(rowNumber);
    }
    
    // 重新排列剩余行的位置
    repositionAllRows();
    updateScrollableContent();
}

void AutoSamplePage::clearAllRows()
{
    // 清空所有动态行
    for (auto it = m_dynamicRows.begin(); it != m_dynamicRows.end(); ++it) {
        destroyRow(it.value());
    }
    m_dynamicRows.clear();
    m_rowData.clear();
    m_visibleRows.clear();
    
    updateScrollableContent();
}

AutoSampleDynamicRowUI* AutoSamplePage::createDataRow(const AutoSampleRowData &rowData)
{
    AutoSampleDynamicRowUI *rowUI = new AutoSampleDynamicRowUI();
    
    // 创建行容器widget，使用1012宽度（无x偏移，因为AutoSample行从x=0开始）
    rowUI->widget = new QWidget(ui->scrollAreaWidgetContents);
    rowUI->widget->setObjectName(QString("widget_r%1").arg(rowData.rowNumber));
    rowUI->widget->resize(CONTENT_WIDTH, ROW_HEIGHT);
    
    // 创建各列的标签（注意：AutoSample没有number列）
    rowUI->patientLabel = createColumnLabel(rowUI->widget, COLUMN_PATIENT, rowData.patient);
    rowUI->paramLabel = createColumnLabel(rowUI->widget, COLUMN_PARAMETER, rowData.parameter);
    rowUI->resultLabel = createColumnLabel(rowUI->widget, COLUMN_RESULT, rowData.result);
    rowUI->datetimeLabel = createColumnLabel(rowUI->widget, COLUMN_DATETIME, rowData.datetime);
    
    // 创建Timer列的背景和文字标签
    rowUI->timerBgLabel = createColumnLabel(rowUI->widget, COLUMN_TIMER, "");
    rowUI->timerLabel = createColumnLabel(rowUI->widget, COLUMN_TIMER, rowData.timer);
    
    rowUI->lotLabel = createColumnLabel(rowUI->widget, COLUMN_LOT, rowData.lot);
    rowUI->cutoffLabel = createColumnLabel(rowUI->widget, COLUMN_CUTOFF, rowData.cutoff);
    
    // 应用样式
    applyRowStyles(rowUI);
    
    // 设置位置
    int rowIndex = getRowDisplayIndex(rowData.rowNumber);
    positionRow(rowUI, rowIndex);
    
    // 显示行
    rowUI->widget->show();
    
    return rowUI;
}

QLabel* AutoSamplePage::createColumnLabel(QWidget *parent, const AutoSampleColumnConfig &config, const QString &text)
{
    QLabel *label = new QLabel(parent);
    label->setGeometry(config.x, 0, config.width, ROW_HEIGHT);
    label->setText(text);
    
    // 启用自动换行和文本优化
    label->setWordWrap(true);
    label->setAlignment(Qt::AlignCenter);
    label->setTextFormat(Qt::PlainText);
    label->setTextInteractionFlags(Qt::TextSelectableByMouse);
    
    // 设置省略号策略（当文本过长时）
    QFontMetrics metrics(label->font());
    QString elidedText = metrics.elidedText(text, Qt::ElideRight, config.width - 4);
    if (elidedText != text) {
        label->setToolTip(text); // 完整文本作为提示
    }
    
    return label;
}

void AutoSamplePage::positionRow(AutoSampleDynamicRowUI *rowUI, int rowIndex)
{
    if (!rowUI || !rowUI->widget) return;
    
    int yPos = rowIndex * ROW_HEIGHT;
    // AutoSample行从x=0开始，无需x偏移
    rowUI->widget->move(0, yPos);
}

void AutoSamplePage::updateRowUI(AutoSampleDynamicRowUI *rowUI, const AutoSampleRowData &rowData)
{
    if (!rowUI) return;
    
    // 更新各列内容
    if (rowUI->patientLabel) rowUI->patientLabel->setText(rowData.patient);
    if (rowUI->paramLabel) rowUI->paramLabel->setText(rowData.parameter);
    if (rowUI->resultLabel) rowUI->resultLabel->setText(rowData.result);
    if (rowUI->datetimeLabel) rowUI->datetimeLabel->setText(rowData.datetime);
    if (rowUI->timerLabel) rowUI->timerLabel->setText(rowData.timer);
    if (rowUI->lotLabel) rowUI->lotLabel->setText(rowData.lot);
    if (rowUI->cutoffLabel) rowUI->cutoffLabel->setText(rowData.cutoff);
}

int AutoSamplePage::getRowDisplayIndex(int rowNumber)
{
    // 获取行的显示索引（按行号倒序排序，最大行号显示在最上面）
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());
    
    return sortedRowNumbers.indexOf(rowNumber);
}

void AutoSamplePage::repositionAllRows()
{
    // 重新排列所有行的位置 - 使用与updateVisibleRows相同的排序逻辑
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const AutoSampleRowData& rowDataA = m_rowData[a];
        const AutoSampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    for (int i = 0; i < sortedRowNumbers.size(); ++i) {
        int rowNumber = sortedRowNumbers[i];
        if (m_dynamicRows.contains(rowNumber)) {
            positionRow(m_dynamicRows[rowNumber], i);
        }
    }
}

const AutoSampleRowData* AutoSamplePage::getRowData(int rowNumber) const
{
    auto it = m_rowData.find(rowNumber);
    return (it != m_rowData.end()) ? &it.value() : nullptr;
}

void AutoSamplePage::loadHundredRows()
{
    // 清空现有数据
    clearAllRows();
    
    // 扩展测试数据集（与FastMode类似但适配AutoSample）
    QStringList patientNames = {
        "Musterman, Otti, 22202301030006", "Muster, Otto, 225", "Smith, John, 22202301030007",
        "Brown, Lisa, 22202301030008", "Johnson, Mary, 22202301030009", "Wilson, David, 22202301030010",
        "Garcia, Ana, 22202301030011", "Miller, Robert, 22202301030012", "Davis, Sarah, 22202301030013",
        "Thompson, Mike, 22202301030014", "Anderson, Kate, 22202301030015", "Taylor, James, 22202301030016",
        "Moore, Emma, 22202301030017", "Jackson, Alex, 22202301030018", "Martin, Lucy, 22202301030019",
        "Lee, Kevin, 22202301030020", "White, Jennifer, 22202301030021", "Harris, Michael, 22202301030022",
        "Clark, Susan, 22202301030023", "Lewis, Paul, 22202301030024", "Robinson, Nicole, 22202301030025",
        "Walker, Daniel, 22202301030026", "Hall, Amanda, 22202301030027", "Allen, Christopher, 22202301030028",
        "Young, Melissa, 22202301030029", "King, Joshua, 22202301030030"
    };
    
    QStringList parameters = {
        "hs-CRP [mg/L]", "CRP [mg/L]", "PCT [ng/mL]", "IL-6 [pg/mL]", 
        "TNF-α [pg/mL]", "ESR [mm/h]", "WBC [×10³/μL]", "Hb [g/dL]"
    };
    
    QStringList results = {
        ">10.00", "70.33", "5.45", "15.20", "8.75", "25.60", "12.30", "45.20", 
        "3.15", "18.90", "2.80", "35.50", "6.25", "42.10", "9.85", "21.30",
        "14.75", "38.60", "7.20", "29.40"
    };
    
    QStringList lots = {
        "20220501", "20220502", "20220503", "20220504", "20220505",
        "20220506", "20220507", "20220508", "20220509", "20220510"
    };
    
    QStringList cutoffs = {"<3.00", "<5.00", "<2.50", "<10.00", "<1.00"};
    
    qDebug() << "AutoSample: Starting to load 100 test data...";
    QElapsedTimer timer;
    timer.start();
    
    // 添加100行测试数据
    for (int i = 1; i <= 100; ++i) {
        QString patient = patientNames[i % patientNames.size()];
        QString parameter = parameters[i % parameters.size()];
        QString result = results[i % results.size()];
        QString datetime = QString("2023.%1.%2 %3:%4, S")
                          .arg((i % 12) + 1, 2, 10, QChar('0'))
                          .arg((i % 28) + 1, 2, 10, QChar('0'))
                          .arg((i % 24), 2, 10, QChar('0'))
                          .arg((i % 60), 2, 10, QChar('0'));
        QString lot = lots[i % lots.size()];
        QString cutoff = cutoffs[i % cutoffs.size()];
        
        AutoSampleRowData rowData(i, patient, parameter, result, datetime, "--:--", lot, cutoff);
        addSampleRow(rowData);
        
        // 每10行输出一次进度
        if (i % 10 == 0) {
            qDebug() << "AutoSample: Loaded" << i << "rows, time:" << timer.elapsed() << "ms";
        }
    }
    
    int totalTime = timer.elapsed();
    qDebug() << "AutoSample: Completed loading 100 data, total time:" << totalTime << "ms, average per row:" << (totalTime / 100.0) << "ms";
    qDebug() << "AutoSample: Actual UI rows in memory:" << m_dynamicRows.size();
    qDebug() << "AutoSample: Current visible rows:" << m_visibleRows.size();
}

void AutoSamplePage::initializeEmptyDisplay()
{
    // Set scroll position to 0 (top)
    ui->scrollArea->verticalScrollBar()->setValue(0);
    
    // Clear all data
    clearAllRows();
    
    // Ensure at least 5 rows are displayed (all empty rows)
    ensureMinimumRows();
    
    qDebug() << "AutoSample: Initialize at least 5 rows display, all empty rows";
}

void AutoSamplePage::ensureMinimumRows()
{
    const int MIN_ROWS = 5;
    
    // Count rows with data
    int dataRows = 0;
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());
    
    for (int rowNumber : sortedRowNumbers) {
        const AutoSampleRowData& rowData = m_rowData[rowNumber];
        if (!rowData.patient.isEmpty() || !rowData.parameter.isEmpty() || 
            !rowData.result.isEmpty() || !rowData.datetime.isEmpty()) {
            dataRows++;
        }
    }
    
    // If data rows are less than minimum, add empty rows
    if (dataRows < MIN_ROWS) {
        int emptyRowsNeeded = MIN_ROWS - dataRows;
        
        // Find the minimum row number for adding empty rows (ensure empty rows are displayed below)
        int minRowNumber = 1;
        if (!m_rowData.isEmpty()) {
            QList<int> allRowNumbers = m_rowData.keys();
            minRowNumber = *std::min_element(allRowNumbers.begin(), allRowNumbers.end());
            if (minRowNumber > 1) {
                minRowNumber = 1; // Start adding empty rows from 1
            }
        }
        
        // Add empty rows using smaller row numbers
        for (int i = 0; i < emptyRowsNeeded; ++i) {
            int emptyRowNumber = minRowNumber - i - 1;
            if (emptyRowNumber <= 0) {
                emptyRowNumber = -(i + 1); // Use negative row numbers to ensure they are placed at the bottom
            }
            
            if (!m_rowData.contains(emptyRowNumber)) {
                AutoSampleRowData emptyRowData(emptyRowNumber, "", "", "", "", "", "", "");
                addSampleRow(emptyRowData);
            }
        }
    }
    
    // Update scroll content
    updateScrollableContent();
    
    // Key fix: ensure visible rows are updated so empty rows are displayed correctly
    updateVisibleRows();
    
    qDebug() << "AutoSample: Ensure minimum" << MIN_ROWS << "rows, data rows:" << dataRows 
             << ", total rows:" << m_rowData.size();
}

int AutoSamplePage::findFirstEmptyRowOrGetNext()
{
    // Iterate through existing rows to find the first empty row
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end());
    
    for (int rowNumber : sortedRowNumbers) {
        const AutoSampleRowData& rowData = m_rowData[rowNumber];
        if (rowData.patient.isEmpty() && rowData.parameter.isEmpty() && 
            rowData.result.isEmpty() && rowData.datetime.isEmpty()) {
            // Found an empty row
            qDebug() << "AutoSample: Found empty row" << rowNumber << "for replacement";
            return rowNumber;
        }
    }
    
    // No empty row found, return the next row number
    int nextRowNumber = m_rowData.size() + 1;
    qDebug() << "AutoSample: No empty row found, using new row number" << nextRowNumber;
    return nextRowNumber;
} 

int AutoSamplePage::getNextRowNumber()
{
    // Get the current maximum row number, new row number = maximum row number + 1
    // This ensures that new data has a larger row number, which will be displayed at the top in sorting
    int maxRowNumber = 0;
    for (auto it = m_rowData.constBegin(); it != m_rowData.constEnd(); ++it) {
        maxRowNumber = qMax(maxRowNumber, it.key());
    }
    return maxRowNumber + 1;
}

void AutoSamplePage::loadInitialDataFromDatabase()
{
    if (m_dataLoaded) {
        qDebug() << "AutoSample: Data already loaded, skipping duplicate load";
        return;
    }
    
    qDebug() << "AutoSample: Starting to load initial data from database...";
    
    // Use FileStorage system
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        qDebug() << "AutoSample: Failed to initialize file storage:" << storage->getLastError();
        m_dataLoaded = true;
        ensureMinimumRows();
        return;
    }
    
    // Query the last 20 test results for both AUTO_SAMPLE and STAT_SAMPLE (shared data)
    QList<TestResult> autoResults = storage->getTestResultsByMode("AUTO_SAMPLE");
    QList<TestResult> statResults = storage->getTestResultsByMode("STAT_SAMPLE");
    QList<TestResult> testResults = autoResults + statResults;
    
    // Sort by test datetime descending and limit to 20
    std::sort(testResults.begin(), testResults.end(), [](const TestResult& a, const TestResult& b) {
        return a.getTestDateTime() > b.getTestDateTime();
    });
    if (testResults.size() > 20) {
        testResults = testResults.mid(0, 20);
    }
    
    if (testResults.isEmpty()) {
        qDebug() << "AutoSample: No AutoSample data in the database";
        m_dataLoaded = true;
        ensureMinimumRows(); // Ensure at least 5 empty rows are displayed
        return;
    }
    
    qDebug() << "AutoSample: Loaded" << testResults.size() << "test results from database";
    
    // Clear existing data
    clearAllRows();
    
    // Convert and add data (testResults are already sorted by time descending)
    int rowNumber = testResults.size(); // Start from the maximum row number to ensure the latest data is at the top
    for (const TestResult& testResult : testResults) {
        // Get patient information
        Patient patient = storage->getPatientById(testResult.getPatientId());
        
        // Convert to row data
        AutoSampleRowData rowData;
        convertTestResultToRowData(testResult, patient, rowData);
        rowData.rowNumber = rowNumber--;
        
        // Add to the page
        addSampleRow(rowData);
    }
    
    // Ensure at least 5 rows are displayed
    ensureMinimumRows();
    
    // Reposition rows
    repositionAllRows();
    
    // Update scroll content
    updateScrollableContent();
    
    m_dataLoaded = true;
    qDebug() << "AutoSample: Initial data loading completed";
}

void AutoSamplePage::convertTestResultToRowData(const TestResult& testResult, const Patient& patient, AutoSampleRowData& rowData)
{
    // 设置数据库记录ID，用于删除操作
    rowData.resultId = testResult.getResultId();
    
    // Format patient information: LastName, FirstName, ID
    QString patientInfo;
    if (patient.isValid()) {
        QString lastName = patient.getLastName().isEmpty() ? "" : patient.getLastName();
        QString firstName = patient.getFirstName().isEmpty() ? "" : patient.getFirstName();
        patientInfo = QString("%1,%2,%3")
                     .arg(lastName)
                     .arg(firstName)
                     .arg(testResult.getPatientId());
    } else {
        // 显示空白而不是"Unknown,Patient"
        patientInfo = QString(",%1").arg(testResult.getPatientId());
    }
    
    // Format date and time: YYYY.MM.DD HH:MM, Sample Type
    QString datetime = testResult.getTestDateTime().toString("yyyy.MM.dd hh:mm") + 
                      ", " + testResult.getSampleType();
    
    // Set row data
    rowData.patient = patientInfo;
    rowData.parameter = testResult.getParameterType();
    rowData.result = testResult.getTestResult();
    rowData.datetime = datetime;
    rowData.timer = "--:--"; // Fixed display
    rowData.lot = testResult.getLotNumber();
    rowData.cutoff = testResult.getCutoffValue();
}

void AutoSamplePage::enableTouchScrolling()
{
    // Enable kinetic scrolling for touch devices
    QScroller::grabGesture(ui->scrollArea, QScroller::LeftMouseButtonGesture);
    
    // Configure scrolling properties for better touch experience
    QScrollerProperties properties = QScroller::scroller(ui->scrollArea)->scrollerProperties();
    
    // Completely disable elastic scrolling to solve the "pulling too far" problem
    properties.setScrollMetric(QScrollerProperties::DragVelocitySmoothingFactor, 0.02);
    properties.setScrollMetric(QScrollerProperties::MinimumVelocity, 0.0);
    properties.setScrollMetric(QScrollerProperties::MaximumVelocity, 0.5);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickMaximumTime, 0.4);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickSpeedupFactor, 1.2);
    properties.setScrollMetric(QScrollerProperties::SnapPositionRatio, 0.2);
    properties.setScrollMetric(QScrollerProperties::MaximumClickThroughVelocity, 0);
    properties.setScrollMetric(QScrollerProperties::DragStartDistance, 0.001);
    properties.setScrollMetric(QScrollerProperties::MousePressEventDelay, 0.1);
    
    // Key fix: restore vertical elastic scrolling, but disable horizontal
    properties.setScrollMetric(QScrollerProperties::HorizontalOvershootPolicy, QScrollerProperties::OvershootAlwaysOff);
    properties.setScrollMetric(QScrollerProperties::VerticalOvershootPolicy, QScrollerProperties::OvershootWhenScrollable);
    
    // Elastic parameter tuning - control elastic range to avoid pulling too far
    properties.setScrollMetric(QScrollerProperties::OvershootDragResistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootDragDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollTime, 0.4);
    
    // Set deceleration parameters
    properties.setScrollMetric(QScrollerProperties::DecelerationFactor, 0.85);
    properties.setScrollMetric(QScrollerProperties::FrameRate, QScrollerProperties::Fps30);
    
    // Apply the properties
    QScroller::scroller(ui->scrollArea)->setScrollerProperties(properties);
    
    // Key fix: add state listener to prevent data display issues after pulling too far
    connect(QScroller::scroller(ui->scrollArea), &QScroller::stateChanged, 
            this, [this](QScroller::State newState) {
        if (newState == QScroller::Inactive) {
            // Check and correct boundaries when scrolling ends
            QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
            int currentValue = scrollBar->value();
            int minValue = scrollBar->minimum();
            int maxValue = scrollBar->maximum();
            
            // If out of bounds, force back to boundaries
            if (currentValue < minValue) {
                scrollBar->setValue(minValue);
                qDebug() << "AutoSample: Corrected upper boundary, from" << currentValue << "to" << minValue;
            } else if (currentValue > maxValue) {
                scrollBar->setValue(maxValue);
                qDebug() << "AutoSample: Corrected lower boundary, from" << currentValue << "to" << maxValue;
            }
        }
    });
    
    // No longer need custom boundary checks as elastic effect is disabled
    qDebug() << "AutoSample: Touch scrolling enabled with vertical elastic bounce and boundary protection";
} 

void AutoSamplePage::applyRowStyles(AutoSampleDynamicRowUI *rowUI)
{
    if (!rowUI) return;
    
    // Apply patient and parameter column styles (supports line breaks)
    QString patientParamStyle = 
        "border: 1px solid black; "
        "padding: 2px; "
        "background-color: white; "
        "color: #333333; "
        "font-size: 10pt; "
        "qproperty-alignment: AlignCenter; "
        "qproperty-wordWrap: true;";
        
    if (rowUI->patientLabel) {
        rowUI->patientLabel->setStyleSheet(patientParamStyle);
        rowUI->patientLabel->setWordWrap(true);
    }
    
    if (rowUI->paramLabel) {
        rowUI->paramLabel->setStyleSheet(patientParamStyle);
        rowUI->paramLabel->setWordWrap(true);
    }
    
    // Apply result column style (red font, supports auto-wrap)
    if (rowUI->resultLabel) {
        rowUI->resultLabel->setStyleSheet(
            "border: 1px solid black; "
            "padding: 2px; "
            "background-color: white; "
            "color: #D32F2F; "
            "font-weight: bold; "
            "font-size: 10pt; "
            "qproperty-alignment: AlignCenter; "
            "qproperty-wordWrap: true;"
        );
        rowUI->resultLabel->setWordWrap(true);
    }
    
    // Apply general column styles (supports auto-wrap)
    QString generalStyle = 
        "border: 1px solid black; "
        "padding: 2px; "
        "background-color: white; "
        "color: #333333; "
        "font-size: 10pt; "
        "qproperty-alignment: AlignCenter; "
        "qproperty-wordWrap: true;";
    
    if (rowUI->datetimeLabel) {
        rowUI->datetimeLabel->setStyleSheet(generalStyle);
        rowUI->datetimeLabel->setWordWrap(true);
    }
    if (rowUI->timerLabel) {
        rowUI->timerLabel->setStyleSheet(generalStyle);
        rowUI->timerLabel->setWordWrap(true);
    }
    if (rowUI->lotLabel) {
        rowUI->lotLabel->setStyleSheet(generalStyle);
        rowUI->lotLabel->setWordWrap(true);
    }
    if (rowUI->cutoffLabel) {
        rowUI->cutoffLabel->setStyleSheet(generalStyle);
        rowUI->cutoffLabel->setWordWrap(true);
    }
}

void AutoSamplePage::destroyRow(AutoSampleDynamicRowUI *rowUI)
{
    if (!rowUI) return;
    
    // Delete all labels
    delete rowUI->patientLabel;
    delete rowUI->paramLabel;
    delete rowUI->resultLabel;
    delete rowUI->datetimeLabel;
    delete rowUI->timerBgLabel;
    delete rowUI->timerLabel;
    delete rowUI->lotLabel;
    delete rowUI->cutoffLabel;
    
    // Delete container widget
    delete rowUI->widget;
    
    // Delete structure
    delete rowUI;
} 

void AutoSamplePage::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // Load data when the page is first displayed
    if (!m_dataLoaded) {
        qDebug() << "AutoSample: Page first displayed, triggering data load";
        loadInitialDataFromDatabase();
    }
}

void AutoSamplePage::refreshAfterDataClear()
{
    qDebug() << "AutoSample: Received data clear signal, starting page refresh";
    
    // Reset data loading state
    m_dataLoaded = false;
    
    // Clear all UI and data
    clearAllRows();
    
    // Initialize empty display state (display 5 empty rows)
    initializeEmptyDisplay();
    
    qDebug() << "AutoSample: Page refresh completed, showing empty state";
}

// === Timer相关方法实现 ===

void AutoSamplePage::startTestForRow(int rowNumber)
{
    // 检查行数据是否存在
    if (!m_rowData.contains(rowNumber)) {
        qDebug() << "AutoSample: Row" << rowNumber << "data does not exist, cannot start test";
        return;
    }

    AutoSampleDynamicRowUI* rowUI = m_dynamicRows.value(rowNumber);
    if (!rowUI || !rowUI->timerLabel) {
        qDebug() << "AutoSample: Row" << rowNumber << "UI components do not exist, cannot start test";
        return;
    }

    AutoTimerStateData& timerState = m_timerStates[rowNumber];

    // 获取行数据中的项目类型和样本类型
    const AutoSampleRowData& rowData = m_rowData[rowNumber];
    QString projectType = rowData.parameter;  // 参数类型作为项目类型
    QString sampleType = rowData.datetime;    // 从datetime字段提取样本类型

    // 提取样本类型（从datetime字段的最后部分）
    if (sampleType.contains(",")) {
        QStringList parts = sampleType.split(",");
        if (parts.size() > 1) {
            sampleType = parts.last().trimmed();
        }
    }

    // 保存项目和样本类型到Timer状态
    timerState.projectType = projectType;
    timerState.sampleType = sampleType;

    // 开始阶段1：等待板条插入
    timerState.currentStage = AutoTimerStateData::STAGE_1;
    timerState.remainingSeconds = 5; // 5秒等待板条插入
    timerState.isClickable = false;

    // 注册测试开始状态 - 阻止新用户添加
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    incubationManager->startTest(projectType, rowNumber);

    qDebug() << "AutoSample: Row" << rowNumber << "starting test flow - stage 1 (Insert Strip), registered test state";
    updateTimerUI(rowNumber);
}

void AutoSamplePage::updateTimerUI(int rowNumber)
{
    if (!m_timerStates.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
        return;
    }

    AutoTimerStateData& timerState = m_timerStates[rowNumber];
    AutoSampleDynamicRowUI* rowUI = m_dynamicRows[rowNumber];

    if (!rowUI->timerLabel) {
        return;
    }

    QString text;
    QString bgStyle;
    QString textStyle;
    QString imagePath;

    switch (timerState.currentStage) {
        case AutoTimerStateData::STAGE_0:
            text = "";
            bgStyle = "QLabel { background-color: transparent; border: none; }";
            textStyle = "QLabel { background-color: transparent; border: none; }";
            break;

        case AutoTimerStateData::STAGE_1:
            text = "Insert Strip";
            bgStyle = "QLabel { border: 1px solid black; padding: 2px; }";
            textStyle = "QLabel { background-color: transparent; border: none; color: white; font-size: 9pt; font-weight: bold; text-align: center; }";
            imagePath = ":/images/auto_sample/add_sample.png";
            break;

        case AutoTimerStateData::STAGE_2:
            // 阶段2已被跳过，不应该显示
            text = "Error";
            bgStyle = "QLabel { background-color: red; border: none; }";
            textStyle = "QLabel { background-color: transparent; border: none; color: white; font-size: 9pt; font-weight: bold; text-align: center; }";
            break;

        case AutoTimerStateData::STAGE_3:
            {
                // 温育倒计时
                int minutes = timerState.remainingSeconds / 60;
                int seconds = timerState.remainingSeconds % 60;
                text = QString("Incubation\n-%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));

                bgStyle = "QLabel { border: 1px solid black; padding: 2px; }";
                textStyle = "QLabel { background-color: transparent; border: none; color: black; font-size: 10pt; font-weight: bold; text-align: center; }";
                imagePath = ":/images/fast_mode/incubation.png";
                qDebug() << "AutoSample: STAGE_3 incubation - remaining:" << timerState.remainingSeconds << "seconds, image:" << imagePath;
            }
            break;

        case AutoTimerStateData::STAGE_4:
            text = QString("Read\n-%1").arg(timerState.remainingSeconds, 2, 10, QChar('0'));
            bgStyle = "QLabel { border: 1px solid black; padding: 2px; }";
            textStyle = "QLabel { background-color: transparent; border: none; color: white; font-size: 10pt; font-weight: bold; text-align: center; }";
            imagePath = ":/images/fast_mode/read.png";
            break;

        case AutoTimerStateData::STAGE_5:
            text = "--:--";
            // 完全照抄FastMode的STAGE_5样式
            bgStyle = "QLabel { "
                     "border: 1px solid black; "
                     "padding: 2px; "
                     "background-color: #E0E0E0; "
                     "}";
            textStyle = "QLabel { "
                       "background-color: transparent; "
                       "border: none; "
                       "color: #666666; "
                       "font-size: 10pt; "
                       "font-weight: bold; "
                       "text-align: center; "
                       "}";
            imagePath = ""; // No background image, use gray background
            break;

        case AutoTimerStateData::STAGE_WAITING:
            text = "Waiting";
            bgStyle = "QLabel { border: 1px solid black; padding: 2px; }";
            textStyle = "QLabel { background-color: transparent; border: none; color: white; font-size: 9pt; font-weight: bold; text-align: center; }";
            imagePath = ":/images/auto_sample/add_sample.png";
            break;

        default:
            text = "";
            bgStyle = "QLabel { background-color: transparent; border: none; }";
            textStyle = "QLabel { background-color: transparent; border: none; }";
            break;
    }

    // 应用背景图和样式 - 使用像FastMode一样的setPixmap方式
    if (rowUI->timerBgLabel) {
        rowUI->timerBgLabel->setStyleSheet(bgStyle);
        
        // 修复图片显示 - 对于Qt资源文件，不使用QFile::exists检查
        if (!imagePath.isEmpty()) {
            qDebug() << "AutoSample: Loading image:" << imagePath;
            qDebug() << "AutoSample: timerBgLabel size:" << rowUI->timerBgLabel->size();

            QPixmap pixmap(imagePath);
            if (!pixmap.isNull()) {
                qDebug() << "AutoSample: Image loaded, original size:" << pixmap.size();
                // Scale image to fit label size, maintaining aspect ratio
                QPixmap scaledPixmap = pixmap.scaled(rowUI->timerBgLabel->size(),
                                                   Qt::KeepAspectRatioByExpanding,
                                                   Qt::SmoothTransformation);
                rowUI->timerBgLabel->setPixmap(scaledPixmap);
                qDebug() << "AutoSample: Successfully set background image" << imagePath << "scaled size:" << scaledPixmap.size();
            } else {
                qDebug() << "AutoSample: Could not load image" << imagePath << "- pixmap is null";
                rowUI->timerBgLabel->clear(); // Clear background image
            }
        } else {
            qDebug() << "AutoSample: No image path, clearing background";
            rowUI->timerBgLabel->clear(); // Clear background image
        }
    }

    // 应用文字样式和内容
    rowUI->timerLabel->setStyleSheet(textStyle);
    rowUI->timerLabel->setText(text);
}

void AutoSamplePage::generateAndUpdateResult(int rowNumber)
{
    AutoSampleDynamicRowUI* rowUI = m_dynamicRows.value(rowNumber);
    if (!rowUI || !rowUI->resultLabel) {
        return;
    }

    const AutoSampleRowData& rowData = m_rowData[rowNumber];
    QString projectType = rowData.parameter;

    // 检查是否是CRP项目，需要生成双行结果
    qDebug() << "AutoSample: Checking project type for dual results:" << projectType;
    if (projectType.contains("CRP", Qt::CaseInsensitive) || projectType.contains("hs-CRP", Qt::CaseInsensitive)) {
        qDebug() << "AutoSample: Creating CRP dual results for project:" << projectType;
        createCRPDualResults(rowNumber);
    } else {
        // 其他项目生成单行结果
        static bool seeded = false;
        if (!seeded) {
            srand(static_cast<uint>(QDateTime::currentMSecsSinceEpoch() % 1000));
            seeded = true;
        }

        double randomValue = (rand() % 1000) / 100.0; // 0-10.00 range
        QString result = QString::number(15.25 + randomValue, 'f', 2);

        // 生成结果时间 - 格式：YYYY.MM.DD HH:MM, B
        QString resultDateTime = QDateTime::currentDateTime().toString("yyyy.MM.dd hh:mm") + ", B";

        // 更新UI和数据
        rowUI->resultLabel->setText(result);
        if (m_rowData.contains(rowNumber)) {
            m_rowData[rowNumber].result = result;
            m_rowData[rowNumber].datetime = resultDateTime; // 更新结果时间
            // 更新UI中的datetime显示
            if (rowUI->datetimeLabel) {
                rowUI->datetimeLabel->setText(resultDateTime);
            }
        }

        qDebug() << "AutoSample: Row" << rowNumber << "generated result:" << result;
    }
}

void AutoSamplePage::createCRPDualResults(int rowNumber)
{
    qDebug() << "AutoSample: Creating CRP dual results for row" << rowNumber;
    
    // 获取原始行数据
    if (!m_rowData.contains(rowNumber)) {
        return;
    }
    
    const AutoSampleRowData& originalData = m_rowData[rowNumber];
    
    // 生成两个结果值
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<uint>(QDateTime::currentMSecsSinceEpoch() % 1000));
        seeded = true;
    }
    
    // hs-CRP结果 (通常较低)
    double hsCrpValue = (rand() % 500) / 100.0; // 0-5.00 range
    QString hsCrpResult = QString::number(hsCrpValue, 'f', 2);
    
    // CRP结果 (通常较高)
    double crpValue = 10.0 + (rand() % 6000) / 100.0; // 10.00-70.00 range
    QString crpResult = QString::number(crpValue, 'f', 2);
    
    // 检查结果是否在范围内并决定颜色
    bool hsCrpInRange = isResultInRange(hsCrpResult, "<3.0");
    bool crpInRange = isResultInRange(crpResult, "<10.0");

    // 获取一个新的行号用于第二行
    int newRowNumber = getNextRowNumber();

    // 生成结果时间 - 格式：YYYY.MM.DD HH:MM, B
    QString resultDateTime = QDateTime::currentDateTime().toString("yyyy.MM.dd hh:mm") + ", B";

    // 修改原始行为hs-CRP（保持原行号，显示在上面）
    AutoSampleRowData hsCrpData = originalData; // 保持原始用户数据
    hsCrpData.parameter = "hs-CRP [mg/L]";      // 只修改参数类型
    hsCrpData.result = hsCrpResult;             // 只修改结果
    hsCrpData.cutoff = "<3.0";                 // hs-CRP的cutoff
    hsCrpData.timer = "--:--";                 // 设置完成状态
    hsCrpData.datetime = resultDateTime;       // 设置结果时间

    // 创建新的CRP行数据（使用新行号，显示在下面）
    AutoSampleRowData crpData = originalData;   // 保持原始用户数据
    crpData.rowNumber = newRowNumber;           // 使用新行号
    crpData.parameter = "CRP [mg/L]";           // 只修改参数类型
    crpData.result = crpResult;                 // 只修改结果
    crpData.cutoff = "<10.0";                  // CRP的cutoff
    crpData.timer = "--:--";                   // 设置完成状态
    crpData.datetime = resultDateTime;         // 设置结果时间

    // 更新原始行为hs-CRP
    m_rowData[rowNumber] = hsCrpData;

    // 设置原始行的timer状态为STAGE_5（完成状态）
    if (m_timerStates.contains(rowNumber)) {
        m_timerStates[rowNumber].currentStage = AutoTimerStateData::STAGE_5;
        m_timerStates[rowNumber].isClickable = false;
    }

    if (m_dynamicRows.contains(rowNumber)) {
        updateRowUI(m_dynamicRows[rowNumber], hsCrpData);
        // 确保datetime UI也更新了
        if (m_dynamicRows[rowNumber]->datetimeLabel) {
            m_dynamicRows[rowNumber]->datetimeLabel->setText(resultDateTime);
        }
        // 更新timer显示为完成状态
        updateTimerUI(rowNumber);
    }
    
    // 添加新的CRP行
    qDebug() << "AutoSample: Adding CRP row with number:" << crpData.rowNumber
             << "parameter:" << crpData.parameter << "result:" << crpData.result;
    addSampleRow(crpData);

    qDebug() << "AutoSample: Updated hs-CRP row with number:" << hsCrpData.rowNumber
             << "parameter:" << hsCrpData.parameter << "result:" << hsCrpData.result;

    // 停止全局测试状态 - 允许新用户添加（CRP双行结果完成）
    IncubationStateManager* incubationManager = IncubationStateManager::getInstance();
    incubationManager->stopTest();
    qDebug() << "AutoSample: CRP dual results completed, stopped test state";
    
    // 应用结果颜色
    // hs-CRP行（原始行）
    if (m_dynamicRows.contains(rowNumber)) {
        AutoSampleDynamicRowUI* hsCrpUI = m_dynamicRows[rowNumber];
        if (hsCrpUI && hsCrpUI->resultLabel) {
            QString colorStyle = hsCrpInRange ? "color: black;" : "color: red;";
            hsCrpUI->resultLabel->setStyleSheet(hsCrpUI->resultLabel->styleSheet() + colorStyle);
        }
    }

    // CRP行（新行）
    if (m_dynamicRows.contains(crpData.rowNumber)) {
        AutoSampleDynamicRowUI* crpUI = m_dynamicRows[crpData.rowNumber];
        if (crpUI && crpUI->resultLabel) {
            QString colorStyle = crpInRange ? "color: black;" : "color: red;";
            crpUI->resultLabel->setStyleSheet(crpUI->resultLabel->styleSheet() + colorStyle);
        }
    }
    
    // 重新排列所有行
    repositionAllRows();
    updateScrollableContent();
    
    qDebug() << "AutoSample: CRP dual results created - hs-CRP:" << hsCrpResult 
             << "CRP:" << crpResult << "hs-CRP in range:" << hsCrpInRange 
             << "CRP in range:" << crpInRange;
}

bool AutoSamplePage::isResultInRange(const QString& result, const QString& cutoff)
{
    // 解析结果值
    bool ok;
    double resultValue = result.toDouble(&ok);
    if (!ok) {
        return false;
    }
    
    // 解析cutoff值
    if (cutoff.startsWith("<")) {
        QString cutoffStr = cutoff.mid(1); // 去掉"<"符号
        double cutoffValue = cutoffStr.toDouble(&ok);
        if (ok) {
            return resultValue < cutoffValue;
        }
    }
    
    // 默认返回false（异常范围，显示红色）
    return false;
}

int AutoSamplePage::getIncubationTimeForProject(const QString& projectType) const
{
    // CRP项目温育时间设置
    if (projectType.contains("CRP", Qt::CaseInsensitive)) {
        return 90; // 1分半，90秒
    }
    
    // 其他项目的默认温育时间
    return 60; // 1分钟
}

bool AutoSamplePage::shouldSkipStages01ForProject(const QString& projectType, const QString& sampleType) const
{
    Q_UNUSED(projectType)
    Q_UNUSED(sampleType)
    // CRP项目不跳过任何阶段，需要完整流程
    return false;
}

bool AutoSamplePage::hasCRPInIncubation() const
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        const AutoTimerStateData& timerState = it.value();
        
        if (timerState.currentStage == AutoTimerStateData::STAGE_3 && 
            timerState.projectType.contains("CRP", Qt::CaseInsensitive)) {
            return true;
        }
    }
    return false;
}

void AutoSamplePage::convertStage2ToWaiting(int excludeRowNumber)
{
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        AutoTimerStateData& timerState = it.value();
        
        if (rowNumber != excludeRowNumber && timerState.currentStage == AutoTimerStateData::STAGE_2) {
            timerState.currentStage = AutoTimerStateData::STAGE_WAITING;
            timerState.remainingSeconds = 0;
            timerState.isClickable = false;
            updateTimerUI(rowNumber);
            qDebug() << "AutoSample: Row" << rowNumber << "converted to WAITING state";
        }
    }
}

void AutoSamplePage::restoreWaitingToStage2()
{
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        AutoTimerStateData& timerState = it.value();
        
        if (timerState.currentStage == AutoTimerStateData::STAGE_WAITING) {
            timerState.currentStage = AutoTimerStateData::STAGE_2;
            timerState.remainingSeconds = getIncubationTimeForProject(timerState.projectType);
            timerState.isClickable = true;
            updateTimerUI(rowNumber);
            qDebug() << "AutoSample: Row" << rowNumber << "restored from WAITING to stage 2";
        }
    }
}

bool AutoSamplePage::hasTestsInPreIncubationStages() const
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        const AutoTimerStateData& timerState = it.value();
        
        if (timerState.currentStage == AutoTimerStateData::STAGE_1 || 
            timerState.currentStage == AutoTimerStateData::STAGE_2) {
            return true;
        }
    }
    return false;
}

int AutoSamplePage::getNextWaitingRow() const
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        const AutoTimerStateData& timerState = it.value();
        
        if (timerState.currentStage == AutoTimerStateData::STAGE_0) {
            return rowNumber;
        }
    }
    return -1;
}

void AutoSamplePage::startNextWaitingTest()
{
    int waitingRow = getNextWaitingRow();
    if (waitingRow > 0) {
        startTestForRow(waitingRow);
        qDebug() << "AutoSample: Started next waiting test for row" << waitingRow;
    }
}

void AutoSamplePage::cleanupInvalidTimerStates()
{
    QList<int> invalidRows;
    
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            invalidRows.append(rowNumber);
        }
    }
    
    for (int rowNumber : invalidRows) {
        m_timerStates.remove(rowNumber);
        qDebug() << "AutoSample: Cleaned invalid Timer state for row" << rowNumber;
    }
    
    if (!invalidRows.isEmpty()) {
        qDebug() << "AutoSample: Cleaned" << invalidRows.size() << "invalid Timer states";
    }
}

void AutoSamplePage::handleTimerClick(int rowNumber)
{
    if (!m_timerStates.contains(rowNumber)) {
        return;
    }

    AutoTimerStateData& timerState = m_timerStates[rowNumber];
    
    switch (timerState.currentStage) {
        case AutoTimerStateData::STAGE_2:
            // 阶段2点击：开始温育
            timerState.currentStage = AutoTimerStateData::STAGE_3;
            timerState.remainingSeconds = getIncubationTimeForProject(timerState.projectType);
            timerState.incubationStartTime = QDateTime::currentDateTime();
            timerState.isClickable = false;
            qDebug() << "AutoSample: Row" << rowNumber << "clicked in stage 2, starting incubation";
            break;
            
        default:
            qDebug() << "AutoSample: Row" << rowNumber << "clicked in non-clickable stage" << timerState.currentStage;
            break;
    }
    
    updateTimerUI(rowNumber);
}

void AutoSamplePage::addNewSample(const QString &patient, const QString &parameter,
                                  const QString &result, const QString &datetime,
                                  const QString &timer, const QString &lot, const QString &cutoff)
{
    // 获取新的行号，确保新数据显示在最上面
    int newRowNumber = getNextRowNumber();
    
    // 创建样本数据
    AutoSampleRowData rowData(newRowNumber, patient, parameter, result, datetime, timer, lot, cutoff);
    
    // 添加行数据
    addSampleRow(rowData);
    
    // 重新排列所有行的位置
    repositionAllRows();
    
    // 确保至少显示5行
    ensureMinimumRows();
    
    // 自动启动新添加行的Timer测试流程
    qDebug() << "AutoSample: Starting timer flow for new row" << newRowNumber;
    startTestForRow(newRowNumber);
    
    // 启动全局Timer（如果还没有启动）
    if (!m_globalTimer->isActive()) {
        m_globalTimer->start();
        qDebug() << "AutoSample: Global timer started for row" << newRowNumber;
    }
    
    // 验证Timer状态
    if (m_timerStates.contains(newRowNumber)) {
        qDebug() << "AutoSample: Row" << newRowNumber << "Timer state set to stage:" << m_timerStates[newRowNumber].currentStage;
    }
    
    qDebug() << "AutoSample: Added new sample and started timer flow, row number" << newRowNumber << ", patient:" << patient;
} 
# AddSamplePage三种模式实现

## 需求概述
实现AddSamplePage的三种模式切换功能：
1. AutoSamplePage调用 → toolButton_insert显示"Insert cartridge for incubation"，背景insert.png
2. STAT sample调用 → toolButton_insert显示"Read incubated cartridge"，背景read.png  
3. Fast mode调用 → toolButton_insert显示"Add test to Fast mode list"，背景fast.png

其他按钮配置：
- toolButton_cancel文字"Insert"，背景cancel.png
- toolButton_delete文字"Delete"，背景delete.png
- pushButton_other背景other.png
- pushButton_exit背景exit.png

## 实施方案
采用枚举+参数传递方案，使用现有的按钮+背景图实现模式。

## 实施计划
1. 添加图片资源到resources.qrc
2. 定义SourcePageType枚举
3. 修改AddSamplePage类接口 
4. 实现按钮样式和图片设置
5. 实现模式化点击逻辑
6. 修改调用方代码
7. 更新UI文件按钮文字

## 执行状态
- [x] 步骤1：添加资源文件定义
- [x] 步骤2：定义模式枚举类型  
- [x] 步骤3：修改类接口
- [x] 步骤4：实现按钮样式和图片设置
- [x] 步骤5：实现模式化点击逻辑
- [x] 步骤6：修改调用方代码
- [ ] 步骤7：更新UI文件按钮文字

## 已完成的修改
1. 在resources.qrc中添加了add_sample目录下的所有图片资源
2. 在addsamplepage.h中定义了SourcePageType枚举
3. 修改了AddSamplePage构造函数支持源页面类型参数
4. 实现了setupButtonsForSourceType()方法设置按钮背景图和文字
5. 实现了onInsertButtonClicked()方法根据源页面执行不同逻辑
6. 修改了MainWindow支持动态创建带参数的AddSamplePage
7. 为StatSamplePage添加了addSampleClicked信号和连接

## 待完成
- FastModePage需要添加Add Sample按钮（目前没有）
- UI文件中的按钮文字需要更新

## 最新修复
- 修复了pushButton_other_2应为pushButton_other的错误
- 移除了QToolButton不支持的setWordWrap调用
- 改为使用CSS background-image方式设置按钮背景图，保持悬停效果
- 设置ToolButton为TextOnly模式以显示文字
- 为所有按钮添加了color和font-weight样式保证文字显示
- 添加了background-size属性确保图片正确覆盖按钮区域(81px x 91px)
- 添加了background-blend-mode实现更好的悬停效果
- 在UI文件中直接设置ToolButton为TextOnly模式和正确文字
- 调整了字体大小以适应按钮区域

## 重构为Widget+QLabel+QPushButton方案
- 将toolButton_cancel、toolButton_delete、toolButton_insert改为Widget容器方案
- 每个按钮使用QLabel作为背景图，QPushButton作为前景文字和交互
- 确保悬停效果正常工作
- 简化了setupButtonsForSourceType方法，只处理动态变化的按钮
- 使用QPixmap.scaled()确保背景图正确显示

## 最新优化
- 为button_insert添加了wordWrap属性支持文字自动换行
- 修复了other和exit按钮的悬停效果，添加了background-blend-mode和background-size
- 实现了setDarkBackgroundForButtons()方法为没有背景图的按钮设置深灰色背景
- 深灰色按钮包含悬停和按下状态的颜色变化

## 界面细节优化
- 将exit按钮也改为Widget+QLabel+QPushButton结构
- 调整insert按钮字体大小为7pt并添加padding和text-align
- 为cancel和delete按钮添加了点击事件处理
- 在跳过深灰色背景的按钮列表中添加了pushButton_exit
- 为insert按钮文字使用换行符(\n)实现手动换行

## 最终优化调整
- 按用户要求调整insert按钮文字按单词换行，以cartridge长度为基准
- 为第一行所有header标签添加右对齐属性
- 为深灰色按钮设置固定尺寸84x66像素，与数字按钮保持一致
- insert按钮字体大小调整为6pt 
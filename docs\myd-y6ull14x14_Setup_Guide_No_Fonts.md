# myd-y6ull14x14 Environment Setup Guide (No Fonts Required)

## 🎯 System Requirements
- **Hardware**: myd-y6ull14x14 ARMv7l embedded board
- **OS**: Linux 4.1.15+ armv7l 
- **Qt**: 5.6.2 (as confirmed by system check)
- **Language**: English interface only (no Chinese font dependency)

## 📦 Required Packages

### 1. Qt5 Runtime Libraries (Should already be installed)
```bash
# Core Qt5 libraries (verified present)
libqt5core5a
libqt5gui5
libqt5widgets5
libqt5sql5
```

### 2. **CRITICAL: SQLite Qt Driver**
```bash
# Install Qt5 SQLite driver (REQUIRED for database functionality)
apt-get update
apt-get install libqt5sql5-sqlite

# Alternative if above fails:
apt-get install qt5-default
```

### 3. Touch Screen Support (Should already be installed)
```bash
# Touch library (verified present)
libts-1.0.so.0
```

## 🔧 Quick Installation Script

Create and run this script on your myd-y6ull14x14 system:

```bash
#!/bin/bash
# TRF Environment Setup for myd-y6ull14x14

echo "=== Installing TRF Dependencies ==="

# Update package list
echo "Updating package list..."
apt-get update

# Install Qt5 SQLite driver (critical for TRF database)
echo "Installing Qt5 SQLite driver..."
apt-get install -y libqt5sql5-sqlite

# Verify installation
echo "=== Verification ==="
if ldconfig -p | grep -q "libqt5sql5-sqlite"; then
    echo "✅ Qt5 SQLite driver installed successfully"
else
    echo "❌ Qt5 SQLite driver installation failed"
    echo "Trying alternative installation..."
    apt-get install -y qt5-default
fi

# Check available Qt SQL drivers
echo "Available Qt SQL drivers:"
find /usr/lib -name "*qsql*" 2>/dev/null || echo "No Qt SQL drivers found in /usr/lib"

echo "=== Setup Complete ==="
echo "You can now run ./run.sh to start TRF"
```

## ✅ Verification Steps

### 1. Run TRF System Check
```bash
./check.sh
```
Expected result: Score 97-100/100 (A+ grade)

### 2. Check Qt SQL Drivers
```bash
# Check if SQLite driver is available
ls -la /usr/lib/*/qt5/plugins/sqldrivers/ 2>/dev/null
# or
find /usr -name "*qsqlite*" 2>/dev/null
```

### 3. Test TRF Launch
```bash
./run.sh
```
Expected: No "QSQLITE driver not loaded" error

## 🎯 TRF Configuration Summary

### ✅ What's Working (Verified):
- System compatibility (97/100 A+)
- Qt version match (5.6.2)
- Architecture compatibility (ARMv7l)
- Touch screen support
- Font system (English-only, no external fonts)
- Resource files loading
- Application startup

### ⚠️ What Needs Fixing:
- Qt SQLite driver installation

### 🚀 Expected Result After Fix:
- Complete TRF functionality
- Database operations working
- Patient/test data management
- All three sampling modes operational

---
**Last Updated**: 2025-01-18  
**TRF Version**: 1.0.0.26+ (English-only, font-free)  
**Target System**: myd-y6ull14x14 ARMv7l Qt 5.6.2 
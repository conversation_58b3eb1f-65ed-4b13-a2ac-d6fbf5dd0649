#include "DatabaseConnection.h"
#include <QStandardPaths>
#include <QDir>
#include <QCoreApplication>
#include <QtSql/QSqlQuery>
#include <QtSql/QSqlError>
#include <QSqlDriver>

// 静态成员变量定义
QSqlDatabase DatabaseConnection::m_database;
QString DatabaseConnection::m_lastError;
const QString DatabaseConnection::DATABASE_NAME = "humafia_system.db";
const QString DatabaseConnection::CONNECTION_NAME = "HumaFIAConnection";

QSqlDatabase DatabaseConnection::getConnection()
{
    if (!m_database.isValid()) {
        // 检查可用的SQL驱动
        QStringList availableDrivers = QSqlDatabase::drivers();
        qDebug() << "Available SQL Drivers:" << availableDrivers;
        
#ifdef QT6_SQLITE_BUILTIN
        qDebug() << "Build configuration: Qt 6.x with built-in SQLite support";
#elif defined(QT5_SQLITE_BUILTIN)
        qDebug() << "Build configuration: Qt 5.x with built-in SQLite support";
#elif defined(SQLITE_BUILTIN_SUPPORT)
        qDebug() << "Build configuration: Generic Qt built-in SQLite support";
#else
        qDebug() << "Build configuration: Standard SQLite detection";
#endif
        
        if (!availableDrivers.contains("QSQLITE")) {
            m_lastError = "SQLite driver not available in this Qt build";
            qCritical() << m_lastError;
            qCritical() << "Available drivers:" << availableDrivers;
            
#ifdef SQLITE_BUILTIN_SUPPORT
            qCritical() << "This should not happen - Qt should include built-in SQLite support";
            qCritical() << "Check Qt installation and SQL module availability";
#ifdef Q_OS_WIN
            qCritical() << "Windows: Verify Qt installation includes SQL module";
#elif defined(Q_OS_LINUX)  
            qCritical() << "Linux: Install package: apt-get install libqt5sql5-sqlite";
#endif
#else
            qCritical() << "SQLite support may not be available in this Qt build";
            qCritical() << "For Windows: ensure Qt was built with SQLite support";
            qCritical() << "For Linux ARM: install libqt5sql5-sqlite package";
#endif
            return QSqlDatabase();
        }
        
        // 创建SQLite数据库连接
        m_database = QSqlDatabase::addDatabase("QSQLITE", CONNECTION_NAME);
        
        // 设置数据库文件路径（应用程序目录下）
        QString dbPath = QCoreApplication::applicationDirPath() + "/" + DATABASE_NAME;
        m_database.setDatabaseName(dbPath);
        
        qDebug() << "Database path:" << dbPath;
        
        // 检查数据库驱动是否正确加载
        if (!m_database.driver()) {
            m_lastError = "Failed to load SQLite driver";
            qCritical() << m_lastError;
            qCritical() << "Driver loading failed despite QSQLITE being in driver list";
            qCritical() << "This may indicate a Qt plugin or library issue";
            return QSqlDatabase();
        }
        
        qDebug() << "SQLite driver loaded successfully";
    }
    
    if (!m_database.isOpen()) {
        if (!m_database.open()) {
            m_lastError = QString("Failed to open database: %1").arg(m_database.lastError().text());
            qCritical() << m_lastError;
            
            // 提供更详细的错误信息
            QSqlError error = m_database.lastError();
            qCritical() << "Error type:" << error.type();
            qCritical() << "Error text:" << error.text();
            qCritical() << "Database error:" << error.databaseText();
            qCritical() << "Driver error:" << error.driverText();
            
            // 提供平台特定的建议
#ifdef Q_OS_WIN
            qCritical() << "Windows troubleshooting:";
            qCritical() << "1. Check if Qt SQL module is properly installed";
            qCritical() << "2. Verify SQLite DLL is available";
            qCritical() << "3. Check application directory permissions";
#elif defined(Q_OS_LINUX)
            qCritical() << "Linux troubleshooting:";
            qCritical() << "1. Install SQLite package: apt-get install libqt5sql5-sqlite";
            qCritical() << "2. Check file system permissions";
            qCritical() << "3. Verify Qt installation completeness";
#endif
            
            return QSqlDatabase();
        }
        qDebug() << "Database connection opened successfully";
    }
    
    return m_database;
}

bool DatabaseConnection::initializeDatabase()
{
    qDebug() << "Initializing HumaFIA database...";
    
    // 获取数据库连接
    QSqlDatabase db = getConnection();
    if (!db.isValid() || !db.isOpen()) {
        m_lastError = "Cannot establish database connection";
        qCritical() << m_lastError;
        return false;
    }
    
    // 开始事务
    if (!db.transaction()) {
        m_lastError = QString("Failed to start transaction: %1").arg(db.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    
    // 创建表结构
    if (!createTables()) {
        db.rollback();
        return false;
    }

    // 创建索引
    if (!createIndexes()) {
        db.rollback();
        return false;
    }

    // 插入初始数据
    if (!insertInitialData()) {
        db.rollback();
        return false;
    }

    // 提交事务
    if (!db.commit()) {
        m_lastError = QString("Failed to commit transaction: %1").arg(db.lastError().text());
        qCritical() << m_lastError;
        return false;
    }

    qDebug() << "Database initialized successfully";
    return true;
}

bool DatabaseConnection::createTables()
{
    QSqlQuery query(m_database);
    
    // 创建患者基础信息表
    QString createPatientsTable = R"(
        CREATE TABLE IF NOT EXISTS patients (
            patient_id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(50),
            first_name VARCHAR(50),
            birthday DATE,
            gender VARCHAR(10) CHECK(gender IN ('Male', 'Female')),
            remarks TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    )";
    
    if (!query.exec(createPatientsTable)) {
        m_lastError = QString("Failed to create patients table: %1").arg(query.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    qDebug() << "Patients table created successfully";
    
    // 创建测试结果主表
    QString createTestResultsTable = R"(
        CREATE TABLE IF NOT EXISTS test_results (
            result_id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id INTEGER NOT NULL,
            test_mode VARCHAR(20) NOT NULL CHECK(test_mode IN ('AUTO_SAMPLE', 'STAT_SAMPLE', 'FAST_MODE')),
            parameter_type VARCHAR(50) NOT NULL,
            sample_type VARCHAR(10) NOT NULL CHECK(sample_type IN ('S', 'P', 'B')),
            test_result VARCHAR(50),
            result_status VARCHAR(20),
            test_datetime DATETIME NOT NULL,
            lot_number VARCHAR(50),
            cutoff_value VARCHAR(50),
            dilution_factor REAL DEFAULT 1.0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
        )
    )";
    
    if (!query.exec(createTestResultsTable)) {
        m_lastError = QString("Failed to create test_results table: %1").arg(query.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    qDebug() << "Test results table created successfully";
    
    // 创建预稀释记录表
    QString createDilutionRecordsTable = R"(
        CREATE TABLE IF NOT EXISTS dilution_records (
            dilution_id INTEGER PRIMARY KEY AUTOINCREMENT,
            result_id INTEGER NOT NULL,
            dilution_step INTEGER NOT NULL,
            dilution_ratio REAL NOT NULL,
            notes TEXT,
            FOREIGN KEY (result_id) REFERENCES test_results(result_id) ON DELETE CASCADE
        )
    )";
    
    if (!query.exec(createDilutionRecordsTable)) {
        m_lastError = QString("Failed to create dilution_records table: %1").arg(query.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    qDebug() << "Dilution records table created successfully";
    
    // 创建系统配置表
    QString createSystemConfigTable = R"(
        CREATE TABLE IF NOT EXISTS system_config (
            config_key VARCHAR(50) PRIMARY KEY,
            config_value TEXT,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    )";
    
    if (!query.exec(createSystemConfigTable)) {
        m_lastError = QString("Failed to create system_config table: %1").arg(query.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    qDebug() << "System config table created successfully";
    
    return true;
}

bool DatabaseConnection::createIndexes()
{
    QSqlQuery query(m_database);
    
    // 核心查询索引
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_test_results_patient_datetime ON test_results(patient_id, test_datetime DESC)",
        "CREATE INDEX IF NOT EXISTS idx_test_results_mode_datetime ON test_results(test_mode, test_datetime DESC)",
        "CREATE INDEX IF NOT EXISTS idx_patients_name ON patients(patient_name)",
        "CREATE INDEX IF NOT EXISTS idx_test_results_parameter ON test_results(parameter_type)",
        "CREATE INDEX IF NOT EXISTS idx_test_search ON test_results(test_mode, patient_id, test_datetime)",
        "CREATE INDEX IF NOT EXISTS idx_dilution_result ON dilution_records(result_id)"
    };
    
    for (const QString& indexSql : indexes) {
        if (!query.exec(indexSql)) {
            m_lastError = QString("Failed to create index: %1 - %2").arg(indexSql, query.lastError().text());
            qWarning() << m_lastError;
            // 索引创建失败不是致命错误，继续执行
        }
    }
    
    qDebug() << "Database indexes created successfully";
    return true;
}

bool DatabaseConnection::insertInitialData()
{
    QSqlQuery query(m_database);
    
    // 插入系统配置初始数据
    QString insertConfig = R"(
        INSERT OR IGNORE INTO system_config (config_key, config_value, description) VALUES 
        ('db_version', '1.0', 'Database schema version'),
        ('max_test_results', '10000', 'Maximum number of test results to store'),
        ('auto_cleanup_enabled', 'true', 'Enable automatic cleanup of old records'),
        ('last_patient_id', '0', 'Last used patient ID for auto-increment reference')
    )";
    
    if (!query.exec(insertConfig)) {
        m_lastError = QString("Failed to insert initial config data: %1").arg(query.lastError().text());
        qWarning() << m_lastError;
        // 初始数据插入失败不是致命错误
    }
    
    qDebug() << "Initial configuration data inserted successfully";
    return true;
}

void DatabaseConnection::closeConnection()
{
    if (m_database.isOpen()) {
        m_database.close();
        qDebug() << "Database connection closed";
    }
    
    if (QSqlDatabase::contains(CONNECTION_NAME)) {
        QSqlDatabase::removeDatabase(CONNECTION_NAME);
        qDebug() << "Database connection removed";
    }
}

bool DatabaseConnection::isConnected()
{
    return m_database.isValid() && m_database.isOpen();
}

QString DatabaseConnection::getLastError()
{
    return m_lastError;
} 
#ifndef DILUTIONFACTORDIALOG_H
#define DILUTIONFACTORDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLineEdit>
#include <QPushButton>
#include <QMessageBox>

class DilutionFactorDialog : public QDialog
{
    Q_OBJECT

public:
    explicit DilutionFactorDialog(QWidget *parent = nullptr);
    QString getDilutionFactor() const;
    void setDilutionFactor(const QString &factor);

private slots:
    void onNumberClicked();
    void onDecimalClicked();
    void onNegativeClicked();
    void onDeleteClicked();
    void onCloseClicked();
    void onOkClicked();
    void showWarning();

private:
    void setupUi();
    QGridLayout* createKeypad(); // 更新返回类型
    bool isValidNumber(const QString &text);
    
    QLineEdit *inputEdit;
    QPushButton *numberButtons[10]; // 0-9
    QPushButton *decimalButton;     // .
    QPushButton *negativeButton;    // -
    QPushButton *deleteButton;      // delete
    QPushButton *closeButton;       // close
    QPushButton *okButton;          // OK
    
    QString dilutionFactor;
    bool hasNegative;
    bool hasDecimal;
};

#endif // DILUTIONFACTORDIALOG_H 
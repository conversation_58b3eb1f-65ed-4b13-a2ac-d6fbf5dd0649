#!/bin/bash
# TRF SQLite驱动修复测试脚本
# 临时解决当前版本的SQLite加载问题

echo "=== TRF SQLite驱动修复测试 ==="
echo "当前目录: $(pwd)"

# 检查当前TRF程序
if [ ! -f "./trf" ]; then
  echo "❌ 错误: 未找到trf程序，请在TRF程序目录中运行此脚本"
  exit 1
fi

echo "✓ 找到TRF程序"

# 获取当前目录
APP_DIR="$(pwd)"

echo ""
echo "=== SQLite驱动诊断 ==="

# 检查系统SQLite驱动
echo "1. 检查系统SQLite驱动:"
for system_sqlite in "/usr/lib/qt5/plugins/sqldrivers/libqsqlite.so" "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers/libqsqlite.so"; do
  if [ -f "$system_sqlite" ]; then
    echo "  ✓ 发现系统SQLite: $system_sqlite"
    if command -v ldd >/dev/null 2>&1; then
      echo "    依赖检查:"
      ldd "$system_sqlite" 2>/dev/null | grep Qt5 | head -3 || echo "    无Qt5依赖或静态链接"
    fi
  else
    echo "  - 未找到: $system_sqlite"
  fi
done

# 检查应用SQLite驱动
echo ""
echo "2. 检查应用SQLite驱动:"
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
  echo "  ✓ 发现应用SQLite: $APP_DIR/plugins/sqldrivers/libqsqlite.so"
  if command -v ldd >/dev/null 2>&1; then
    echo "    依赖检查:"
    ldd "$APP_DIR/plugins/sqldrivers/libqsqlite.so" 2>/dev/null | grep Qt5 | head -3 || echo "    无Qt5依赖或静态链接"
  fi
else
  echo "  - 未找到应用SQLite驱动"
fi

echo ""
echo "=== 修复策略测试 ==="

# 策略1: 仅使用系统驱动
echo "测试策略1: 仅使用系统SQLite驱动"
export QT_PLUGIN_PATH="/usr/lib/qt5/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"
echo "  设置: QT_PLUGIN_PATH=/usr/lib/qt5/plugins"
echo "  测试运行..."
timeout 5s ./trf 2>&1 | grep -E "(SQLite|available drivers|QSQLITE)" | head -3
echo "  (5秒超时测试)"

echo ""

# 策略2: 混合路径，系统优先
echo "测试策略2: 混合路径，系统SQLite优先"
export QT_PLUGIN_PATH="/usr/lib/qt5/plugins:$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"
echo "  设置: QT_PLUGIN_PATH=/usr/lib/qt5/plugins:$APP_DIR/plugins"
echo "  测试运行..."
timeout 5s ./trf 2>&1 | grep -E "(SQLite|available drivers|QSQLITE)" | head -3
echo "  (5秒超时测试)"

echo ""

# 策略3: 混合路径，应用优先
echo "测试策略3: 混合路径，应用SQLite优先"
export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"
echo "  设置: QT_PLUGIN_PATH=$APP_DIR/plugins:/usr/lib/qt5/plugins"
echo "  测试运行..."
timeout 5s ./trf 2>&1 | grep -E "(SQLite|available drivers|QSQLITE)" | head -3
echo "  (5秒超时测试)"

echo ""

# 策略4: 仅使用应用驱动
echo "测试策略4: 仅使用应用SQLite驱动"
export QT_PLUGIN_PATH="$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"
echo "  设置: QT_PLUGIN_PATH=$APP_DIR/plugins"
echo "  测试运行..."
timeout 5s ./trf 2>&1 | grep -E "(SQLite|available drivers|QSQLITE)" | head -3
echo "  (5秒超时测试)"

echo ""
echo "=== 测试完成 ==="
echo "请查看上述测试结果，选择最佳策略:"
echo "- 如果看到 'QSQLITE driver not loaded' 说明策略无效"
echo "- 如果看到其他错误或程序正常运行说明SQLite已修复"
echo ""
echo "要手动测试某个策略，请设置相应的环境变量后运行 ./trf" 
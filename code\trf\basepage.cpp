#include "basepage.h"

BasePage::BasePage(QWidget *parent) : QWidget(parent)
{
    setupUi();
}

BasePage::~BasePage() {}

void BasePage::setupUi()
{
    // Set up main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // Create header
    header = new CommonHeader(this);

    // Create content area (reserved for future development)
    contentArea = new QWidget();
    
    // Add to layout
    mainLayout->addWidget(header);
    mainLayout->addWidget(contentArea);
}

void BasePage::setContentWidget(QWidget *widget)
{
    if (contentArea) {
        layout()->removeWidget(contentArea);
        delete contentArea;
    }
    contentArea = widget;
    layout()->addWidget(contentArea);
}

CommonHeader* BasePage::getHeader()
{
    return header;
}

QWidget* BasePage::getContentWidget() const
{
    return contentArea;
} 
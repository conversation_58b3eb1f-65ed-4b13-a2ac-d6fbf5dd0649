# 160901(1)_1_45_translate_20250526171032

PDF文档转换为Markdown格式

---

## 第 1 页

3
目录
胡马菲亚..................................................................................... 2
目录......................................................................................... 3
1 安全说明.................................................................................... 5
1.1 介绍................................................................................... 5
1.2 用户保修............................................................................... 5
1.3 仪器使用............................................................................... 5
1.4 一般安全警告........................................................................... 5
1.5 处置管理理念........................................................................... 6
1.6 生物危害警告........................................................................... 6
1.7 器械消毒............................................................................... 7
2 系统说明.................................................................................... 8
2.1 预期用途............................................................................... 8
2.2 仪器结构............................................................................... 8
2.2.1 HumaFIA仪器........................................................................ 8
2.2.2 HumaFIA电源........................................................................ 9
2.2.3 HumaFIA校准卡...................................................................... 9
2.2.4 HumaFIA电源组...................................................................... 9
2.2.5 HumaFIA测试盒..................................................................... 10
2.2.6 HumaFIA数据电缆................................................................... 10
2.2.7 HumaFIA标准试剂盒................................................................. 11
2.3 技术数据.............................................................................. 11
2.4 方法：时间分辨荧光免疫分析............................................................ 12
2.4.1 荧光免疫分析侧流盒................................................................ 12
2.4.2 HumaFIA的时间分辨技术............................................................. 12
2.5 安装.................................................................................. 13
2.5.1 打开仪器.......................................................................... 14
2.5.2 关闭仪器.......................................................................... 15
2.6 软件用户界面.......................................................................... 15
2.6.1 用户界面.......................................................................... 15
2.6.2 菜单树............................................................................ 16
3 日常使用和测量............................................................................. 18
3.1 仪器操作和样品制备.................................................................... 18
3.1.1 样本.............................................................................. 18
3.1.2 程序.............................................................................. 18
3.1.3 校准.............................................................................. 19
3.1.4 结果计算.......................................................................... 19
3.1.5 质量控制.......................................................................... 19
3.1.6 参考值............................................................................ 19
3.1.7 结果解释.......................................................................... 19


---

## 第 2 页

HumaFIA |用户手册
4
3.1.8 局限性............................................................................ 19
3.2 采样模式.............................................................................. 20
3.2.1 自动采样模式，具有内部孵育功能.................................................... 20
3.2.2 STAT样本模式，灵活测试............................................................ 21
3.2.3 快速模式采样...................................................................... 22
3.2.4 添加样本、患者数据和参数类型...................................................... 24
3.3 质量控制模块.......................................................................... 26
3.3.1 上传新的控制材料.................................................................. 27
3.3.2 在自动模式下运行控制.............................................................. 28
3.3.3 在快速模式下运行控制.............................................................. 29
3.3.4 QC模块中的数据库.................................................................. 31
3.3.5 QC模块中的校准卡概述.............................................................. 32
3.4 数据库接口............................................................................ 33
3.4.1 一般业务.......................................................................... 33
3.4.2 数据库过滤器功能.................................................................. 34
4 高级操作................................................................................... 35
4.1 设置界面.............................................................................. 35
4.2 测试程序的设置........................................................................ 36
4.3 通用（日期、时间、语言、亮度、温度、删除）............................................ 37
4.4 参数列表选择.......................................................................... 38
4.5 启用标志的设置........................................................................ 40
4.6 常规(2)设置、软件更新、自动警告....................................................... 41
4.7 数据库中数据字段的选择................................................................ 42
4.8 设置以建立连接通道.................................................................... 42
4.9 服务和维护............................................................................ 43
4.9.1 运行标准试剂盒作为质量检查........................................................ 43
5 故障排除................................................................................... 46
6. 最新信息/更新............................................................................. 48


---

## 第 3 页

HumaFIA |用户手册
5
1 安全说明
1.1 介绍
本手册是仪器的一部分，操作人员和维修人员必须掌握。为了准确安装、使用和维护，请仔细阅读以下说
明。
为避免仪器损坏或人身伤害，请仔细阅读第1.4章“ 一般安全警告” ，其中描述了适当的运行程序。如果仪器
出现故障或其他问题，请联系您当地授权的技术服务部门。
1.2 用户保修
制造商保证其授权代表销售的仪器在材料或工艺方面没有任何缺陷，但该保证仅适用于自新仪器交付给买方
之日起一年内出现的明显缺陷。
在保修期内，除运输至维修点的费用外，人类代表应免费更换或修理任何有缺陷的物品。
本保修不包括人类代表对在正常使用过程中被视为消耗品的任何物品进行更换的责任，例如。：灯、阀门、
注射器、玻璃器皿、保险丝、管路等。
如果产品未按照制造商的说明使用，以任何非人为规定的方式进行更改，未定期维护，与未经人类批准的设
备一起使用或用于非设计用途，则该人类代表应免除本保证项下的任何责任。
1.3 仪器的使用
本仪器必须用于其预期用途（见第2.1章预期用途）。必须由合格人员在完美技术条件下操作，且在第1.4章
一般安全警告中描述的工作条件下维护。本手册包含合格专业操作员的说明。
1.4 一般安全警告
仅使用人类指定和提供的化学试剂和附件，或本手册中提及的化学试剂和附件。放置产品时应确保其具有适
当的通风条件。仪器应安装在平坦、静止的工作表面上，且无振动。
切勿在灰尘过多的区域操作。按照本手册中列出的规格，在温度和湿度水平下操作。切勿在拆下盖板和面板
的情况下操作本仪器。仅使用为本产品指定的电源线，且电源线的接地导体连接到地。
仅使用本仪器制造商指定的保险丝类型和额定值。使用额定值不正确的保险丝可能会造成电气和火灾危险。
为避免火灾或触电危险，请遵守所有额定值和


---

## 第 4 页

HumaFIA |用户手册
6
仪器上的标记。不要在可能爆炸或有火灾危险的环境中为仪器供电。
在清洁或维护仪器前，请先关闭仪器并拔掉电源线。本手册中描述的清洁材料是唯一允许使用的，因为其他
材料可能会损坏部件。建议在使用仪器时始终穿戴防护服和护目镜。本手册中的所有警告标志都必须仔细遵
守。
该仪器专用于临床诊断。
仅适用于欧盟用户：请向制造商和用户和/或患者所在成员国的主管当局报告与器械相关的任何严重事件。
1.5 处置管理理念
必须遵守有关处置的适用当地法规。用户有责任安排对各个部件进行适当的处置。
在处置前，必须通过适当的、经过确认的程序（高压灭菌、化学处理）对可能含有潜在传染性物质的所有部
件进行消毒。器械和电子附件（不含电池、电源包等）必须根据当地有关电子组件处置的适用法规进行处
置。
电池、电源组和类似的电源必须从电气/电子部件上拆下，并按照适用的当地法规进行处置。
1.6 生物危害警告
体外诊断应用的分析仪器涉及处理应至少视为潜在传染性的人体样本和对照品，因此，该仪器的每个部件和
附件如果可能与这些样本接触，则同样应视为潜在传染性。
在首次使用生物材料之前，必须在仪器上贴上生物危害警告标签！
图1生物危害符号


---

## 第 5 页

HumaFIA |用户手册
7
1.7 器械消毒
在对仪器进行任何维修之前，彻底消毒所有可能被污染的部件非常重要。在将仪器从实验室移除以进行处置
或维修之前，必须对其进行去污处理。去污工作必须由经过授权且训练有素的人员执行，并遵守所有必要的
安全预防措施。


---

## 第 6 页

HumaFIA |用户手册
8
2 系统说明
2.1 预期用途
HumaFIA是一种半自动时间分辨荧光免疫分析仪，用于定量测量人体血清、血浆或全血中的生理或病理参数，
具体取决于测试参数。该仪器是一个封闭系统，仅可与HumaFIA SR试剂配合使用，作为辅助诊断、筛查或监
测的工具。仅供实验室专业人员使用。
2.2 仪器结构
仪器由仪器外壳、电源、荧光法盒式读取器、显示器和数据管理单元组成。
仪器外壳由上壳和下壳两部分组成。上壳可从仪器上拆卸下来，以便维修人员方便地进行维修。
仪器由内部软件控制，可存储多达10000个测试结果。电源开关位于仪器背面。
2.2.1 HumaFIA仪器
图2-1 HumaFIA仪器与卡盒，将插入到卡匣输入槽中


---

## 第 7 页

HumaFIA |用户手册
9
2.2.2 HumaFIA电源
图2-2电源
2.2.3 HumaFIA校准卡
校准卡[CAL]包含一个参数的校准曲线，显示在卡片上。此外，在参数名称下方显示了相应试剂盒的批号。将
校准卡插入分析仪并上传校准曲线。之后不再需要校准卡，因为该批次和参数的校准曲线已存储在分析仪
中。然而，在试剂盒用完之前，请保留校准卡。有关上传的更多详细信息，请参阅QC模块中的第3.3.5节“ 校
准卡概述” 。
图2-3校准卡，包含批号和参数信息
2.2.4 HumaFIA电源组
HumaFIA电源（73 Wh）是一种可选配件，即使在没有市电的情况下，分析仪也能运行数小时。它可以与太阳
能电池板结合使用，作为独立设备运行。
电力系统
图2-4-1电池组，20100 mAh，73 Wh
图2-4-2太阳能板折叠式，36W
REF: 18250/70
REF: 18250/73


---

## 第 8 页

HumaFIA |用户手册
10
2.2.5 HumaFIA测试盒
每个测试盒的形状相同，可插入分析仪前面板上标记为“ 盒式输入” 的插槽。样本体积通过盒式盒的样本端
口注入。测试结果在分析仪内的盒式盒矩形窗口内读取。
分析仪自动读取卡匣上的条形码，并将试剂盒类型与用户在软件中的选择进行匹配。此外，还会检查批次的
过期日期。测试类型和批次编号是
显示在条形码旁边。
首先按照箭头所示，将带有样品端口的试剂盒插入分析仪。
图2-5各参数的药盒，条形码包含批号信息
2.2.6 HumaFIA数据电缆
图2-6数据电缆，用于将HumaFIA与PC连接以进行数据交换


---

## 第 9 页

HumaFIA |用户手册
11
2.2.7 HumaFIA标准试剂盒
标准墨盒是一种维修工具。标准墨盒允许维修工程师或有经验的用户检查HumaFIA光学系统的正确功能。
图2-7标准滤芯，参考编号：16090/510
要在HumaFIA上运行标准试剂盒，首先插入相应的校准卡。在QC模块中运行标准试剂盒，参见第3.3章“ QC模
块” 。
2.3 技术数据
表2-1规格
显示
800 x 480像素，/7英寸触摸屏
操作系统
一种可免费使用的操作系统
样品读取时间
少于3秒
样品要求
取决于参数类型（血清、血浆、全血）
环境条件
°
20-25℃操作温度。
相对湿度20-90 %，无冷凝。
气压86.0-106.0 kPA
避免强磁场干扰，远离爆炸性气体、粉尘、阳光直射和水浸。
储存条件
10-40℃（当分析仪未连接时）<93%相对湿度，无冷凝。空气压力
50.0-106.0 kPA
打印机
内部热敏打印机
样本标识
外部（可选）条形码扫描器
电源
100-220V，50-60 Hz，最大功耗60 VA。直流12V 5A。三线电源线，
接地性能良好。
尺寸
300 x 305 x 120 cm
权重
净重：4.2 kg，总计：6.0 kg


---

## 第 10 页

HumaFIA |用户手册
12
2.4 方法：时间分辨荧光免疫分析
2.4.1 荧光免疫分析侧流盒
使用干试剂的横向流动试验非常简单，但由于其抗体检测方法，因此具有很高的特异性。
原理：该测试采用夹心检测法来测定样本中的分析物，如CRP。如果样本中含有分析物，Eu3+标记的抗体会捕
获硝酸纤维素膜下游流动的分析物。随后，免疫复合物被固定抗体捕获在测试区域，形成抗原-抗体-抗原复
合物。样本中抗原/分析物越多，测试区域捕获的免疫复合物就越多。这会导致Eu3+荧光信号强度增强，仪器
处理后显示样本中的分析物浓度。
图2-8反应原理
稳定干燥试剂盒已准备好使用（4-30°C），便于储存和运输。支持全血、血清和血浆样本类型。请参阅每个
参数对应的IFU。
2.4.2 HumaFIA的时间分辨技术
高灵敏度的时间分辨荧光免疫分析（TR-FIA）技术基于荧光铕Eu3+配合物，这些配合物具有显著的斯托克斯
位移、狭窄的发射带和较长的荧光寿命。传统荧光染料仅在激发脉冲后的一小段时间内发光。然而，在这一
短暂的时间段内，也会发生自发荧光，这会增加传统测试的背景信号。
基于长荧光寿命的Eu3+的时间分辨技术，允许在自发荧光完全衰减后立即开始检测荧光。这保证了非常灵敏
的测试，具有较低的检测限（LOD）。


---

## 第 11 页

HumaFIA |用户手册
13
由于该过程在0.5 ms下进行，仪器测试的读出时间仅需几秒钟。
图2-9 TR-FIA方法说明，仅作说明用的图表
2.5 安装
本章提供了HumaFIA分析仪的拆箱和安装说明。请遵循以下程序以确保正确操作和服务。在操作分析仪之前，
请仔细阅读并遵循本用户手册中的所有说明。此免疫荧光分析仪是一种精密仪器：请小心操作。
若不慎掉落或不当处理仪器，将会破坏或损坏已校准的机械和电子部件。检查仪器在运输过程中是否有任何
可见的损坏迹象。如果发现任何损坏，请立即向承运人或经销商提出索赔。核对包装清单，检查收到的配
件。如果缺少任何物品，请联系服务部门。
1.
拆开仪器包装。
2.
将仪器放置在易于接近接地电源插座的表面上，并确保仪器与电源插座之间的距离大于20 cm。
3.
将仪器放置在用户可以轻松接触到电源开关的位置。在紧急情况下，用户可以通过开关关闭仪器或拔掉
电源线。
4.
工作台应至少为400 mm（长）x 450 mm（宽）x 450 mm（高），并且应能承受超过5 kg的重量。


---

## 第 12 页

HumaFIA |用户手册
14
5.
在仪器前面留出足够的空间，便于操作。将仪器放置在稳定的位置，避免碰撞或冲击。
6.
将电源线连接到仪器电源接口。
7.
请勿随意拆卸仪器。
8.
保持房间通风良好，以冷却。
为保证仪器的准确性和测量数据的可比性，需要对仪器的光学部分进行测试，详见标准试纸使用说明，第
4.9.1节“ 运行标准试纸作为质量检查” 。
运行样本的基本步骤，详细说明见下文软件描述
1.
打开分析仪。
2.
插入校准卡以上传校准曲线（参见QC模块中的3.3.5校准卡概述）。
3.
选择采样模式。
4.
添加患者姓名或ID。
5.
向卡盒中添加样品并等待孵育（参见IFU，了解每个参数）。
6.
将测试卡插入分析仪中进行读取。
7.
自动数据存储，可选打印结果。
8.
关闭分析仪。
2.5.1 打开仪器
连接电源线，打开仪器背面的电源开关。每次打开仪器时，它都会进行自检。系统自动检查硬件，自检完成
后进入仪器登录界面，选择管理员，输入密码12345678。
图2-10开始屏幕


---

## 第 13 页

HumaFIA |用户手册
15
注：并非所有功能/选项在管理模式下可用，甚至更少的功能/选项在用户模式下可用。请联系您的供应商以
促使进行仅在服务模式下可能的变化。
选择用户级别，输入密码，然后单击登录进入主屏幕（请参阅第2.6.1.4章“ 主屏幕” ）。
2.5.2 关闭仪器
完成测试后，关闭仪器后面的电源开关。
2.6 软件用户界面
本章包含有关所实现的软件菜单结构的结构和使用的信息。该集成软件控制仪器操作，包括测量数据的计算
和评估、显示结果和信息屏幕、数据存储和检索。
2.6.1 用户界面
HumaFIA分析仪的用户界面设计直观易用，可访问所有用户级别功能。用户仅通过触摸屏和虚拟键盘或使用外
部键盘与HumaFIA交互。
2.6. 1. 1使用触摸屏
触摸屏是玻璃后面的电容传感器。必须使用手指或专为电容触摸屏设计的导电笔来操作，因为该设备对压力
不敏感。触摸屏允许用户通过轻触选定的菜单项或按钮来选择和“ 按下” 它们。HumaFIA分析仪所使用的触摸
屏设备因其可靠性、耐用性和与医疗实验室环境的兼容性而被选中。该设备支持简单的操作，但不支持多点
触控手势或多次点击。表面玻璃材料具有抗液体性，但在使用时应保持干燥，否则可能无法正常工作。当分
析仪关闭时，使用湿布清洁玻璃表面。
2.6. 1.2按钮颜色
激活按钮显示为红色和白色图标，或深灰色和白色图标，处于非活动状态。选择后，它们会变为相反的颜
色。如果按钮不可选，则保持灰色。
2.6. 1.3输入信息
HumaFIA有时需要操作员输入诸如样本ID或患者数据等信息。要将信息输入到数据字段中，只需轻触或点击其
框。数据字段的颜色将发生变化，表示该字段的数据输入处于活动状态。使用虚拟屏幕键盘或可选的外部键
盘输入信息。
在用户按下Enter或Save或Accept继续操作之前，数据不会被保存。
2.6. 1.4主屏幕
启动后或在菜单标题中按下人形标志时，将出现主菜单。


---

## 第 14 页

HumaFIA |用户手册
16
图2-11主屏幕
主屏幕界面的描述
左侧红色条：
.仪器时间和日期
仪器用户模式（管理员、用户或服务）
。外部条码阅读器连接状态
。打印机连接状态
六个灰色按钮
灰色按钮直接指向它们所命名的功能。
2.6.2 菜单树
以下为HumaFIA分析仪菜单树的列表。
2.6.2. 1主屏幕界面
图2-11为系统登录后自动出现的主界面。
菜单显示了3种采样模式和3个模块来操作系统，下面将分别在不同的章节中进行说明(3 .2采样模式、3.3质
控模块、3.4数据库接口、4.1设置接口）。
自动加样孵育：加样后插入检测盒，孵育和读取自动完成。
仅读取STAT样本：样本的孵育在分析仪外进行，时间可以手动管理。仅插入试剂盒以读取。
快速模式，通过软件进行工作流程规划，定义孵化的起始点、读取和监测孵化时间的请求。
.质控模块：允许用户设置质控批号、参考值和偏差，检查L-J曲线，检查质控结果值。
数据库模块：查询结果，查看样本结果值。


---

## 第 15 页

HumaFIA |用户手册
17
设置模块：用于设置日期和时间，但同时也用于设置标志、参考值、打印机设置、连接性和数据库
选择，以便对结果进行审查。
通过按压永久性标题栏左上角的人形标志，从分析仪的任何屏幕访问主屏幕。
2.6.2.2 每个屏幕的标题
图2-12每个HumaFIA屏幕上显示永久性标题
第一行显示了人类标志，然后是选定的用户（例如，管理员），接着是各种连接端口的状态、软件检查和设
置。
软件的6个主要类别或模块显示在永久标题栏的第二行：自动采样、STAT采样、快速模式、QC模块、数据库和
设置。每个模块都在单独的章节中进行解释(3 .2采样模式，3.3 QC模块，3.4数据库界面和4.1设置界面）。
表2-2每个屏幕标题栏状态行中符号含义
如果最初未显示更多患者结果或数据，请使用大多数屏幕右侧的滚动条来获取。点击人形标志始终会导向主
屏幕。
状态
白的
-符号颜色和含义-黄色
打印机
打印机已连接。
打印机不工作。
外部条码阅读器
条形码阅读器已连接。
条码阅读器脱机。
校准卡
上传的校准有效且与所选测试匹
配。
上传的校准无效或与所选测试不匹配。
样品盒
警告，错误消息。
质量控制模块
控制有效且在目标值范围内。
控制已过期或超出目标值范围。
数据库
可用存储容量
存储容量低。
镶嵌
警告，错误消息。
报警三角
警告，错误消息。
温度，由HumaFIA系统上的
传感器监测。
在18-28°C的样本孵育限度内。
环境温度传感器超出可接受范围<18°C或>28
°C。存在错误值的风险。
时间
在hh：mm：ss格式中，如需调整，请参阅“ 设置” 章节
日期
在YYY-MM-DD格式中，如需适应，请参见“ 设置” 章节


---

## 第 16 页

HumaFIA |用户手册
18
3 日常使用和测量
3.1 仪器操作和样品制备
HumaFIA分析仪是一种半自动仪器。用户必须准备样本，孵育试剂盒（在分析仪内自动或在外部手动），将样
本试剂盒插入分析仪的卡匣输入槽，并开始读取结果。更多详情请参见下文。
对于样本制备、孵育和试剂盒处理，请遵循各HumaFIA参数的使用说明，因为样本类型、抗凝剂、样本体积和
缓冲液的使用也取决于参数。
3.1.1 样本
1. 通常，可以使用全血、血清和血浆。
2. 应在采血后2小时内通过离心分离血清或血浆。
3. 立即或在样本采集后2小时内检测样本。如果无法在2小时内完成检测
将血清或血浆在+2°+8°C下储存8小时；如果需要更长时间的储存，将血清或血浆在-20°C以下储存3
个月。不要加热灭活样品。丢弃溶血样品。
3.1.2 程序
严格按照描述的程序进行：
1. 在进行样品测试前，请仔细阅读本说明书和适用的仪器用户手册。使用冷藏测试盒前，请使其达到工作
温度(18-28℃）。将测试盒放置在水平的平面上。
2. 检查测试盒的批号是否与校准卡一致。
3. 将校准卡插入仪器的校准卡端口进行测试。
4. 冷藏的血清或血浆标本必须在检测前达到室温，冷冻的血浆或血清标本解冻后应进行涡旋和离心处理，
上清液应收集并达到室温后方可检测。
5. 如果需要，将血清／血浆／全血移入稀释剂小瓶中（参见参数IFU)。重新盖上小瓶盖，将小瓶倒置10
次以混合样品混合物。
6. 将80µL（稀释）样品精确移入检测盒的样品口。启动计时器，反应需要几分钟（根据IFU)。
7. 反应时间结束后，将测试盒插入分析仪中，选择以下三种模式之一的准确样本类型来运行样本。分析仪
会自动检测并检查测试盒类型。一旦分析仪读取测试结果，屏幕上就会显示结果，分析完成后可以打
印。
8. 完成检测后，将试剂盒从分析仪中取出。


---

## 第 17 页

HumaFIA |用户手册
19
9. 根据任何适用法规处置用过的滤芯和移液管头。
3.1.3 校准
主曲线校准：每个HumaFIA SR试剂盒都有一个校准卡[CAL]，其中包含用于校准的批次特定信息。从卡中上传
校准曲线后，该批次的校准将存储在仪器上。所有后续样本都可以在无需进一步上传的情况下进行测试。校
准卡可以随时上传。
3.1.4 结果计算
HumaFIA系统自动计算每个样本的分析物浓度。参见第3.3.5节“ 质控模块中的校准卡概述” 中的校准曲线上
传。
3.1.5 质量控制
质量控制方面，使用人类提供的相应质量控制产品：
每天一次，在测试前。
如果有任何意外的测试结果。
每次上传校准后。
质量控制结果不在可接受范围内可能表明试验结果无效。
3.1.6 参考值
IFU中提供了参考值，可将每个参数的参考值/范围存储在分析仪中。
建议每个实验室为其服务的人群建立自己的预期值。
3.1.7 结果解释
如果样品中的浓度超过线性范围，则在检测前用5 % BSA生理盐水溶液稀释样品。HumaFIA提供软件选项，可
自动计算预稀释样品的值。每个样品最多可稀释3次。
检测结果不能作为诊断的唯一依据，应结合其他临床和实验室数据进行临床诊断，如果检测结果与临床评估
不一致，应进一步检查。
3.1.8 局限性
某些含有针对试剂组分的抗体的样本可能会造成干扰。因此，应考虑患者的临床病史以及进行的任何其他检
测的结果来解释检测结果。
如果样本中含有高浓度的甘油三酯、胆固醇、胆红素或溶血，检测结果将受到影响。
与使用小鼠抗体的任何试验一样，样本中的人抗小鼠抗体（HAMA）可能产生干扰。试验的制定旨在将这种干
扰降至最低；然而，样本来自


---

## 第 18 页

HumaFIA |用户手册
20
经常接触动物产品或动物血清的患者可能含有异嗜性抗体，这可能导致错误的结果。详情请参见IFU。
注：每个药筒的内包装/袋必须密封严密，无渗漏现象，测试卡表面应光滑、无毛刺，颜色均匀。
安全须知：所有患者样本、校准品和质控品均应视为可能具有传染性的物质处理。所有来源为人类的供体单
位均已使用批准的方法检测了HBsAg、HIV和HCV抗体，结果均未呈阳性反应。所有动物源材料可避免使用人血
清（如乙型肝炎、丙型肝炎和HIV)带来的诸多风险。然而，所有来源为人或动物的材料仍应视为可能具有传
染性的物质处理。
3.2 取样模式
HumaFIA软件提供了3种模式来运行样本。
a)
自动采样模式
b)
STAT样本模式
c)
快速模式
3.2.1 自动采样模式，具有内部孵育功能
选择自动取样后，将显示以下屏幕。
图3-1自动样本模式，用于自动孵育和读取测试
自动取样模式允许对测试进行自动取样。分析仪在分析仪内完成测试孵育后，自动管理孵育时间和读取测试
结果。
如果孵育时间为1.5分钟，例如CRP参数，则在此孵育/测试时间内不能进行其他试验。
使用“ 添加样本” 按钮，通过选择患者姓名/ID和参数类型，将新测试添加到工作列表中。请参阅下面的第
3.2.4章“ 添加样本、患者数据和参数类型” 。


---

## 第 19 页

HumaFIA |用户手册
21
按下“ 插入培养盒” 按钮，关闭样本详细信息界面并启动软件计时器。
将患者样本加入到试剂盒中，然后将试剂盒放入分析仪中，最后按下此按钮。
按下此按钮，启动自动孵育计时器。孵育时间结束后，测试读数为
由软件自动触发。结果的准确性取决于正确的孵育时间。因此，在准备试剂盒和启动软件计时器时，时间非
常重要。
剩余孵育时间以mm：ss格式显示在沙漏旁边的计时器列中。在LOT列中，显示所用试剂盒的LOT编号。在Cut-
off列中，显示临床临界值。
分析仪自动读取结果后，会将结果及其对应的单位括号显示。黑色表示正常，红色则用箭头标记，表明结果
超出正常范围。向上箭头表示值高于正常水平，向下箭头则表示值低于正常范围。显示读取的日期和时间，
以及样本类型。S=血清，B=全血，P=血浆。
注：3合1检测，如cTnI/Myo/CK-MB显示在3行中。3合1试剂盒的每个参数在屏幕上显示在单独的一行中。参数
名称以两个点开始和结束（例如，.. cTnI..，或.. Myo. .)。
3.2.2 STAT样本模式，灵活测试
在主屏幕上或永久性标题栏中选择STAT样本，以进入以下菜单。
图3-2 STAT样本模式，便于灵活读取检测结果
STAT模式允许在分析仪外孵育样本，并在孵育时间允许的情况下，在中间运行另一个样本。在这种模式下，
分析仪不监测孵育时间。因此，操作员必须通过手动方法确保正确的孵育时间，例如使用计时器。


---

## 第 20 页

HumaFIA |用户手册
22
首先，使用“ 添加样本” 按钮定义下一个样本。这将导致第3.2.4章“ 添加样本、患者数据和参数类型” 中描
述的过程。
按下“ 读取” 按钮，将培养盒置于上一个屏幕“ 添加样本” 上，关闭样本详细信息界面并开
始读取培养盒。
将孵育后的试剂盒插入分析仪中，并在孵育时间结束时按下此按钮。
按下此按钮立即开始读取结果。结果的准确性取决于正确的孵育时间。因此，在准备试剂盒、启动计时器和
然后开始读取时，时间非常重要。
注：3合1检测，如cTnI/Myo/CK-MB，显示在3行中。3合1检测盒的每个参数在屏幕上单独显示一行。参数名称
以两个点开头和结尾（例如，.. cTnI..，或.. Myo. .)。
3.2.3 快速模式采样
在主屏幕上或永久性标题栏中选择快速模式，以访问以下菜单。
图3-3快速模式，用于读取一系列测试
快速模式允许用户运行一系列测试，并每20秒提供一个测试结果。工作流管理软件通过管
理以下内容支持快速测试：
-何时将样本添加到卡盒中，
-何时将完全孵育的试剂盒插入分析仪进行读取
-保证质量结果的正确孵育时间
这样，可以轻松地管理一系列测试，甚至不同参数类型的测试。
首先，通过“ 添加样本” 按钮定义患者工作列表以添加样本。这将导致第3.2.4章“ 添
加样本、患者数据和参数类型” 中描述的过程。


---

## 第 21 页

HumaFIA |用户手册
23
在上一屏幕中按下“ 添加测试到快速模式列表” 可关闭样本详细信息界面，并将样本从下往上逐一添加到工
作列表中。
要以快速模式运行样本，请按照以下步骤进行每个测试。
在快速模式工作列表的“ 计时器” 列中，会出现“ 下一个样本” 按钮。秒数显示了可以开
始下一个（或第一个）样本的时间。如果秒数达到00或显示80µL vol.已添加，则可以开始
该测试。
下一步是将80µL的样本加入到卡盒中。完成后，请立即按下此按钮。现在开始监测该测试
的孵育时间。注意：根据参数的不同，80µL的样本体积可以是血液/血清，也可以是血液/
血清的稀释液（参见参数IFU）。
对于本试验，现在剩余的孵育时间以mm：ss格式显示。当本试验的孵育时间快结束时，下
一个按钮出现。
现在，用户有10秒时间将试剂盒插入分析仪并按下“ 读取” 按钮。为了获得正确结果，当
计时器达到00时，按下“ 读取” 按钮。阅读仅需几秒钟，然后显示此参数的结果。
如果时间正确，--：- -出现，表明孵育时间正确，并且在可接受范围内。
如果出现红色数字，如+02：11，则孵育时间过长2分钟11秒，表明结果无效，可能需要重
复。
要运行一系列测试，可以启动上述工作流程以进行下一次测试。
一旦上一个测试的孵育时间开始，此按钮就会显示出来，以便在快速工作列表中准备下一
个测试。工作流程程序运行计时器，允许用户准备下一个测试。如果计时器显示达到00，
系统就准备好准备下一个测试盒了。
现在进行第二次测试，必须将80µL的样本移液到卡盒中。完成此操作后，立即按下此按
钮。现在开始监测工作列表中第二次测试的孵育时间。
将对上述第一项测试的系列进行处理，以进行第二项测试和后续测试，直至快速工作列表完成。
当快速工作列表清空后，每行将提供以下样本信息：患者的姓名、ID、参数类型（括号内为单位）、结果、
测试日期和时间，以及使用的样本类型（B=全血，S=血清，P=血浆）。此外，还将提供该参数的批次（LOT）
和截止值。


---

## 第 22 页

HumaFIA |用户手册
24
显示为参考。分析仪仅显示结果，而不显示线性标志，因为这些标志表明测试无效，必须重复。如果计时器
列中出现红色数字，则孵育时间不正确。
可在设置中启用以下声学警报：
声报警
功能
2个短蜂鸣声
可选择下一个样本
长蜂鸣声
孵育时间快结束，距离孵育时间结束还有10秒
非常长的蜂鸣声
孵育时间结束
短蜂鸣
完成对墨盒的读取，显示结果
表3-1声音报警设置，可在设置菜单中启用和修改
注：三合一检测（如cTnI/肌钙蛋白/CK-MB）以三行显示。每个参数在屏幕上分别显示一行。参数名称以两个点开
头和结尾（例如，.. cTnI..，或..肌钙蛋白. .)。
3.2.4 添加样本、患者数据和参数类型
在三种样本运行模式中，均采用相同程序添加患者ID、选择参数和样本类型或预稀释。此外，还可以向样本
添加其他患者数据，如姓氏、名字、生日、性别及备注。
下一个屏幕允许用户在Auto sample、STAT sample和Fast模式下将患者样本添加到工作列表中，屏幕始终相
同，只是关闭屏幕的按钮不同，取决于样本模式。见下图3-4：
图3-4向Auto-sample、STAT-sample或Fast-mode过程添加新样本以运行测试


---

## 第 23 页

HumaFIA |用户手册
25
3.2.4. 1在3种测试模式中确认患者数据
根据样本模式，按下以下按钮之一关闭上一个屏幕：
自动采样
STAT样本
快速模式
3.2.4.2添加ID
要添加新的患者样本，请按“ 添加ID” 以分配一个ID编号。然后将自动生成一个新的ID编号。
3.2.4.3 选择参数
使用深灰色按钮选择参数类型。由于之前已上传有效的校准曲线，可
用参数以白色字体显示。如果按钮为红色，则先上传校准曲线。如果
所需参数不在列表中，请按其他。当正确参数显示在浅灰色字段中
时，按退出将所选参数转移到上一个屏幕。
选择样本类型，白色显示的类型可用于所选参数类型。
如果需要对样本进行稀释以获得线性范围内的结果，则将预稀释功
能（Pre-Dil.）设置为“ 是” 。检查参数的IFU值，以使用经验证的
稀释因子。
3.2.4.4 可自动计算预稀释样品的选项
如果预期值非常高，系统可选择自动计算预稀释样品。如果选择“ 是” 作为“ 预稀释” （参见上图），则在
处理结果时会自动考虑预稀释因子。
按下“ 插入培养盒” 按钮，将在自动样品模式下关闭此屏幕并启动软件计时
器。
将患者样本加入到试剂盒中，并在按下此按钮之前将试剂盒插入分析仪。
按下“ 读取” 孵育盒按钮，将关闭STAT样本模式下的此屏幕并立即开始测试
读数。
请确保：将患者样本添加到试剂盒中，并在按下此按钮之前将试剂盒插入分
析仪。
按下“ 添加测试到快速模式列表” 按钮，将关闭快速模式下的此屏幕并填充
快速工作列表。新测试/患者将依次添加到工作列表中，从按钮到顶部。
可在下一个屏幕上启动孵育和读取。


---

## 第 24 页

HumaFIA |用户手册
26
注意：只有当预稀释与软件显示的稀释率相匹配时，结果才是正确的。为了进行预稀释，根据IFU，用5 %
BSA生理盐水溶液稀释样品。
3.2.4.5 添加患者数据
有两种方式可以添加患者数据：
A) 通过键盘手动
B) 通过可选条形码阅读器
成功添加患者数据后，姓、名、出生日期和性别显示在浅灰色字段中。
A) 键盘
如果选择，将显示以下屏幕。
图3-5软件键盘选择名为“ 姓氏” 的浅灰色字段，用键盘添加数据。
姓名和生日也是如此。性别可以在性别下方的下拉菜单中选择。按Enter键关闭屏幕，将所有输入的数据转移
到上一个屏幕。
B) 可选条形码
患者数据可以通过条形码阅读器上传。
3.3 质量控制模块
根据药物非临床研究质量管理规范（GLP），必须每天使用对照品，以确保系统和试剂正常工作。
QC模块允许上载控制材料的目标值、测量QC样品和操作服务功能工具，标准试剂盒（参见第4.9章“ 服务和维
护” ）。
必须始终提前上传目标值，否则无法通过卡匣运行控制材料。请参见第3.3.1章“ 上传新的控制材料” 。
关于制备对照品的更多信息，例如复溶或向测试盒中移液对照体积，请遵循对照品和相应测试参数的使用说
明。对照品的使用方式与相同参数类型的样品相同。
要运行每日控制，有两种模式可用。


---

## 第 25 页

HumaFIA |用户手册
27
1.
自动模式下的运行控制：如果只需要检查少量的对照材料，且在孵育和读取期间无需人工操作，则
推荐使用此选项。
2.
快速模式下运行控制：如果需要在短时间内运行不同级别和参数的控制材料，建议使用此选项。
图3-6质量控制主界面
上传的控制的批号、目标值（范围）和参数显示在一行中。请注意，每个控制级别显示在单独的一行中。
“ 结果” 列显示最后记录的值，日期和时间信息在“ 日期时间” 列中可用。
如果结果与目标值匹配，则列“ 范围” 中的标志显示P，表示通过。如果列“ 范围” 中的标志为红色，并显示
CH或CL，则表示控制结果过高（CH）或过低（CL）。
要删除已过期的控件或其他原因，请按要删除的控件前面的深灰色按钮。
如果当前有一个控制装置处于自动模式，并且留在分析仪中进行孵育和读取，则会显示沙漏。沙漏旁边的数
字表示剩余的孵育时间（以毫米为单位）：ss。
控制读数的最新结果显示在最后一行。
注：要删除控制结果，请按下每个列出的控制结果上的深灰色按钮。
3.3.1 上传新的控制材料
控制套件包含目标值表，提供参数必须匹配的范围和批号。


---

## 第 26 页

HumaFIA |用户手册
28
图3-7从目标值表上传控制数据
为了上传控制的所有数据（批次、水平、目标值），需要控制的目标值表。
首先按下“ Card” 名称。这将打开下一个窗口，用于选择控制参数，例
如示例中的PCT。
选择控制级别。
输入批号。
选择到期日期。
输入目标值（根据参数的单位）。
要完成新控件的上载，请按“ 添加” 按钮。
要删除一个控件，请按控件左侧的深灰色按钮。如果控件已过期，可能
需要这样做。如果控件不再有效，软件会显示红色的“ Exp” 标志。
按照控制盒的使用说明准备控制。
3.3.2 在自动模式下运行控制
在QC模块屏幕中选择“ 自动模式” 控制按钮，以访问下面的屏幕。这允许用户选择在自动模式下分析的下一
个控制。


---

## 第 27 页

HumaFIA |用户手册
29
图3-8选择一个控制材料，在自动模式下运行它
屏幕提供所有已上传控制材料的列表。每个控制级别均被视为单独的控制材料，需要进行分析或运行。
通过按下下一运行的对照材料行中的深灰色按钮来选择对照。X表示已选对照材料。只能选择一种对照材料。
根据对照组的IFU值和参数，向卡盒中添加复溶的对照材料。随后按下“ 插入对照进行孵育” 按钮，在自动模
式下运行对照实验。待孵育并读取结果后，该界面关闭，结果会显示在之前的界面上。
注：列LOT中的红色Exp标志表示该批次的质控品已过期，必须用新批次替代。
3.3.3 在快速模式下运行控制
点击QC模块屏幕上的“ 添加控制到快速模式列表” 按钮，进入该窗口，可选择下一个要添加到快速模式列表
中的控制材料。


---

## 第 28 页

HumaFIA |用户手册
30
图3-9选择一个或多个要进入快速模式列表的控件
屏幕提供所有已上传控制材料的列表。每个控制级别被视为单独的控制材料，以供分析。
注：列LOT中的红色Exp标志表示该批次的质控品已过期，必须用新批次的质控品替换。
按下待分析对照材料行中的深灰色按钮，选择对照材料。X表示已选择的对照材料。可以选取一系列对照材
料，即使参数不同，也可以将其添加到下图所示屏幕上的自动模式列表中。
按下“ 添加选定控件” 按钮，将这些控件材料添加到快速模式列表中。这样就可以以系列方式运行这些控
件。新增一行，如果在孵育时间后有可用的时间段来读取控件，则该行显示“ Next Con.” ，更多详情如下。
图3-10快速模式下要运行的控制的工作列表
按照以下说明对每种选定的对照品进行快速模式下的控制。


---

## 第 29 页

HumaFIA |用户手册
31
在快速模式工作列表的“ 计时器” 列中，会出现“ 下一个控制材料” 按钮。秒数表示可以
开始快速模式下的第一个（或下一个）控制材料的时间。如果秒数达到00，测试就可以开
始了。此时，会显示添加的“ 80µl卷” 按钮。
下一步是将80µl的对照品加入到卡盒中。完成后，立即按下此按钮。这将开始监测该对照
读数的孵育时间。详情请参见IFU。
对于该对照，剩余孵育时间现在以mm：ss的格式显示。当该对照的孵育时间快结束时，将
出现“ 读取” 按钮。
现在还有10秒时间将测试盒插入分析仪并按下“ 读取” 按钮。为了获得最佳结果，请在计
时器达到00时按下“ 读取” 按钮。读取过程仅需几秒钟，随后将显示该控制参数的结果。
如果时间正确，--：- -出现，表明孵育时间正确或在可接受范围内。
如果出现红色数字，例如+02：11，则孵育时间延长了2分钟11秒，表明结果无效，必须重
复检测。
要运行一系列控制材料，请启动上述用于下一个控制的工作流。
一旦第一个控制的孵育时间开始，这个按钮就会显示出来，提示可以准备快速工作列表
中的下一个控制。工作流程程序会运行一个计时器，允许用户准备下一个控制。当计时
器归零时，用户可以选择这个控制进行移液操作。
现在进行第二次对照，将80µl的对照体积移液到卡盒中。完成后立即按下此按钮。现在
开始监测工作列表中第二次对照的孵育时间。
对上述系列进行处理，执行第一次和所有后续控制，直到快速工作列表为空。
一旦快速工作列表为空，则每行将显示控制材料的以下信息：批次、读取日期和时间、控制低和高目标值、
控制水平和控制参数的记录结果。
注：计时器栏中的红色数字表示孵育时间不正确。必须使用另一个试剂盒重复测量。
3.3.4 QC模块中的数据库
QC控制测量的所有结果均显示在以下屏幕上，可通过按下QC模块主屏幕上的数据库按钮访问：


---

## 第 30 页

HumaFIA |用户手册
32
图3-11质控结果数据库
可以通过点击要删除的数据集的深灰色按钮来删除数据集。通过“ 向上翻页” 和“ 向下翻页” 按钮可以访问
更多的数据集。
3.3.5 QC模块中的校准卡概述
按下QC模块主屏幕上的“ 加载新校准卡” 按钮，可查看已上传至分析仪的所有校准卡、其批次、失效日期和
参数。
图3-12 QC模块中上传的校准卡概述
添加校准卡：上传当前插入分析仪的校准卡的数据。一旦将参数的校准卡插入分析仪，就会自动进行此操
作。通过按下要删除的数据集的深灰色按钮，可以删除数据集。


---

## 第 31 页

HumaFIA |用户手册
33
注：仅显示参数校准卡，此处不显示标准试剂盒。
3.4 数据库接口
前几章介绍了如何在三种采样模式下收集测试结果。接下来的部分将介绍如何检索记录的结果和相关的信
息。
注：数据库非常大，并非所有数据都相关。第4.7章“ 数据库中数据字段的选择” 描述了如何设置要显示的数
据。
注：数据库非常大，并非所有数据都相关。第3.6.4章描述了如何设置要显示的数据。
数据库显示在设置/数据选择中定义的所有记录数据。
3.4.1 一般业务
通过使用向上翻页和向下翻页按钮，可以访问更多数据集。从左向右滑动屏幕可显示更多数据。
如果需要导出或删除某些数据，请首先使用以下描述的过滤器功能：
“ 导出全部” 按钮将导出通过筛选器选择的所有数据。如果未应用筛选器，则将所有数据导出到USB。
“ 删除全部” 按钮将删除通过筛选器选择的所有数据。如果未应用筛选器，则将删除所有数据。小心点！
某些字段，例如“ 打印或导出” 列中的字段，默认设置为“ 否” 。按该字段可将此设置更改为“ 是” 。
要删除完整数据集，请按患者姓名旁边的废纸篓图标。
图3-13数据库中记录结果的审查


---

## 第 32 页

HumaFIA |用户手册
34
3.4.2 数据库过滤器功能
要选择用于存储、打印、导出或比较等操作的数据集，最好只选择感兴趣的那些数据集。按“ 搜索” 可进入
以下屏幕，该屏幕显示所有可能的搜索功能/筛选：
图3-14数据库中的过滤器功能
使用过滤器，用户可以选择第一行中的日期/时间1和第二行中的日期/时间2（年、月、日、小时、分钟）之
间记录的所有样本。
此外，还有一个筛选器可以选择以下一个或多个标准。
。姓，名
患者ID
。参数
。结果标志
计时器（关于正确/错误读数时间的评论）
全部
.样品类型
。已过期或未过期的读数
。标记出超出线性范围的结果


---

## 第 33 页

HumaFIA |用户手册
35
4 高级操作
警告：以下操作仅适用于有经验的用户。如果修改设置，警告可能会消失，数据将不再显示，或临床参考值
会改变。这可能导致错误标记和结果解释。因此，建议只有在用户完全理解设置的情况下才进行更改。
4.1 设置界面
在主菜单中点击设置，进入下面的显示界面。
图4-1设置屏幕，显示了可修改的设置组
可以为以下设置组定义设置：
-测试程序：设置警报，定义如何生成ID，或是否打印每个结果
-一般：日期、时间和温度的格式，以及语言
-参数：首先显示的参数，创建自定义参数和参考编号
-标志：选择应显示的标志
-一般(2)：运行软件更新，启用自动警告
-数据选择：数据库中显示哪些数据字段
-重置出厂设置：删除所有数据和设置，以恢复出厂设置
-连接性：定义通信信道的IP地址和端口
注：通过向上滑动右侧按钮，可以找到“ 连接” 按钮。它位于“ 重置出厂设置” 按钮下方。
每个模块在下面的单独章节中进行解释。


---

## 第 34 页

HumaFIA |用户手册
36
4.2 测试程序的设置
图4-2声音报警、患者ID设置及打印
可以定义声光报警器。每个报警器都可以启用/禁用。通过在激活字段中添加一个数字，可以选择报警器的类
型：
-1表示短蜂鸣
-2表示长蜂鸣
-3声短鸣表示2次
-4表示非常长的蜂鸣声
启用患者ID功能会自动为每位新患者分配一个连续的患者ID。此外，还可以设置患者ID前几位的前缀数字。
这些数字与其余部分之间用下划线隔开。例如，如果用户设置101作为前缀数字，那么第一位患者将被分配ID
101_1，第二位患者101_2，依此类推。这有助于日后更轻松地选择和过滤数据。通过“ 每日自动重置前缀”
选项，自动递增的数字每天早上从1开始——即自动重置功能。在这个例子中，每天早晨第一位患者的ID将是
101_1。
如果启用“ 自动打印每个结果（热打印机）” ，则可在生成结果后在集成热打印机上自动打印结果。
如果已激活“ 自动打印（外部打印机）上的所有结果” ，则会激活向外部打印机打印的功能。但是，只有加
载了外部打印机的打印机驱动程序后，此选项才可用。此功能当前不可用。
退出将应用所选设置并关闭窗口。


---

## 第 35 页

HumaFIA |用户手册
37
4.3 一般（日期、时间、语言、亮度、温度、删除）
图4-3设置日期、时间、语言、亮度、温度和删除警告
日期格式可以设置为YYYY. MM. DD或DD. MM.YYYY。D代表日，M代表月，Y代表年。
时间格式可以设置为hh：mm（0-24h）或hh：mm，AM/PM。h代表小时，m代表分钟。
亮度可通过+增加或减少
如果分析仪上安装了所需语言，可以更改语言。如果需要其他语言，请要求服务工程师上传。分析仪一次只
能支持5种语言。
温度格式可以设置为摄氏度（.C）或华氏度（.F）。长按“ 温度” 标题几秒钟，会进入一个屏幕，用户可以
在此调整温度。如果屏幕上显示的温度过高，可以通过降低温度来调整；反之亦然。例如，输入-4以将温度
降低4摄氏度。按下“ 设置” 应用温度调整，然后按“ 返回” 按钮回到常规设置屏幕。
删除
。数据：通过警告确认：如果启用此选项，将显示警告以确认删除数据。
.测试/控制确认选项可启用与测试和控制的使用相关的所有警告，例如过期的试剂提示。
退出将应用所选设置并关闭窗口。


---

## 第 36 页

HumaFIA |用户手册
38
4.4 参数列表选择
图4-4设置优先选择7个参数
在许多屏幕上，仅显示7个参数供选择。按下“ 其他” 按钮后，才能看到所有其他参数。此屏幕允许用户定义
参数的显示顺序。
Prio栏允许用户更改参数的显示顺序。1-7定义了第一页上的位置，其他所有位置都在其他页面上。使用键盘
将数字输入到选定的字段中。
个位数表示单个参数测试的优先级/位置。
如果参数是联合检测的一部分，则会显示两位数字。第一位数字表示该参数作为单独检测的位置，而第二位
数字则表示联合检测的位置。例如，如果Prio列显示cTnI为1.7，则单独的cTnI检测将显示在位置1，而联合
检测cTnI、Myo和CK-MB将出现在位置7（Myo和CK-MB这两个参数的第二位数字也将是7）。
对于每个参数，可以定义低和高截止值。在这两个值之间，结果被视为正常，并以黑色显示。如果结果较
高，则在测量屏幕上的结果旁边显示红色箭头。
低(L)表示患者显示异常低值的范围。此时，会出现一个红色向下箭头。
正常(N)表示患者健康或正常的参数范围。
高(H)表示患者显示异常高的值。在这种情况下，出现红色向上箭头。
按Page up/Page down可进入参数列表的其余部分。
按下“ 添加” 按钮后，会弹出如下界面，允许用户为上传的某个参数创建自定义参考范围。在下面的例子
中，创建了一个针对新生儿PCT的自定义参数。例如，新生儿根据年龄（以小时计）有不同的参考范围。这
里，创建了12小时大的新生儿的参考范围。选择“ Card名称” 和“ 参数” 为PCT。名称为


---

## 第 37 页

HumaFIA |用户手册
39
新参数为neo12h，可任意选择名称，然后将正常值范围设置为0.5至8.0 ng/ml，在弹出窗口中点击添加，创
建具有自定义参考范围的新参数。
最后一步是为新参数定义优先级。
图4-5添加自定义参考范围
在图4-5中，为PCT添加了一个新的参考范围。新参考范围用neo12h表示。在参数列表中，现在可以找到PCT_
neo12h。如果使用此参数（代替PCT)，则结果标记将采用新的参考范围。定义了PCT的新低(L)阈值和高(H)阈
值：
分别为0.5和8 ng/ml。
Add将自定义参考范围添加到新参数中，例如示例中的PCT_neo12h。
取消将关闭窗口，但不会应用任何更改。
回到设置屏幕，Del Mode允许用户通过按下“ 备注” 字段中的相应行来删除任何参数校准信息。
退出将应用所选设置并关闭窗口。


---

## 第 38 页

HumaFIA |用户手册
40
4.5 启用标志的设置
图4-6设置选项启用标志
结果数据中显示的所有标志都可以通过启用或禁用标志来设置为显示或隐藏。
如果激活了孵育时间不正确（长IL，短IS)的标志，则会显示IS和IL标志。如果这些标志出现，则由于孵育时
间不正确，必须重复测量。
红色箭头标志表示结果超出正常范围，如果激活“ 异常样本” 、“ 红色箭头上下” ，则显示该结果。
如果激活了“ 低、正常、高” 分类样本结果，则显示L、N和H标志。
如果激活了“ 测试卡／质控品过期” ，则显示Tx和Cx标志，表示测试批次（Tx）或质控品批次（Cx）已过
期。
如果激活了“ 控制超出范围（过低或过高）” ，则显示CL和CH标志。这表示控制结果超出目标值范围。
如果超出线性范围，但显示结果被激活，则会显示LH和LL标志。在这种情况下，即使结果超出线性范围，结
果仍然会在结果屏幕上显示。如果不启用，超出线性范围的结果不会显示。
退出将应用所选设置并关闭窗口。


---

## 第 39 页

HumaFIA |用户手册
41
4.6 常规（2）设置、软件更新、自动警告
注意：此设置仅在服务模式下可访问。请先从用户或管理员模式切换到服务模式。
图4-7运行软件更新并启用警告
失效品盒/测试
如果启用“ cart./test过期时停止操作” 设置，则软件将停止过期测试批次的操作。这保证了仅使用有效试
剂，从而获得正确结果。
如果设置Flag结果，如果已启用“ 测试盒已过期” （Tx-flag），则如果正在使用的测试批次已过期，则将结
果标记为Tx。
有效期控制
如果设置的Flag结果为“ 如果控制已过期（Cx-flag）启用” ，则如果正在使用的控制LOT过期，则用Cx标记
控制结果。
更新软件
必须将新软件版本传输到U盘并导入系统。如果接受，U盘会立即开始闪烁。按“ 更新” 按钮，软件更新将自
动从U盘上传。
注：只能使用串行连接端口旁边的USB端口来上传软件更新。
显示软件版本打开一个屏幕，显示系统上运行的当前软件版本。
退出将应用选定的设置并关闭窗口。


---

## 第 40 页

HumaFIA |用户手册
42
4.7 数据库中数据字段的选择
图4-8自定义数据库模块中数据字段的显示
数据库容量较大，存储了大量数据字段。此设置页面允许用户通过启用（?）或禁用(x)相应数据字段的选
项，自定义数据库中显示的内容或打印内容。
退出将应用选定的设置并关闭窗口。
4.8 建立连接通道的设置
注：此设置仅在服务模式下可访问。请先从用户或管理员模式切换到服务模式。
图4-9设置以太网连接及其他数据端口
要连接到internet，请添加IP地址、子网掩码、PC IP地址和PC端口信息。按每个字段以输入值。


---

## 第 41 页

HumaFIA |用户手册
43
点击关闭，结束数据上传。
Connect根据上传的数据启动连接。
激活设置LIS端口（以太网端口）可确保仪器启动后系统自动开始LIS通信。
如果启用“ 串行端口(PC接口）” 设置，则系统将在仪器启动后自动通过HumaFIA所连接的PC上的HumaFIA数据
线开始数据传输。
如果启用“ 串行端口” 设置，则仪器启动后，系统自动启动串行端口通信。
退出将应用所选设置并关闭窗口。
4.9 服务和维护
HumaFIA分析仪被封装在密封外壳内，测试过程中不会产生液体废物。只要正确处理试剂盒，仪器就不会接触
到血液样本，从而避免样本溢出。唯一的维护工具是标准试剂盒套件，具体说明如下。以下为必须执行的维
护工作：
.清除器械表面的灰尘。
当天所有测试完成后，关闭电源开关。
4.9.1 运行标准试剂盒作为质量检查
标准试剂盒套件是一种服务工具，允许用户检查分析仪的光学系统。建议每天作为仪器的质量检查运行。此
外，如果各种参数的控制超出范围，请联系服务工程师，并使用标准试剂盒套件的所有5个级别来检查分析
仪。还应在每6个月一次的服务维护期间使用。
该套件包含一份规格表，其中列出了标准墨盒的5个级别中的每个级别的参考值。根据级别不同，墨盒编号为
1-5。
标准试剂盒用作对照材料，因此使用QC模块屏幕进行操作。


---

## 第 42 页

HumaFIA |用户手册
44
图4-10 QC模块操作标准试剂盒
第一步，必须上传该标准试剂盒的目标值。一旦将标准试剂盒的校准卡插入分析仪，就会自动完成这一操
作。
图4-11标准试剂盒目标值成功上传
在QC模块屏幕上按下Load Standard Cartridge，以访问以下屏幕。然后按下STD-test，弹出窗口显示标准试
剂盒批次。在Gradient旁边，选择接下来应使用的标准试剂盒水平。
图4-12读取标准卡盒（STD）


---

## 第 43 页

HumaFIA |用户手册
45
按下“ 确定” 开始读取所选级别的标准墨盒。对每个级别重复此过程
对5级标准试剂盒对照材料进行检测，显示了当前读数结果以及上传的目标值范围。如图4-13所示，STD读数
的重现性应该非常高。
图4-13标准样品结果及目标值范围
如果结果在质量标准范围内，则出现消息“ QC通过” 。如果不是，重新运行标准试剂盒或使用新的批次。将
上传的目标值与试剂盒中的目标值表上的值进行比较，以确保已正确上传。如果问题仍然存在，请联系服务
工程师。


---

## 第 44 页

HumaFIA |用户手册
46
5 故障排除
本手册包括所有简单故障的故障排除信息。如果问题仍然存在，请联系当地的售后服务中心、当地代理商或
制造商。本设备附带一份合格证。请妥善保管合格证。用户和技术服务人员必须定期维护分析仪。
故障排除措施列表
故障内容
含义
分析仪启动时的自检失败。
警告621
自检显示存储存在问题
警告620
自检显示连接存在问题
分析仪的一个部件出现故障。确保墨盒滑块为空。
检查警告消息中提到的问题。
如果自检报警仍然存在，请联系服务工程师。
某一水平的控制不在范围内。
更换对照材料并重复对照测量。
如果问题仍然存在，请运行标准墨盒套件或联系服务工程师。
标准滤芯套件不匹配所有5个级别。
光学组件故障，请联系服务工程师。
在自动采样或快速模式下的“ 计时器” 列
中显示红色数字。红色数字如02：11以及
IL或IS标志显示如下警告：
警告300
孵育时间过长。结果不正确。
请重新运行样本。
HumaFIA对孵育时间的监测显示了不正确的时机。IL：孵育时间
过长，IS：孵育时间过短。
使用新的测试盒重复测量。
警告400
控制已过期，请使用新的批次
所用的对照材料已过期。用新的批次替换它，并上传新的目标
值。
警告402
超过24小时未进行控制测量
GLP建议每天使用一次控制。每24小时运行一次控制。
警告403
测试盒已过期，请使用新批次
测试盒批次已过期，用新批次替换，并上传新的校准数据。
警告404
无法记录当前参数。
GLP建议每天使用控制。运行一个控制，当控制成功匹配参数范
围后，软件将允许用户再次运行这个参数。


---

## 第 45 页

HumaFIA |用户手册
47
过去yy天内没有有效的对照测量。
请控制运行！
旧数据消失。
警告501
已达到最大数据量。将开始覆盖旧数据。
请导出/删除数据！
警告501提示数据存储空间不足。如果忽略此消息，将覆盖最早
的资料集。
警告100
环境温度不符合规范。
请在18至28℃的房间内重新运行样品
温度
HumaFIA具有温度传感器，用于监测环境温度。如果温度超出指
定范围，则所有结果都将被标记，并显示警告。由于孵育温度
不当，结果可能不正确。
警告502
结果超出线性范围。
请重新运行预稀释样本（自动/STAT）
HumaFIA提供软件选项，可在自动样本模式和STAT样本模式下自
动计算预稀释样品。
非线性结果超出分析仪质量标准，可能是错误的。重新运行预
稀释样品。
警告611
外部条形码阅读器未连接
检查外部条形码阅读器的电缆连接和状态LED。如需进一步帮
助，请参考外部条形码阅读器的用户手册。
警告612
LIS，以太网连接未激活
警告613
串行端口(PC接口）未激活。
请检查电缆
警告614
串行端口未激活。请检查电缆
检查两侧（分析仪和外部连接器）的电缆和连接。
请联系服务工程师以获得进一步的帮助。
警告615
外置打印机离线，请检查打印机状态
检查分析仪和外部打印机两侧的电缆和连接。请参考打印机用
户手册以获取进一步的帮助。
警告301
预稀释样品结果偏低，请在未稀释模式下
重新运行样品
由于预稀释需要手动移液步骤，因此尽可能使用未稀释样品。
如果结果处于正常线性范围内，则建议将样品作为正常样品再
次运行。
警告610需要维护。请寻求技术支持！
建议每6个月运行一次维护周期。
表5-1故障排除


---


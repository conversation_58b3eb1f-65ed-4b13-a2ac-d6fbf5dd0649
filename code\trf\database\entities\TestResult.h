#ifndef TESTRESULT_H
#define TESTRESULT_H

#include <QString>
#include <QDateTime>
#include <QDebug>
#include "../../addsamplepage.h" // 引用SourcePageType枚举

/**
 * 测试结果数据实体类
 * 包含测试结果的完整信息和验证方法
 */
class TestResult
{
public:
    TestResult();
    TestResult(int patientId, SourcePageType testMode, const QString& parameterType,
               const QString& sampleType, const QString& testResult);
    TestResult(int resultId, int patientId, SourcePageType testMode,
               const QString& parameterType, const QString& sampleType,
               const QString& testResult, const QString& resultStatus,
               const QDateTime& testDateTime, const QString& lotNumber,
               const QString& cutoffValue, double dilutionFactor);
    
    // Getter methods
    int getResultId() const { return m_resultId; }
    int getPatientId() const { return m_patientId; }
    SourcePageType getTestMode() const { return m_testMode; }
    QString getTestModeString() const;
    QString getParameterType() const { return m_parameterType; }
    QString getSampleType() const { return m_sampleType; }
    QString getTestResult() const { return m_testResult; }
    QString getResultStatus() const { return m_resultStatus; }
    QDateTime getTestDateTime() const { return m_testDateTime; }
    QString getLotNumber() const { return m_lotNumber; }
    QString getCutoffValue() const { return m_cutoffValue; }
    double getDilutionFactor() const { return m_dilutionFactor; }
    QDateTime getCreatedAt() const { return m_createdAt; }
    
    // Setter methods
    void setResultId(int resultId) { m_resultId = resultId; }
    void setPatientId(int patientId) { m_patientId = patientId; }
    void setTestMode(SourcePageType testMode) { m_testMode = testMode; }
    void setParameterType(const QString& parameterType) { m_parameterType = parameterType; }
    void setSampleType(const QString& sampleType) { m_sampleType = sampleType; }
    void setTestResult(const QString& testResult) { m_testResult = testResult; }
    void setResultStatus(const QString& resultStatus) { m_resultStatus = resultStatus; }
    void setTestDateTime(const QDateTime& testDateTime) { m_testDateTime = testDateTime; }
    void setLotNumber(const QString& lotNumber) { m_lotNumber = lotNumber; }
    void setCutoffValue(const QString& cutoffValue) { m_cutoffValue = cutoffValue; }
    void setDilutionFactor(double dilutionFactor) { m_dilutionFactor = dilutionFactor; }
    void setCreatedAt(const QDateTime& createdAt) { m_createdAt = createdAt; }
    
    /**
     * 验证测试结果数据有效性
     * @return bool 数据是否有效
     */
    bool isValid() const;
    
    /**
     * 获取验证错误信息
     * @return QString 错误描述
     */
    QString getValidationError() const;
    
    /**
     * 获取格式化的测试时间
     * @return QString 格式化后的时间
     */
    QString getFormattedDateTime() const;
    
    /**
     * 获取格式化的样本类型描述
     * @return QString 样本类型描述
     */
    QString getSampleTypeDescription() const;
    
    /**
     * 判断结果是否异常
     * @return bool 是否异常
     */
    bool isAbnormal() const;
    
    /**
     * 转换为界面显示用的数据结构
     */
    QString toDisplayString() const;
    
    /**
     * 静态方法：将SourcePageType转换为数据库字符串
     */
    static QString testModeToString(SourcePageType mode);
    static SourcePageType stringToTestMode(const QString& modeStr);
    
    /**
     * 判断两个测试结果对象是否相等
     */
    bool operator==(const TestResult& other) const;
    bool operator!=(const TestResult& other) const;
    
    /**
     * 调试输出
     */
    QString toString() const;

private:
    int m_resultId;                  // 结果ID
    int m_patientId;                 // 患者ID
    SourcePageType m_testMode;       // 测试模式
    QString m_parameterType;         // 参数类型
    QString m_sampleType;            // 样本类型 (S/P/B)
    QString m_testResult;            // 测试结果
    QString m_resultStatus;          // 结果状态
    QString m_lotNumber;             // 批号
    QString m_cutoffValue;           // 截止值
    double m_dilutionFactor;         // 稀释倍数
    QDateTime m_testDateTime;        // 测试时间
    QDateTime m_createdAt;           // 创建时间
    
    /**
     * 验证样本类型有效性
     */
    bool isValidSampleType(const QString& sampleType) const;
    
    /**
     * 验证参数类型有效性
     */
    bool isValidParameterType(const QString& parameterType) const;
};

// 调试输出支持
QDebug operator<<(QDebug debug, const TestResult& testResult);

#endif // TESTRESULT_H 
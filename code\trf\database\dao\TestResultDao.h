#ifndef TESTRESULTDAO_H
#define TESTRESULTDAO_H

#include <QObject>
#include <QList>
#include <QSqlQuery>
#include <QSqlError>
#include <QDateTime>
#include "../entities/TestResult.h"
#include "../../addsamplepage.h"

/**
 * 测试结果数据访问对象(DAO)
 * 提供测试结果数据的数据库CRUD操作
 */
class TestResultDao : public QObject
{
    Q_OBJECT

public:
    explicit TestResultDao(QObject *parent = nullptr);
    
    /**
     * 创建新测试结果记录
     * @param testResult 测试结果对象
     * @return int 新创建的测试结果ID，失败返回-1
     */
    int create(const TestResult& testResult);
    
    /**
     * 根据ID查找测试结果
     * @param resultId 测试结果ID
     * @return TestResult 测试结果对象，找不到返回无效对象
     */
    TestResult findById(int resultId);
    
    /**
     * 根据患者ID查找测试结果
     * @param patientId 患者ID
     * @param limit 限制返回数量，默认-1表示无限制
     * @return QList<TestResult> 测试结果列表
     */
    QList<TestResult> findByPatientId(int patientId, int limit = -1);
    
    /**
     * 根据测试模式查找测试结果
     * @param testMode 测试模式
     * @param limit 限制返回数量，默认100
     * @return QList<TestResult> 测试结果列表
     */
    QList<TestResult> findByTestMode(SourcePageType testMode, int limit = 100);
    
    /**
     * 根据时间范围查找测试结果
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param testMode 测试模式（可选）
     * @return QList<TestResult> 测试结果列表
     */
    QList<TestResult> findByDateRange(const QDateTime& startTime, const QDateTime& endTime, 
                                      SourcePageType testMode = SourcePageType::AUTO_SAMPLE);
    
    /**
     * 查找所有测试结果
     * @param limit 限制返回数量，默认-1表示无限制
     * @param offset 偏移量，默认0
     * @return QList<TestResult> 测试结果列表
     */
    QList<TestResult> findAll(int limit = -1, int offset = 0);
    
    /**
     * 更新测试结果
     * @param testResult 测试结果对象（必须包含有效的resultId）
     * @return bool 更新是否成功
     */
    bool update(const TestResult& testResult);
    
    /**
     * 删除测试结果记录
     * @param resultId 测试结果ID
     * @return bool 删除是否成功
     */
    bool remove(int resultId);
    
    /**
     * 检查测试结果是否存在
     * @param resultId 测试结果ID
     * @return bool 是否存在
     */
    bool exists(int resultId);
    
    /**
     * 获取测试结果总数
     * @param testMode 测试模式（可选过滤）
     * @return int 测试结果总数
     */
    int count(SourcePageType testMode = SourcePageType::AUTO_SAMPLE);
    
    /**
     * 根据参数类型搜索测试结果
     * @param parameterType 参数类型（模糊查询）
     * @param limit 限制返回数量
     * @return QList<TestResult> 匹配的测试结果列表
     */
    QList<TestResult> searchByParameter(const QString& parameterType, int limit = 50);
    
    /**
     * 获取最近的测试结果
     * @param testMode 测试模式
     * @param limit 限制返回数量
     * @return QList<TestResult> 测试结果列表
     */
    QList<TestResult> getRecentResults(SourcePageType testMode, int limit = 20);
    
    /**
     * 批量删除过期的测试结果
     * @param beforeDate 删除此日期之前的记录
     * @return int 删除的记录数量
     */
    int cleanupOldResults(const QDateTime& beforeDate);
    
    /**
     * 删除所有测试结果（谨慎使用）
     * @return bool 删除是否成功
     */
    bool deleteAllTestResults();
    
    /**
     * 获取最后的错误信息
     * @return QString 错误描述
     */
    QString getLastError() const;

private:
    QString m_lastError;
    
    /**
     * 从查询结果创建TestResult对象
     * @param query SQL查询对象
     * @return TestResult 测试结果对象
     */
    TestResult createTestResultFromQuery(const QSqlQuery& query);
    
    /**
     * 验证测试结果数据
     * @param testResult 测试结果对象
     * @return bool 数据是否有效
     */
    bool validateTestResult(const TestResult& testResult);
    
    /**
     * 设置错误信息
     * @param error 错误描述
     */
    void setError(const QString& error);
    
    /**
     * 绑定测试结果参数到查询
     * @param query SQL查询对象
     * @param testResult 测试结果对象
     */
    void bindTestResultParameters(QSqlQuery& query, const TestResult& testResult);
};

#endif // TESTRESULTDAO_H 
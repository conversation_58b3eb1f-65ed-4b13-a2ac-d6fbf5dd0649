#!/bin/bash

# TRF SQLite 驱动 Docker 提取脚本
# 备用方案：使用 Docker 从 Ubuntu 16.04 ARM 环境提取 SQLite 驱动

echo "=== TRF SQLite 驱动 Docker 提取工具 ==="
echo "使用 Ubuntu 16.04 ARM 环境提取兼容的 SQLite 驱动"
echo "适用于 myd-y6ull14x14 系统 (Qt 5.6.2)"
echo "=========================================="

# 检查 Docker 是否可用
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装或不可用"
    echo "请安装 Docker 后重试"
    exit 1
fi

# 检查输出目录参数
OUTPUT_DIR="${1:-./sqlite_drivers}"
mkdir -p "$OUTPUT_DIR"

echo "输出目录: $OUTPUT_DIR"
echo ""

# 步骤1：拉取 ARM Ubuntu 16.04 镜像
echo "=== 步骤1：准备 ARM Ubuntu 16.04 环境 ==="
echo "正在拉取 arm32v7/ubuntu:16.04 镜像..."

if ! docker pull arm32v7/ubuntu:16.04; then
    echo "❌ 无法拉取 ARM Ubuntu 镜像"
    echo "请检查网络连接和 Docker 配置"
    exit 1
fi

echo "✓ ARM Ubuntu 16.04 镜像已准备"
echo ""

# 步骤2：创建提取容器
echo "=== 步骤2：安装 Qt5 并提取 SQLite 驱动 ==="

# 创建临时 Dockerfile
cat > /tmp/sqlite_extractor.dockerfile << 'EOF'
FROM arm32v7/ubuntu:16.04

# 安装 Qt5 和 SQLite 驱动
RUN apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    qt5-default \
    qtbase5-dev \
    libqt5sql5-sqlite \
    libqt5widgets5 \
    libqt5gui5 \
    libqt5core5a \
    file \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建提取脚本
RUN echo '#!/bin/bash' > /extract_drivers.sh && \
    echo 'echo "=== 查找 SQLite 驱动 ==="' >> /extract_drivers.sh && \
    echo 'echo "Qt5 版本信息:"' >> /extract_drivers.sh && \
    echo 'qmake --version' >> /extract_drivers.sh && \
    echo 'echo ""' >> /extract_drivers.sh && \
    echo 'echo "查找 SQLite 驱动文件..."' >> /extract_drivers.sh && \
    echo 'find /usr -name "*sqlite*.so" -type f 2>/dev/null' >> /extract_drivers.sh && \
    echo 'echo ""' >> /extract_drivers.sh && \
    echo 'echo "Qt5 插件目录结构:"' >> /extract_drivers.sh && \
    echo 'find /usr -path "*/qt5/plugins*" -type d 2>/dev/null | head -10' >> /extract_drivers.sh && \
    echo 'echo ""' >> /extract_drivers.sh && \
    echo 'echo "SQLite 驱动详细信息:"' >> /extract_drivers.sh && \
    echo 'for driver in $(find /usr -name "*qsqlite*.so" -o -name "*sqlite*.so" | grep qt); do' >> /extract_drivers.sh && \
    echo '  if [ -f "$driver" ]; then' >> /extract_drivers.sh && \
    echo '    echo "驱动文件: $driver"' >> /extract_drivers.sh && \
    echo '    echo "  大小: $(stat -c%s "$driver") bytes"' >> /extract_drivers.sh && \
    echo '    echo "  文件信息: $(file "$driver")"' >> /extract_drivers.sh && \
    echo '    mkdir -p /output' >> /extract_drivers.sh && \
    echo '    cp "$driver" /output/$(basename "$driver")" 2>/dev/null && echo "  ✓ 已复制到输出目录"' >> /extract_drivers.sh && \
    echo '  fi' >> /extract_drivers.sh && \
    echo 'done' >> /extract_drivers.sh && \
    echo 'echo ""' >> /extract_drivers.sh && \
    echo 'echo "输出目录内容:"' >> /extract_drivers.sh && \
    echo 'ls -la /output/ 2>/dev/null || echo "输出目录为空"' >> /extract_drivers.sh && \
    chmod +x /extract_drivers.sh

CMD ["/extract_drivers.sh"]
EOF

echo "正在构建 SQLite 驱动提取器..."
if ! docker build --platform linux/arm/v7 -t trf-sqlite-extractor -f /tmp/sqlite_extractor.dockerfile .; then
    echo "❌ 构建失败"
    rm -f /tmp/sqlite_extractor.dockerfile
    exit 1
fi

rm -f /tmp/sqlite_extractor.dockerfile
echo "✓ SQLite 驱动提取器构建完成"
echo ""

# 步骤3：运行提取容器
echo "=== 步骤3：提取 SQLite 驱动 ==="

echo "正在运行提取容器..."
docker run --rm --platform linux/arm/v7 -v "$(realpath "$OUTPUT_DIR"):/output" trf-sqlite-extractor

echo ""

# 步骤4：验证提取结果
echo "=== 步骤4：验证提取结果 ==="

if [ "$(ls -A "$OUTPUT_DIR" 2>/dev/null)" ]; then
    echo "✓ SQLite 驱动提取成功！"
    echo ""
    echo "提取的文件："
    for file in "$OUTPUT_DIR"/*; do
        if [ -f "$file" ]; then
            echo "  $(basename "$file")"
            echo "    大小: $(stat -c%s "$file" 2>/dev/null || echo "unknown") bytes"
            echo "    类型: $(file "$file" 2>/dev/null | cut -d: -f2)"
        fi
    done
    echo ""
    echo "使用方法："
    echo "1. 将 $OUTPUT_DIR 中的文件复制到 TRF 应用的 plugins/sqldrivers/ 目录"
    echo "2. 运行: cp $OUTPUT_DIR/* /path/to/trf/plugins/sqldrivers/"
    echo "3. 使用修复的启动脚本启动 TRF"
    
else
    echo "❌ 未提取到任何文件"
    echo ""
    echo "可能的原因："
    echo "1. Ubuntu 16.04 ARM 镜像中没有 SQLite 驱动"
    echo "2. 包安装失败"
    echo "3. 驱动位置与预期不同"
    echo ""
    echo "请尝试："
    echo "1. 检查网络连接"
    echo "2. 手动运行: docker run --rm --platform linux/arm/v7 trf-sqlite-extractor"
    echo "3. 使用其他源获取 SQLite 驱动"
fi

echo ""

# 步骤5：清理
echo "=== 步骤5：清理临时资源 ==="
echo "正在清理 Docker 镜像..."
docker rmi trf-sqlite-extractor 2>/dev/null || true
echo "✓ 清理完成"

echo ""
echo "=== Docker 提取完成 ==="
echo "SQLite 驱动文件位置: $OUTPUT_DIR"
echo "如需帮助，请查看 docs/Minimal_System_SQLite_Fix.md" 
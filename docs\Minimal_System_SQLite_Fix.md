# TRF SQLite 驱动修复指南

## 问题描述
在 myd-y6ull14x14 系统上运行 TRF 应用时遇到 SQLite 驱动缺失问题：
```
QSqlDatabase: QSQLITE driver not loaded
QSqlDatabase: available drivers: 
Database initialization failed: Cannot establish database connection
```

## 原因分析
- 应用编译使用 Qt 5.5.1，运行环境为 Qt 5.6.2
- Qt 版本不匹配导致 SQLite 驱动无法加载
- 需要将系统 Qt 5.6.2 的 SQLite 驱动复制到应用目录

## 解决方案

### 方法1：快速修复（推荐）

在 myd-y6ull14x14 设备上执行：

```bash
# 1. 创建插件目录
mkdir -p /mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers

# 2. 复制 SQLite 驱动
cp /usr/lib/qt5/plugins/sqldrivers/libqsqlite.so /mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers/

# 3. 创建修复启动脚本
cat > /mnt/sd/TRF-********-Linux-ARM-v7l/run_fixed.sh << 'EOF'
#!/bin/bash
APP_DIR="/mnt/sd/TRF-********-Linux-ARM-v7l"
cd "$APP_DIR"
export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins"
export LC_ALL=C
./trf
EOF

# 4. 设置执行权限
chmod +x /mnt/sd/TRF-********-Linux-ARM-v7l/run_fixed.sh

# 5. 测试运行
cd /mnt/sd/TRF-********-Linux-ARM-v7l && ./run_fixed.sh
```

### 方法2：自动化部署脚本（推荐）

使用专门的部署脚本自动处理：

```bash
# 将 scripts/deploy_sqlite_driver.sh 传输到设备
chmod +x deploy_sqlite_driver.sh
./deploy_sqlite_driver.sh /mnt/sd/TRF-********-Linux-ARM-v7l
```

### 方法3：从新版本发布包获取

从 GitHub Actions 构建的新版本发布包中，SQLite 驱动已自动包含：

```bash
# 下载最新发布包，驱动已包含在 plugins/ 目录中
# 直接使用 run.sh 启动即可
cd /mnt/sd/TRF-********-Linux-ARM-v7l && ./run.sh
```

### 方法4：Docker 环境提取（高级）

在有 Docker 的环境中提取兼容驱动：

```bash
# 在开发机器上运行
chmod +x scripts/extract_sqlite_driver_docker.sh
./scripts/extract_sqlite_driver_docker.sh ./sqlite_output

# 将提取的驱动传输到设备
scp ./sqlite_output/* root@myd-y6ull14x14:/mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers/
```

## 验证修复

成功修复后，应用启动时应显示：
```
"QApplication created successfully"
"Starting Qt plugin path configuration..."
Available SQL Drivers: QSQLITE
"HumaFIA database initialized successfully"
```

## 故障排除

### 如果仍然无法找到驱动：

1. **手动查找 SQLite 驱动：**
```bash
find /usr -name "*sqlite*.so" -type f 2>/dev/null
```

2. **检查系统 Qt 版本：**
```bash
strings /usr/lib/libQt5Core.so.5 | grep "5\." | head -1
```

3. **验证驱动复制：**
```bash
ls -la /mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers/
```

### 其他可能位置：
- `/usr/lib/plugins/sqldrivers/`
- `/usr/lib/qt5/sqldrivers/`
- `/lib/qt5/plugins/sqldrivers/`

## 环境变量设置

关键环境变量：
```bash
export QT_PLUGIN_PATH="/mnt/sd/TRF-********-Linux-ARM-v7l/plugins:/usr/lib/qt5/plugins"
export QT_SQL_DRIVERS="/mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers"
export LC_ALL=C
```

## 技术细节

- **系统**: Linux myd-y6ull14x14 4.1.15+ armv7l
- **Qt 运行时**: 5.6.2
- **应用编译版本**: 5.5.1
- **架构**: ARM v7l
- **驱动文件**: libqsqlite.so

## 成功标志

修复成功后，TRF 应用将：
1. 正常加载 SQLite 驱动
2. 成功连接数据库
3. 显示登录界面
4. 所有数据库功能正常工作 
# 登录界面背景和标题栏修改任务

## 任务描述
1. 将登录界面背景改为 `background_login.png`，放在最底层，不遮挡登录框
2. 移除窗口标题栏，隐藏最小化、最大化、关闭按钮

## 执行计划
1. 修改 `loginscreen.ui` 的 styleSheet，添加背景图片设置
2. 修改 `loginscreen.cpp`，移除 QPalette 背景代码，添加无边框窗口标志
3. 清理不必要的头文件引用

## 已完成的修改

### 1. loginscreen.ui
- 在 styleSheet 中添加了 `#LoginScreen` 的背景设置
- 使用 `background-image: url(:/images/background_login.png)`
- 设置 `background-position: center` 居中显示
- 设置 `background-repeat: no-repeat` 不重复
- 设置 `background-attachment: fixed` 固定背景

### 2. loginscreen.cpp
- 移除了 QPalette 背景设置代码
- 添加了 `setWindowFlags(Qt::FramelessWindowHint)` 隐藏标题栏
- 移除了不再需要的 `#include <QPalette>` 和 `#include <QPixmap>`

## 预期效果
- 登录界面显示 `background_login.png` 作为背景
- 背景在最底层，不遮挡半透明的登录框
- 窗口无标题栏，无最小化/最大化/关闭按钮

## 问题修复

### 背景图片问题
- **问题1**: 背景图片不显示
- **问题2**: 背景图片应用到所有子控件，包括登录框
- **问题3**: Qt CSS不支持 `background-size` 属性
- **解决方案**: 
  - 使用QPalette代替CSS设置背景
  - 添加resize事件处理确保背景正确缩放
  - 使用 `Qt::KeepAspectRatioByExpanding` 保持宽高比
- **修改**: 
  - 添加 `updateBackground()` 方法
  - 添加 `resizeEvent()` 处理窗口大小变化
  - 使用QPalette::Window设置背景

### 全局无边框设置
- **主窗口**: 在 `mainwindow.cpp` 中添加 `setWindowFlags(Qt::FramelessWindowHint)`
- **登录窗口**: 在 `loginscreen.cpp` 中添加 `setWindowFlags(Qt::FramelessWindowHint)`
- **效果**: 所有窗口都无标题栏 
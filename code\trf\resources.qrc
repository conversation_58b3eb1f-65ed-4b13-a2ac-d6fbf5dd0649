<!DOCTYPE RCC><RCC version="1.0">
<qresource prefix="/">
    <file>styles/global_colors.qss</file>
    <file>images/background_login.png</file>
    <file>images/logo.png</file>
    <file>images/icon_calibration.png</file>
    <file>images/icon_settings_main.png</file>
    <file>images/icon_statistics.png</file>
    <file>images/icon_sampling.png</file>
    <file>images/icon_database.png</file>
    <file>images/icon_logout.png</file>
    <file>images/icon_qc.png</file>
    <file>images/icon_settings.png</file>
    <file alias="images/auto_sample.png">images/icon_settings.png</file>
    <file alias="images/qc_module.png">images/icon_sampling.png</file>
    <file alias="images/stat_sample.png">images/icon_settings.png</file>
    <file alias="images/database.png">images/icon_qc.png</file>
    <file alias="images/fast_mode.png">images/icon_logout.png</file>
    <file alias="images/settings.png">images/icon_database.png</file>
    <file>images/header_home.png</file>
    <file>images/header_lock.png</file>
    <file>images/logo_humafia.png</file>
    
    <!-- Main interface images for header buttons -->
    <file>images/main_auto.png</file>
    <file>images/main_sampling.png</file>
    <file>images/main_manual.png</file>
    <file>images/main_qc.png</file>
    <file>images/main_database.png</file>
    <file>images/main_settings.png</file>
    
    <!-- Common header button background images -->
    <file>images/common/red_sample.png</file>
    <file>images/common/red_fast.png</file>
    <file>images/common/red_qc.png</file>
    <file>images/common/red_database.png</file>
    <file>images/common/red_settings.png</file>
    <file>images/common/white_sample.png</file>
    <file>images/common/white_fast.png</file>
    <file>images/common/white_qc.png</file>
    <file>images/common/white_database.png</file>
    <file>images/common/white_settings.png</file>
    
    <!-- Auto sample page images -->
    <file>images/auto_sample/patient_id.png</file>
    
    <!-- Add sample page images -->
    <file>images/add_sample/patient_id.png</file>
    <file>images/add_sample/parameter.png</file>
    <file>images/add_sample/add_sample.png</file>
    <file>images/add_sample/cancel.png</file>
    <file>images/add_sample/delete.png</file>
    <file>images/add_sample/insert.png</file>
    <file>images/add_sample/read.png</file>
    <file>images/add_sample/fast.png</file>
    <file>images/add_sample/other.png</file>
    <file>images/add_sample/exit.png</file>
    <file>images/auto_sample/parameter.png</file>
    <file>images/auto_sample/result.png</file>
    <file>images/auto_sample/date_time.png</file>
    <file>images/auto_sample/timer.png</file>
    <file>images/auto_sample/lot.png</file>
    <file>images/auto_sample/cut_off.png</file>
    <file>images/auto_sample/add_sample.png</file>
    <file>images/auto_sample/incubation.png</file>
    
    <!-- Fast mode page images -->
    <file>images/fast_mode/fast_header_1.png</file>
    <file>images/fast_mode/next_sample.png</file>
    <file>images/fast_mode/add_80.png</file>
    <file>images/fast_mode/incubation.png</file>
    <file>images/fast_mode/read.png</file>
</qresource>
</RCC> 
#include "TestResultDao.h"
#include "../connection/DatabaseConnection.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QDebug>

TestResultDao::TestResultDao(QObject *parent)
    : QObject(parent)
{
}

int TestResultDao::create(const TestResult& testResult)
{
    if (!validateTestResult(testResult)) {
        setError("Test result data validation failed: " + testResult.getValidationError());
        return -1;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return -1;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        INSERT INTO test_results (patient_id, test_mode, parameter_type, sample_type, 
                                test_result, result_status, test_datetime, lot_number, 
                                cutoff_value, dilution_factor, created_at)
        VALUES (:patient_id, :test_mode, :parameter_type, :sample_type, 
                :test_result, :result_status, :test_datetime, :lot_number, 
                :cutoff_value, :dilution_factor, :created_at)
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare insert query: " + query.lastError().text());
        return -1;
    }
    
    bindTestResultParameters(query, testResult);
    
    if (!query.exec()) {
        setError("Failed to insert test result: " + query.lastError().text());
        return -1;
    }
    
    int newResultId = query.lastInsertId().toInt();
    qDebug() << "Test result created successfully with ID:" << newResultId;
    return newResultId;
}

TestResult TestResultDao::findById(int resultId)
{
    TestResult result;
    
    if (resultId <= 0) {
        setError("Result ID must be greater than 0");
        return result;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return result;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE result_id = :result_id
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return result;
    }
    
    query.bindValue(":result_id", resultId);
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return result;
    }
    
    if (query.next()) {
        result = createTestResultFromQuery(query);
    } else {
        setError(QString("No test result found with ID %1").arg(resultId));
    }
    
    return result;
}

QList<TestResult> TestResultDao::findByPatientId(int patientId, int limit)
{
    QList<TestResult> results;
    
    if (patientId <= 0) {
        setError("Patient ID must be greater than 0");
        return results;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE patient_id = :patient_id 
        ORDER BY test_datetime DESC
    )";
    
    if (limit > 0) {
        sql += QString(" LIMIT %1").arg(limit);
    }
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return results;
    }
    
    query.bindValue(":patient_id", patientId);
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

QList<TestResult> TestResultDao::findByTestMode(SourcePageType testMode, int limit)
{
    QList<TestResult> results;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE test_mode = :test_mode 
        ORDER BY test_datetime DESC
    )";
    
    if (limit > 0) {
        sql += QString(" LIMIT %1").arg(limit);
    }
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return results;
    }
    
    query.bindValue(":test_mode", TestResult::testModeToString(testMode));
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

QList<TestResult> TestResultDao::findByDateRange(const QDateTime& startTime, const QDateTime& endTime, SourcePageType testMode)
{
    QList<TestResult> results;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE test_datetime BETWEEN :start_time AND :end_time
    )";
    
    if (testMode != SourcePageType::AUTO_SAMPLE) {
        sql += " AND test_mode = :test_mode";
    }
    
    sql += " ORDER BY test_datetime DESC";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare select query: " + query.lastError().text());
        return results;
    }
    
    query.bindValue(":start_time", startTime);
    query.bindValue(":end_time", endTime);
    
    if (testMode != SourcePageType::AUTO_SAMPLE) {
        query.bindValue(":test_mode", TestResult::testModeToString(testMode));
    }
    
    if (!query.exec()) {
        setError("Failed to execute select query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

QList<TestResult> TestResultDao::findAll(int limit, int offset)
{
    QList<TestResult> results;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        ORDER BY test_datetime DESC
    )";
    
    if (limit > 0) {
        sql += QString(" LIMIT %1").arg(limit);
        if (offset > 0) {
            sql += QString(" OFFSET %1").arg(offset);
        }
    }
    
    if (!query.exec(sql)) {
        setError("Failed to execute select query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

bool TestResultDao::update(const TestResult& testResult)
{
    if (testResult.getResultId() <= 0) {
        setError("Result ID must be greater than 0 for update");
        return false;
    }
    
    if (!validateTestResult(testResult)) {
        setError("Test result data validation failed: " + testResult.getValidationError());
        return false;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        UPDATE test_results 
        SET patient_id = :patient_id, test_mode = :test_mode, parameter_type = :parameter_type,
            sample_type = :sample_type, test_result = :test_result, result_status = :result_status,
            test_datetime = :test_datetime, lot_number = :lot_number, cutoff_value = :cutoff_value,
            dilution_factor = :dilution_factor
        WHERE result_id = :result_id
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare update query: " + query.lastError().text());
        return false;
    }
    
    bindTestResultParameters(query, testResult);
    query.bindValue(":result_id", testResult.getResultId());
    
    if (!query.exec()) {
        setError("Failed to update test result: " + query.lastError().text());
        return false;
    }
    
    int affectedRows = query.numRowsAffected();
    if (affectedRows == 0) {
        setError(QString("No test result found with ID %1").arg(testResult.getResultId()));
        return false;
    }
    
    qDebug() << "Test result updated successfully, ID:" << testResult.getResultId();
    return true;
}

bool TestResultDao::remove(int resultId)
{
    if (resultId <= 0) {
        setError("Result ID must be greater than 0");
        return false;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "DELETE FROM test_results WHERE result_id = :result_id";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare delete query: " + query.lastError().text());
        return false;
    }
    
    query.bindValue(":result_id", resultId);
    
    if (!query.exec()) {
        setError("Failed to delete test result: " + query.lastError().text());
        return false;
    }
    
    int affectedRows = query.numRowsAffected();
    if (affectedRows == 0) {
        setError(QString("No test result found with ID %1").arg(resultId));
        return false;
    }
    
    qDebug() << "Test result deleted successfully, ID:" << resultId;
    return true;
}

bool TestResultDao::exists(int resultId)
{
    if (resultId <= 0) {
        return false;
    }
    
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "SELECT COUNT(*) FROM test_results WHERE result_id = :result_id";
    
    if (!query.prepare(sql)) {
        return false;
    }
    
    query.bindValue(":result_id", resultId);
    
    if (!query.exec() || !query.next()) {
        return false;
    }
    
    return query.value(0).toInt() > 0;
}

int TestResultDao::count(SourcePageType testMode)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return -1;
    }
    
    QSqlQuery query(db);
    QString sql;
    
    if (testMode == SourcePageType::AUTO_SAMPLE) {
        // 如果是默认值，统计所有记录
        sql = "SELECT COUNT(*) FROM test_results";
    } else {
        sql = "SELECT COUNT(*) FROM test_results WHERE test_mode = :test_mode";
    }
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare count query: " + query.lastError().text());
        return -1;
    }
    
    if (testMode != SourcePageType::AUTO_SAMPLE) {
        query.bindValue(":test_mode", TestResult::testModeToString(testMode));
    }
    
    if (!query.exec() || !query.next()) {
        setError("Failed to execute count query: " + query.lastError().text());
        return -1;
    }
    
    return query.value(0).toInt();
}

QList<TestResult> TestResultDao::searchByParameter(const QString& parameterType, int limit)
{
    QList<TestResult> results;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE parameter_type LIKE :parameter_type 
        ORDER BY test_datetime DESC
        LIMIT :limit
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare search query: " + query.lastError().text());
        return results;
    }
    
    QString searchPattern = QString("%%1%").arg(parameterType);
    query.bindValue(":parameter_type", searchPattern);
    query.bindValue(":limit", limit);
    
    if (!query.exec()) {
        setError("Failed to execute search query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

QList<TestResult> TestResultDao::getRecentResults(SourcePageType testMode, int limit)
{
    QList<TestResult> results;
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return results;
    }
    
    QSqlQuery query(db);
    QString sql = R"(
        SELECT result_id, patient_id, test_mode, parameter_type, sample_type, 
               test_result, result_status, test_datetime, lot_number, 
               cutoff_value, dilution_factor, created_at
        FROM test_results 
        WHERE test_mode = :test_mode 
        ORDER BY test_datetime DESC
        LIMIT :limit
    )";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare recent results query: " + query.lastError().text());
        return results;
    }
    
    query.bindValue(":test_mode", TestResult::testModeToString(testMode));
    query.bindValue(":limit", limit);
    
    if (!query.exec()) {
        setError("Failed to execute recent results query: " + query.lastError().text());
        return results;
    }
    
    while (query.next()) {
        results.append(createTestResultFromQuery(query));
    }
    
    return results;
}

int TestResultDao::cleanupOldResults(const QDateTime& beforeDate)
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return -1;
    }
    
    QSqlQuery query(db);
    QString sql = "DELETE FROM test_results WHERE test_datetime < :before_date";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare cleanup query: " + query.lastError().text());
        return -1;
    }
    
    query.bindValue(":before_date", beforeDate);
    
    if (!query.exec()) {
        setError("Failed to execute cleanup query: " + query.lastError().text());
        return -1;
    }
    
    int deletedRows = query.numRowsAffected();
    qDebug() << "Cleaned up" << deletedRows << "old test results";
    return deletedRows;
}

QString TestResultDao::getLastError() const
{
    return m_lastError;
}

TestResult TestResultDao::createTestResultFromQuery(const QSqlQuery& query)
{
    TestResult result;
    result.setResultId(query.value("result_id").toInt());
    result.setPatientId(query.value("patient_id").toInt());
    result.setTestMode(TestResult::stringToTestMode(query.value("test_mode").toString()));
    result.setParameterType(query.value("parameter_type").toString());
    result.setSampleType(query.value("sample_type").toString());
    result.setTestResult(query.value("test_result").toString());
    result.setResultStatus(query.value("result_status").toString());
    result.setTestDateTime(query.value("test_datetime").toDateTime());
    result.setLotNumber(query.value("lot_number").toString());
    result.setCutoffValue(query.value("cutoff_value").toString());
    result.setDilutionFactor(query.value("dilution_factor").toDouble());
    result.setCreatedAt(query.value("created_at").toDateTime());
    
    return result;
}

bool TestResultDao::validateTestResult(const TestResult& testResult)
{
    return testResult.isValid();
}

void TestResultDao::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "TestResultDao Error:" << error;
}

void TestResultDao::bindTestResultParameters(QSqlQuery& query, const TestResult& testResult)
{
    query.bindValue(":patient_id", testResult.getPatientId());
    query.bindValue(":test_mode", TestResult::testModeToString(testResult.getTestMode()));
    query.bindValue(":parameter_type", testResult.getParameterType());
    query.bindValue(":sample_type", testResult.getSampleType());
    query.bindValue(":test_result", testResult.getTestResult());
    query.bindValue(":result_status", testResult.getResultStatus());
    query.bindValue(":test_datetime", testResult.getTestDateTime());
    query.bindValue(":lot_number", testResult.getLotNumber());
    query.bindValue(":cutoff_value", testResult.getCutoffValue());
    query.bindValue(":dilution_factor", testResult.getDilutionFactor());
    query.bindValue(":created_at", QDateTime::currentDateTime());
}

bool TestResultDao::deleteAllTestResults()
{
    QSqlDatabase db = DatabaseConnection::getConnection();
    if (!db.isValid()) {
        setError("Database connection is not valid");
        return false;
    }
    
    QSqlQuery query(db);
    QString sql = "DELETE FROM test_results";
    
    if (!query.prepare(sql)) {
        setError("Failed to prepare delete all query: " + query.lastError().text());
        return false;
    }
    
    if (!query.exec()) {
        setError("Failed to execute delete all query: " + query.lastError().text());
        return false;
    }
    
    qDebug() << "Successfully deleted" << query.numRowsAffected() << "test results";
    return true;
} 
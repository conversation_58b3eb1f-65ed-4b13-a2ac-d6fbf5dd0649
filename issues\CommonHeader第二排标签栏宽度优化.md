# CommonHeader第二排标签栏宽度优化

## 需求描述
优化 CommonHeader 第2排标签栏横向宽度，整体减少48px，使两边的边框更加清晰可见。

## 优化方案
采用居中缩减布局，从1024px宽度减少到976px，两边各留24px边距。

## 修改内容

### 布局调整
- **原始配置**：
  - 总宽度：1024px (0-1024)
  - 按钮位置：{0, 171, 341, 512, 682, 853}
  - 按钮宽度：{171, 170, 171, 170, 171, 171}

- **优化后配置**：
  - 总宽度：976px (24-1000)
  - 按钮位置：{24, 186, 349, 511, 674, 836}
  - 按钮宽度：{162, 163, 162, 163, 162, 164}

### 文件修改：code/trf/commonheader.cpp
```cpp
// 修改1：setupUi函数中的布局配置
int buttonX[] = {24, 186, 349, 511, 674, 836};
int buttonW[] = {162, 163, 162, 163, 162, 164};

// 修改2：createNavButtons函数中的布局配置
int buttonX[] = {24, 186, 349, 511, 674, 836};
int buttonW[] = {162, 163, 162, 163, 162, 164};
```

## 优化效果
- ✅ 标签栏总宽度减少48px
- ✅ 居中布局，两边边距各24px
- ✅ 两边边框更加清晰可见
- ✅ 保持按钮功能不变

## 状态
✅ 已完成优化 - 标签栏布局更加精致 
#ifndef COMMONHEADER_H
#define COMMONHEADER_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QDateTime>

class CommonHeader : public QWidget
{
    Q_OBJECT

public:
    explicit CommonHeader(QWidget *parent = nullptr);
    ~CommonHeader();

public slots:
    void setActiveButton(int index);
    void setNavigationEnabled(bool enabled); // 新增：启用/禁用导航功能

signals:
    void navigationClicked(int index);
    void homeClicked(); // 新增：Logo点击跳转到主屏幕信号

private slots:
    void updateDateTime();
    void onLogoClicked(); // 新增：Logo点击处理

private:
    void setupUi();
    void createNavButtons();
    void onButtonClicked(int index);

    // Row 1 components
    QPushButton *logoButton; // 改为QPushButton以支持点击
    QLabel *temperatureLabel;
    QLabel *dateTimeLabel;
    QTimer *timer;

    // Row 2 - Navigation buttons
    static const int s_buttonCount = 6;
    QWidget* m_buttonContainers[s_buttonCount];
    QLabel* m_buttonBackgrounds[s_buttonCount];
    QPushButton* m_buttons[s_buttonCount];
    QString m_iconPathsInactive[s_buttonCount];
    QString m_iconPathsActive[s_buttonCount];
    QString m_buttonTexts[s_buttonCount];
    int m_activeButtonIndex;
    bool m_navigationEnabled; // 新增：导航是否启用
};

#endif // COMMONHEADER_H 
#include "timerwidget.h"
#include <QVBoxLayout>
#include <QPainter>
#include <QPixmap>
#include <QDebug>
#include <QApplication>
#include <QTime>
#include <QFile>
#include <cstdlib>

// Stage background image paths
const char* TimerWidget::STAGE_BACKGROUNDS[] = {
    "",                                          // STAGE_0: No background
    ":/images/fast_mode/next_sample.png",       // STAGE_1: Next Sample
    ":/images/fast_mode/add_80.png",            // STAGE_2: Add 80ul
    ":/images/fast_mode/incubation.png",        // STAGE_3: Incubation
    ":/images/fast_mode/read.png",              // STAGE_4: Read
    ""                                          // STAGE_5: Gray background (using CSS)
};

TimerWidget::TimerWidget(QWidget *parent)
    : QWidget(parent)
    , m_textLabel(nullptr)
    , m_countdownTimer(new QTimer(this))
    , m_readingTimer(new QTimer(this))
    , m_currentState(STAGE_0)
    , m_remainingSeconds(0)
{
    setFixedSize(138, 66);
    setObjectName("TimerWidget"); // Set object name to avoid CSS conflicts
    setupUI();
    
    // Connect Timer signals
    connect(m_countdownTimer, &QTimer::timeout, this, &TimerWidget::onCountdownTimeout);
    connect(m_readingTimer, &QTimer::timeout, this, &TimerWidget::onReadingTimeout);
    
    qDebug() << "TimerWidget: Initialization completed";
}

TimerWidget::~TimerWidget()
{
    stopAllTimers();
}

void TimerWidget::setupUI()
{
    // Create text label, slightly smaller to avoid covering borders
    m_textLabel = new QLabel(this);
    m_textLabel->setGeometry(2, 2, 134, 62); // Leave 2px space for borders
    m_textLabel->setAlignment(Qt::AlignCenter);
    m_textLabel->setWordWrap(true);
    m_textLabel->setStyleSheet(
        "background-color: transparent; "
        "border: none; "
        "color: white; "
        "font-size: 10pt; "
        "font-weight: bold;"
    );
    
    qDebug() << "TimerWidget: setupUI completed";
    
    // Ensure Widget is visible
    show();
    setVisible(true);
    
    // Initial state
    setTimerState(STAGE_0);
}

void TimerWidget::setTimerState(TimerState state)
{
    if (m_currentState == state) return;
    
    TimerState oldState = m_currentState;
    m_currentState = state;
    
    // Stop all timers
    stopAllTimers();
    
    // Update display
    updateDisplay();
    updateBackground();
    updateTextStyle();
    
    // Additional debug: Check Widget basic properties
    qDebug() << "TimerWidget: State changed from" << oldState << "to" << state;
    qDebug() << "TimerWidget: Size" << size() << "Position" << pos() << "Visibility" << isVisible();
    qDebug() << "TimerWidget: Parent" << parent() << "Object name" << objectName();
    
    emit stageChanged(state);
}

void TimerWidget::startStage1Countdown(int seconds)
{
    if (m_currentState != STAGE_1) {
        setTimerState(STAGE_1);
    }
    
    m_remainingSeconds = seconds;
    m_countdownTimer->start(1000); // Update every second
    updateDisplay();
    
    qDebug() << "TimerWidget: Starting Stage 1 countdown" << seconds << "seconds";
}

void TimerWidget::startStage3Incubation(int minutes)
{
    if (m_currentState != STAGE_3) {
        setTimerState(STAGE_3);
    }
    
    m_remainingSeconds = minutes * 60;
    m_incubationStartTime = QDateTime::currentDateTime();
    m_countdownTimer->start(1000); // Update every second
    updateDisplay();
    
    qDebug() << "TimerWidget: Starting Stage 3 incubation countdown" << minutes << "minutes";
}

void TimerWidget::startStage4Reading()
{
    if (m_currentState != STAGE_4) {
        setTimerState(STAGE_4);
    }
    
    m_stage4StartTime = QDateTime::currentDateTime();
    m_remainingSeconds = 0;
    m_countdownTimer->start(1000); // Count every second
    updateDisplay();
    
    qDebug() << "TimerWidget: Starting Stage 4 reading wait";
}

void TimerWidget::stopAllTimers()
{
    m_countdownTimer->stop();
    m_readingTimer->stop();
}

void TimerWidget::updateDisplay()
{
    QString text;
    
    switch (m_currentState) {
        case STAGE_0:
            text = ""; // Empty display, but keep border
            break;
            
        case STAGE_1:
            if (m_remainingSeconds > 0) {
                text = QString("Next Sample\n%1").arg(m_remainingSeconds, 2, 10, QChar('0'));
            } else {
                text = "Next Sample\n00";
            }
            break;
            
        case STAGE_2:
            text = "80ul vol.\nadded";
            break;
            
        case STAGE_3:
            if (m_remainingSeconds > 0) {
                text = QString("Incubation\n-%1").arg(formatTimeMinSec(m_remainingSeconds));
            } else {
                text = "Incubation\n00:00";
            }
            break;
            
        case STAGE_4:
            text = QString("Read\n-%1").arg(m_remainingSeconds, 2, 10, QChar('0'));
            break;
            
        case STAGE_5:
            // Stage 5 displays time difference or default value
            if (m_stage4StartTime.isValid()) {
                QDateTime currentTime = QDateTime::currentDateTime();
                QString timeDiff = calculateTimeDifference(currentTime);
                text = timeDiff;
            } else {
                text = "--:--"; // Default display
            }
            break;
    }
    
    m_textLabel->setText(text);
}

void TimerWidget::updateBackground()
{
    QString styleSheet;
    
    // Use highest priority style settings to ensure overriding parent component styles
    if (m_currentState == STAGE_5) {
        // Stage 5 uses light gray background
        styleSheet = "QWidget { "
                    "border: 1px solid black !important; "
                    "padding: 2px !important; "
                    "background-color: #E0E0E0 !important; "
                    "}";
    } else if (m_currentState != STAGE_0) {
        // Other stages use background images
        const char* imagePath = STAGE_BACKGROUNDS[m_currentState];
        if (imagePath && strlen(imagePath) > 0) {
            // Verify if resource exists
            QFile resourceFile(imagePath);
            if (resourceFile.exists()) {
                // 预加载图片验证是否可用（ARM系统内存优化）
                QPixmap testPixmap(imagePath);
                if (!testPixmap.isNull() && testPixmap.width() > 0 && testPixmap.height() > 0) {
                    styleSheet = QString("QWidget { "
                                       "border: 1px solid black !important; "
                                       "padding: 2px !important; "
                                       "background-image: url(%1) !important; "
                                       "background-repeat: no-repeat !important; "
                                       "background-position: center !important; "
                                       "}").arg(imagePath);
                    qDebug() << "TimerWidget: Successfully loaded background image" << imagePath << "size:" << testPixmap.size();
                } else {
                    qDebug() << "TimerWidget: Image file exists but failed to load as pixmap:" << imagePath;
                    // Use colored background as fallback when image can't be loaded
                    QString fallbackColor = (m_currentState == STAGE_1) ? "#4CAF50" : // Green
                                           (m_currentState == STAGE_2) ? "#2196F3" : // Blue
                                           (m_currentState == STAGE_3) ? "#FF9800" : // Orange
                                           "#F44336"; // Red (STAGE_4)
                    styleSheet = QString("QWidget { "
                                       "border: 1px solid black !important; "
                                       "padding: 2px !important; "
                                       "background-color: %1 !important; "
                                       "}").arg(fallbackColor);
                    qDebug() << "TimerWidget: Using color fallback:" << fallbackColor;
                }
            } else {
                // Use colored background as fallback when resource doesn't exist
                QString fallbackColor = (m_currentState == STAGE_1) ? "#4CAF50" : // Green
                                       (m_currentState == STAGE_2) ? "#2196F3" : // Blue
                                       (m_currentState == STAGE_3) ? "#FF9800" : // Orange
                                       "#F44336"; // Red (STAGE_4)
                styleSheet = QString("QWidget { "
                                   "border: 1px solid black !important; "
                                   "padding: 2px !important; "
                                   "background-color: %1 !important; "
                                   "}").arg(fallbackColor);
                qDebug() << "TimerWidget: Image resource doesn't exist, using color fallback:" << fallbackColor;
            }
        } else {
            // Invalid image path
            styleSheet = "QWidget { "
                        "border: 1px solid black !important; "
                        "padding: 2px !important; "
                        "background-color: #CCCCCC !important; "
                        "}";
            qDebug() << "TimerWidget: Invalid image path for stage" << m_currentState;
        }
    } else {
        // Stage 0 uses white background
        styleSheet = "QWidget { "
                    "border: 1px solid black !important; "
                    "padding: 2px !important; "
                    "background-color: white !important; "
                    "}";
    }
    
    // Apply stylesheet
    setStyleSheet(styleSheet);
    update(); // Force redraw
    qDebug() << "TimerWidget: Applied stylesheet for stage" << m_currentState;
}

void TimerWidget::updateTextStyle()
{
    QString textColor;
    
    switch (m_currentState) {
        case STAGE_0:
            textColor = "#666666"; // Gray text
            break;
        case STAGE_5: {
            // Stage 5 determines color based on time difference
            if (m_stage4StartTime.isValid()) {
                QDateTime currentTime = QDateTime::currentDateTime();
                qint64 diffSeconds = m_stage4StartTime.secsTo(currentTime);
                if (diffSeconds > INCUBATION_ERROR_THRESHOLD) {
                    textColor = "#FF0000"; // Red error text
                } else {
                    textColor = "#666666"; // Gray normal text
                }
            } else {
                textColor = "#666666"; // Gray text
            }
            break;
        }
        case STAGE_1:
        case STAGE_2:
        case STAGE_4:
            textColor = "white"; // White text
            break;
        case STAGE_3:
            textColor = "black"; // Black text
            break;
    }
    
    m_textLabel->setStyleSheet(QString(
        "background-color: transparent; "
        "border: none; "
        "color: %1; "
        "font-size: 10pt; "
        "font-weight: bold; "
        "qproperty-alignment: AlignCenter;"
    ).arg(textColor));
}

void TimerWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() != Qt::LeftButton) {
        QWidget::mousePressEvent(event);
        return;
    }
    
    switch (m_currentState) {
        case STAGE_2:
            // Stage 2 click enters stage 3
            emit stage2Clicked();
            qDebug() << "TimerWidget: Stage 2 clicked, should enter stage 3";
            break;
            
        case STAGE_4: {
            // Stage 4 click starts reading
            QDateTime clickTime = QDateTime::currentDateTime();
            QString timeDiff = calculateTimeDifference(clickTime);
            emit stage4Clicked(timeDiff);
            simulateReading();
            qDebug() << "TimerWidget: Stage 4 clicked, time difference:" << timeDiff;
            break;
        }
        
        default:
            // Other stages don't respond to clicks
            break;
    }
    
    QWidget::mousePressEvent(event);
}

void TimerWidget::onCountdownTimeout()
{
    switch (m_currentState) {
        case STAGE_1:
            if (m_remainingSeconds > 0) {
                m_remainingSeconds--;
                updateDisplay();
                
                if (m_remainingSeconds == 0) {
                    // Stage 1 completed, automatically enter stage 2
                    m_countdownTimer->stop();
                    setTimerState(STAGE_2);
                    qDebug() << "TimerWidget: Stage 1 countdown completed, entering stage 2";
                }
            }
            break;
            
        case STAGE_3:
            if (m_remainingSeconds > 0) {
                m_remainingSeconds--;
                updateDisplay();
                
                if (m_remainingSeconds == 0) {
                    // Stage 3 completed, automatically enter stage 4
                    m_countdownTimer->stop();
                    setTimerState(STAGE_4);
                    startStage4Reading();
                    emit incubationCompleted();
                    qDebug() << "TimerWidget: Stage 3 incubation completed, entering stage 4";
                }
            }
            break;
            
        case STAGE_4:
            // Stage 4 only counts time, doesn't end automatically
            m_remainingSeconds++;
            updateDisplay();
            break;
            
        default:
            break;
    }
}

void TimerWidget::onReadingTimeout()
{
    // Simulate reading completion
    m_readingTimer->stop();
    
    // Generate simulation result (ensure random seed is initialized)
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<uint>(QTime::currentTime().msec()));
        seeded = true;
    }
    double randomValue = rand() / static_cast<double>(RAND_MAX) * 10.0; // 0-10 range
    QString result = QString::number(15.25 + randomValue, 'f', 2);
    
    setTimerState(STAGE_5);
    emit readingCompleted(result);
    
    qDebug() << "TimerWidget: Reading completed, result:" << result;
}

void TimerWidget::simulateReading()
{
    // Start 10-second reading simulation
    m_readingTimer->setSingleShot(true);
    m_readingTimer->start(READING_SIMULATION_TIME * 1000);
    
    qDebug() << "TimerWidget: Starting reading simulation, will complete in" << READING_SIMULATION_TIME << "seconds";
}

QString TimerWidget::calculateTimeDifference(const QDateTime& clickTime) const
{
    if (!m_stage4StartTime.isValid()) {
        return "--:--";
    }
    
    qint64 diffSeconds = m_stage4StartTime.secsTo(clickTime);
    
    if (diffSeconds <= INCUBATION_ERROR_THRESHOLD) {
        // Within allowable range
        return "--:--";
    } else {
        // Out of range, display red timeout
        int minutes = diffSeconds / 60;
        int seconds = diffSeconds % 60;
        return QString("+%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
    }
}

QString TimerWidget::formatTime(int totalSeconds) const
{
    return QString("%1").arg(totalSeconds, 2, 10, QChar('0'));
}

QString TimerWidget::formatTimeMinSec(int totalSeconds) const
{
    int minutes = totalSeconds / 60;
    int seconds = totalSeconds % 60;
    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

bool TimerWidget::isStage1Complete() const
{
    return m_currentState > STAGE_1;
}

bool TimerWidget::isStage3Complete() const
{
    return m_currentState > STAGE_3;
}

bool TimerWidget::isClickable() const
{
    return m_currentState == STAGE_2 || m_currentState == STAGE_4;
}

void TimerWidget::paintEvent(QPaintEvent *event)
{
    QWidget::paintEvent(event);
    
    // If special drawing logic is needed, it can be added here
    // Currently background images are handled through CSS, no manual drawing needed
} 
# AddSample到页面显示数据流实现

## 任务概述
实现完整的数据流：从AddSamplePage添加数据 → 数据库存储 → 通知MainWindow → 更新对应页面显示

## 关键特性

### 1. Patient ID显示格式
- **格式**: "last name, ID"
- **示例**: "Smith, 12345" 或 "Unknown, 9876"
- **自动处理**: 空姓名自动填充"Unknown"，空ID使用resultId

### 2. 数据流架构
```
AddSamplePage → 数据库存储 → onDataInserted() → 发出信号 → MainWindow → 路由到页面 → 添加行显示
```

### 3. 三种模式统一处理
- **AutoSample**: 不带行号
- **StatSample**: 不带行号  
- **FastMode**: 需要行号（自动生成）

## 实现详细

### 1. AddSamplePage修改

#### 新增信号
```cpp
void dataAddedToDatabase(SourcePageType sourceType, const QString& patientName, const QString& patientId, 
                        const QString& parameter, const QString& sampleType, const QString& result,
                        const QString& datetime, const QString& lot, const QString& cutoff);
```

#### onDataInserted()方法增强
- ✅ 保存表单数据
- ✅ 构造Patient ID显示格式："last name, ID"
- ✅ 生成datetime格式：当前时间 + 样本类型缩写
- ✅ 发出dataAddedToDatabase信号
- ✅ 显示成功消息
- ✅ 清空表单

### 2. MainWindow数据路由

#### 信号连接
在`navigateToAddSamplePageWithType()`中连接：
```cpp
connect(addSamplePage, &AddSamplePage::dataAddedToDatabase, this, &MainWindow::onDataAddedToDatabase);
```

#### 数据分发逻辑
```cpp
void MainWindow::onDataAddedToDatabase(SourcePageType sourceType, ...) {
    switch (sourceType) {
        case SourcePageType::AUTO_SAMPLE:
            autoContent->addSampleRow(patientId, parameter, result, datetime, "--:--", lot, cutoff);
            break;
        case SourcePageType::STAT_SAMPLE:
            statContent->addSampleRow(patientId, parameter, result, datetime, "--:--", lot, cutoff);
            break;
        case SourcePageType::FAST_MODE:
            int rowNumber = fastContent->getRowCount() + 1;
            fastContent->addSampleRow(rowNumber, patientId, parameter, result, datetime, "--:--", lot, cutoff);
            break;
    }
}
```

### 3. 数据字段映射

| AddSample字段 | 页面显示列 | 处理逻辑 |
|---|---|---|
| lastName + firstName | Patient | "last name, first name" |
| addId / resultId | Patient ID部分 | addId优先，为空则用resultId |
| parameterType | Parameter | 直接使用 |
| sampleType | DateTime后缀 | 转换为缩写(B/S/P) |
| 当前时间 | DateTime | "yyyy.MM.dd hh:mm, S" |
| 固定值 | Result | "测试中..." |
| 当前日期 | Lot | "yyyyMMdd" |
| 固定值 | Cutoff | "<3.00" |

### 4. 样本类型处理

#### 转换规则
- Blood → B
- Serum → S  
- Plasma → P
- Capillary blood → B

#### 显示格式
DateTime字段包含样本类型：`2024.01.15 14:30, S`

## 文件修改清单

### addsamplepage.h
- 新增`dataAddedToDatabase`信号声明

### addsamplepage.cpp  
- 包含`<QDateTime>`头文件
- 增强`onDataInserted()`方法
- 实现数据收集和信号发出逻辑

### mainwindow.h
- 新增`onDataAddedToDatabase`槽函数声明

### mainwindow.cpp
- 实现数据路由槽函数
- 在`navigateToAddSamplePageWithType`中连接信号

## 用户体验流程

### 正常操作流程
1. 🔄 用户在任意页面点击"Add sample"
2. 📝 进入AddSamplePage填写信息
3. ✅ 点击Insert按钮
4. 💾 数据保存到数据库
5. 📢 发出成功信号
6. 🔄 MainWindow接收并路由数据
7. 📊 对应页面添加新行显示
8. 🧹 表单自动清空
9. ✅ 显示成功消息

### 错误处理
- ❌ 验证失败：显示具体错误信息
- ❌ 数据库错误：显示错误消息，不清空表单
- 🔧 自动回退：保持用户输入数据

## 技术亮点

### 1. 类型安全
- 强类型的SourcePageType枚举
- 结构化的信号参数传递
- 类型检查的页面转换

### 2. 松耦合设计
- AddSample不直接依赖页面类
- MainWindow作为数据路由中心
- 信号槽异步通信

### 3. 用户友好
- 智能默认值填充
- 清晰的状态反馈
- 自动表单管理

## 下一步计划
1. 实现TestResultDao真实数据库保存
2. 从数据库获取真实的结果、批号、截止值
3. 添加页面刷新和数据同步机制
4. 完整功能测试和用户验收 
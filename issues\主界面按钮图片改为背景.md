# 主界面按钮图片改为背景

## 任务背景
将主界面右侧6个按钮的图片从icon属性改为CSS背景图片

## 修改内容

### 1. CSS样式优化
- 添加background-image、background-position、background-size等属性
- 调整padding-top为60px，为图片留出空间
- 图片位置：center 15px（水平居中，距离顶部15px）
- 图片大小：48px × 48px

### 2. 按钮图片映射
- Auto Sample → icon_settings.png
- QC Module → main_qc.png  
- STAT Sample → icon_settings.png
- Database → icon_qc.png
- Fast Mode → icon_logout.png
- Settings → icon_database.png

### 3. UI文件修改
- 移除所有QPushButton的icon和iconSize属性
- 使用CSS选择器为每个按钮设置专门的背景图片
- 保持按钮的objectName以便CSS定位

## 技术实现
- 使用background-image: url(:/images/xxx.png)设置背景
- 通过QPushButton#button_name选择器精确控制每个按钮
- 保持原有的hover和pressed效果
- 文字显示在图片下方

## 预期效果
- 图片作为按钮背景显示在上方
- 文字显示在按钮下方
- 保持原有的交互效果和样式

## 最终实现方案
使用QToolButton替代QPushButton实现文字叠加效果：
- 将所有按钮从QPushButton改为QToolButton
- 设置toolButtonStyle为Qt::ToolButtonTextUnderIcon
- 图标尺寸设置为388x144（按钮实际尺寸）
- 文字颜色设为白色，添加阴影效果提高可读性
- 修改C++代码中的按钮连接和类型转换

## 实现效果
- 图片铺满整个按钮作为背景
- 文字叠加在图片上方，白色字体带阴影
- 按钮尺寸固定，不会因图片而变形
- 保持hover和pressed交互效果
- 解决了QPushButton图标文字并排的问题 
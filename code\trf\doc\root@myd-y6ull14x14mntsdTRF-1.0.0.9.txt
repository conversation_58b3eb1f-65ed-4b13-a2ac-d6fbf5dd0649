root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# ./run.sh 
TRF ARM鍚姩鑴氭湰 - <PERSON><PERSON> Jan 18 05:34:56 UTC 2022
=========================
鍚姩 TRF (ARMv7l)...
"????? TRF ????..."
qt.qpa.input: evdevkeyboard: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x8)
qt.qpa.input: Found matching devices ("/dev/input/event2", "/dev/input/event0")
qt.qpa.input: Adding keyboard at "/dev/input/event2"
qt.qpa.input: Try to create keyboard handler for "/dev/input/event2" ""
qt.qpa.input: Opening keyboard at "/dev/input/event2"
qt.qpa.input: Create keyboard handler with for device "/dev/input/event2"
qt.qpa.input: Unload current keymap and restore built-in
qt.qpa.input: numlock=0 , capslock=0, scrolllock=0
qt.qpa.input: Adding keyboard at "/dev/input/event0"
qt.qpa.input: Try to create keyboard handler for "/dev/input/event0" ""
qt.qpa.input: Opening keyboard at "/dev/input/event0"
qt.qpa.input: Create keyboard handler with for device "/dev/input/event0"
qt.qpa.input: Unload current keymap and restore built-in
qt.qpa.input: numlock=0 , capslock=0, scrolllock=0
qt.qpa.input: evdevmouse: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x1|0x2)
qt.qpa.input: Found matching devices ()
qt.qpa.input: evdevtouch: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x2|0x4)
qt.qpa.input: Found matching devices ("/dev/input/event1")
qt.qpa.input: evdevtouch: Adding device at "/dev/input/event1"
qt.qpa.input: evdevtouch: Using device /dev/input/event1
qt.qpa.input: evdevtouch: /dev/input/event1: Protocol type A  (single)
qt.qpa.input: evdevtouch: /dev/input/event1: min X: 0 max X: 4095
qt.qpa.input: evdevtouch: /dev/input/event1: min Y: 0 max Y: 4095
qt.qpa.input: evdevtouch: /dev/input/event1: min pressure: 0 max pressure: 0
qt.qpa.input: evdevtouch: /dev/input/event1: device name: iMX6UL TouchScreen Controller
No such plugin for spec  "tslib:/dev/input/event0"
"QApplication ????"
"=== ?????? ==="
"Qt ??: 5.12.8"
"Qt ?????: 5.6.2"
"??????: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l"
"???????: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l/trf"
"Qt ??????:"
"  - /usr/lib/qt5/plugins (??: ?)"
"  - /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l (??: ?)"
"???SQL??:"
"platforms??: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l/platforms (??: ?)"
"?????:"
"  - Windows"
"  - Fusion"
"=== ?????? ==="
"??????: ??"
"????????: global_colors.qss"
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(PaletteChange, 0x7ee449ec)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(AcceptDropsChange, 0x7ee4471c)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(CursorChange, 0x7ee449f4)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(Polish, 0x7ee44934)
qt.qpa.input: evdevtouch: Updating QInputDeviceManager device count: 1  touch devices, 0 pending handler(s)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(PolishRequest, 0x54d87960)
----
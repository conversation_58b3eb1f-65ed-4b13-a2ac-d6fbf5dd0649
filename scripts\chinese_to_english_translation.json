{"debug_messages": {"Qt版本兼容性配置:": "Qt Version Compatibility Config:", "编译版本:": "Build Version:", "运行版本:": "Runtime Version:", "UTF-8编码已设置": "UTF-8 encoding configured", "Windows系统优化已启用": "Windows system optimization enabled", "ARM系统优化已启用": "ARM system optimization enabled", "检测到目标系统myd-y6ull14x14，启用专用配置...": "Detected target system myd-y6ull14x14, enabling dedicated configuration...", "使用触摸设备:": "Using touch device:", "myd-y6ull14x14专用配置已启用": "myd-y6ull14x14 dedicated configuration enabled", "LinuxFB图形输出已设置": "LinuxFB graphics output configured", "触摸输入已配置": "Touch input configured", "EGL/OpenGL ES支持已启用": "EGL/OpenGL ES support enabled", "目标系统:": "Target system:", "精确匹配模式已激活": "Precise matching mode activated", "开始智能字体路径检测...": "Starting smart font path detection...", "发现字体目录:": "Found font directory:", "字体目录不存在:": "Font directory not found:", "设置字体环境变量:": "Setting font environment variables:", "检查插件路径:": "Checking plugin path:", "插件路径存在:": "Plugin path exists:", "插件路径不存在:": "Plugin path not found:", "可用样式:": "Available styles:", "开始初始化 TRF 应用程序...": "Starting TRF application initialization...", "QApplication 创建成功": "QApplication created successfully", "开始初始化 HumaFIA 系统数据库...": "Starting HumaFIA system database initialization...", "数据库初始化失败:": "Database initialization failed:", "数据库初始化成功": "Database initialization successful", "加载全局样式表...": "Loading global stylesheet...", "全局样式表加载成功": "Global stylesheet loaded successfully", "全局样式表加载失败": "Global stylesheet loading failed", "创建登录界面...": "Creating login screen...", "创建主窗口...": "Creating main window...", "显示登录界面": "Showing login screen", "登录成功，显示主窗口": "Login successful, showing main window", "程序正常退出": "Program exited normally"}, "log_messages": {"=== 检查资源文件 ===": "=== Checking Resource Files ===", "全局样式文件:": "Global style file:", "存在": "exists", "缺失": "missing", "关键资源文件缺失:": "Critical resource file missing:", "图片资源": "Image resource", "发现": "Found", "个图片资源缺失": "image resources missing", "=== 记录系统信息 ===": "=== Recording System Information ===", "应用程序版本:": "Application version:", "Qt构建版本:": "Qt build version:", "Qt运行版本:": "Qt runtime version:", "工作目录:": "Working directory:", "可执行文件路径:": "Executable path:", "库信息路径:": "Library info path:", "当前样式:": "Current style:", "=== 检查插件路径 ===": "=== Checking Plugin Paths ===", "=== 字体路径检测 ===": "=== Font Path Detection ===", "=== 系统信息记录完成 ===": "=== System Information Recording Complete ==="}, "error_messages": {"资源文件错误": "Resource File Error", "关键资源文件缺失，程序无法正常运行。": "Critical resource files missing, program cannot run normally.", "数据库错误": "Database Error", "数据库初始化失败:": "Database initialization failed:", "详细错误信息请查看日志文件:": "For detailed error information, please check log file:"}, "ui_text": {"QC 模块": "QC Module", "临时数据清理工具": "Temporary Data Cleanup Tool", "清空用户数据": "Clear User Data", "确认清空": "Confirm Clear", "您确定要清空所有用户数据吗？此操作不可撤销。": "Are you sure you want to clear all user data? This operation cannot be undone.", "清空完成": "Clear Complete", "用户数据已成功清空。": "User data has been successfully cleared.", "清空失败": "Clear Failed", "清空用户数据时发生错误:": "Error occurred while clearing user data:"}, "comments": {"Qt版本兼容性检查和配置": "Qt version compatibility check and configuration", "设置UTF-8编码支持（兼容性处理）": "Set UTF-8 encoding support (compatibility handling)", "只设置Locale编码，避免使用已废弃的函数": "Only set Locale encoding, avoid using deprecated functions", "注意：setCodecForTr和setCodecForCStrings在Qt 5.15+中已废弃，故不使用": "Note: setCodecForTr and setCodecForCStrings are deprecated in Qt 5.15+, so not used", "Qt 6.0+ 默认使用UTF-8，无需额外设置": "Qt 6.0+ uses UTF-8 by default, no additional setup needed", "Windows平台优化设置": "Windows platform optimization settings", "Qt 5.x版本的高DPI设置": "Qt 5.x version high DPI settings", "Qt 5.10+才有此属性，Qt 6.0+中已被移除": "This attribute is only available in Qt 5.10+, removed in Qt 6.0+", "ARM嵌入式系统优化的应用程序属性": "ARM embedded system optimized application properties", "ARM系统通常不需要高DPI": "ARM systems typically don't need high DPI", "目标系统myd-y6ull14x14专用优化配置": "Target system myd-y6ull14x14 dedicated optimization configuration", "针对Qt 5.6.2的特定兼容性设置": "Specific compatibility settings for Qt 5.6.2", "针对iMX6UL TouchScreen Controller的触摸输入优化": "Touch input optimization for iMX6UL TouchScreen Controller", "系统支持libEGL.so.1和libGLESv2.so.2": "System supports libEGL.so.1 and libGLESv2.so.2", "设置区域环境变量以支持UTF-8": "Set locale environment variables to support UTF-8", "禁用一些ARM系统上可能有问题的特性": "Disable some features that may be problematic on ARM systems", "输出目标系统匹配信息": "Output target system matching information", "智能字体路径检测和配置": "Smart font path detection and configuration", "ARM嵌入式系统常见的字体目录列表（按优先级排序）": "Common font directories for ARM embedded systems (sorted by priority)", "现代Linux TrueType字体目录": "Modern Linux TrueType font directory", "标准Linux字体目录": "Standard Linux font directory", "用户安装字体目录": "User installed font directory", "DejaVu字体包": "DejaVu font package", "Liberation字体包": "Liberation font package", "Android风格字体目录": "Android style font directory", "Qt5特定字体目录": "Qt5 specific font directory", "备用字体目录": "Backup font directory", "应用程序目录下字体": "Fonts in application directory", "可执行文件目录字体": "Fonts in executable directory", "设置字体环境变量": "Set font environment variables", "全局错误日志文件": "Global error log file", "确保日志文件使用UTF-8编码": "Ensure log file uses UTF-8 encoding", "Qt 6.0+ 默认使用UTF-8，无需设置": "Qt 6.0+ uses UTF-8 by default, no setup needed", "写入启动标记": "Write startup marker", "在应用程序创建前设置Qt兼容性": "Set Qt compatibility before application creation", "设置错误日志": "Set error logging", "创建应用程序实例": "Create application instance", "配置插件路径和字体": "Configure plugin paths and fonts", "记录系统信息": "Record system information", "检查资源文件": "Check resource files"}}
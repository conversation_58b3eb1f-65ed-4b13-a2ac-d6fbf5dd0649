#ifndef ADDSAMPLEPAGE_H
#define ADDSAMPLEPAGE_H

#include <QWidget>
#include <QPushButton>
#include <QEvent>
#include <QDate>

namespace Ui {
class AddSamplePage;
}

enum class SourcePageType {
    AUTO_SAMPLE,
    STAT_SAMPLE,
    FAST_MODE
};

class DatePickerDialog; // 前向声明
class DilutionFactorDialog; // 前向声明
class TestResultController; // 前向声明
struct AddSampleFormData; // 前向声明
class IncubationStateManager; // 前向声明

class AddSamplePage : public QWidget
{
    Q_OBJECT

public:
    explicit AddSamplePage(QWidget *parent = nullptr, SourcePageType sourceType = SourcePageType::AUTO_SAMPLE);
    ~AddSamplePage();

signals:
    void exitRequested();
    void dataAddedToDatabase(SourcePageType sourceType, const QString& patientName, const QString& patientId, 
                            const QString& parameter, const QString& sampleType, const QString& result,
                            const QString& datetime, const QString& lot, const QString& cutoff);

protected:
    bool eventFilter(QObject *watched, QEvent *event) override; // 添加事件过滤器

private slots:
    void onYesClicked();
    void onNoClicked();
    void onSampleButtonClicked();
    void onParameterButtonClicked();
    void onNumberButtonClicked();
    void onExitButtonClicked();
    void onBirthdayClicked(); // Handle birthday click event
    void onCancelClicked(); // Clear Add ID input box
    void onDeleteClicked(); // Delete last character from Add ID input box
    
    // Database operation feedback slots
    void onDataInserted(int resultId, SourcePageType sourceType);
    void onInsertFailed(const QString& error, SourcePageType sourceType);
    
    // Test completion monitoring
    void onTestCompleted();
    void checkTestCompletion();

private:
    void setupConnections();
    void setupButtonsForSourceType();
    void setDarkBackgroundForButtons();
    void onInsertButtonClicked();
    
    // 收集表单数据的辅助方法
    AddSampleFormData collectFormData();
    
    // 样本类型转换方法
    QString convertSampleTypeToAbbreviation(const QString& fullName);
    
    Ui::AddSamplePage *ui;
    SourcePageType m_sourcePageType;
    TestResultController *m_testResultController; // 数据库操作控制器
    bool m_waitingForTestCompletion;
};

#endif // ADDSAMPLEPAGE_H 
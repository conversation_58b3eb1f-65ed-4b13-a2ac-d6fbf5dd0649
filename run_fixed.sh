#!/bin/bash

# TRF SQLite Built-in Support Startup Script
# For myd-y6ull14x14 system with built-in SQLite fix

echo "TRF SQLite Built-in Support Startup Script"
echo "==========================================="
echo "Date: $(date)"
echo "System: $(uname -a)"
echo ""

# Application directory
APP_DIR="/mnt/TRF-********-Linux-ARM-v7l-SystemTxt-Adapted/TRF-********-Linux-ARM-v7l-SystemTxt-Adapted"

if [ ! -d "$APP_DIR" ]; then
    echo "ERROR: Application directory not found: $APP_DIR"
    echo "Please confirm TRF application is correctly deployed"
    exit 1
fi

cd "$APP_DIR"

if [ ! -f "./trf" ]; then
    echo "ERROR: trf executable not found"
    exit 1
fi

chmod +x ./trf

echo "Current directory: $(pwd)"
echo ""

# System SQLite support check
echo "=== System SQLite Support Check ==="
if ldconfig -p | grep -q "libQt5Sql.so.5"; then
    echo "Qt5 SQL Library: Available"
    QT_SQL_PATH=$(ldconfig -p | grep "libQt5Sql.so.5" | awk '{print $NF}' | head -1)
    echo "   Path: $QT_SQL_PATH"
else
    echo "Qt5 SQL Library: Not found"
fi

# Check Qt version
QT_VERSION=$(strings /usr/lib/libQt5Core.so.5 2>/dev/null | grep "^5\." | head -1 || echo "Unknown")
echo "Qt Version: $QT_VERSION"
echo ""

# Runtime environment configuration - optimized for myd-y6ull14x14
echo "=== Runtime Environment Configuration ==="

# Basic system configuration
export LC_ALL=C
export LANG=C

# Qt platform configuration - LinuxFB for myd-y6ull14x14
export QT_QPA_PLATFORM="linuxfb"
export QT_QPA_FB_DISABLE_INPUT="0"

# Touch device configuration
if [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
    echo "Touch Device: /dev/input/event1"
elif [ -c /dev/input/event0 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event0"
    echo "Touch Device: /dev/input/event0"
else
    echo "Touch Device: Not found"
fi

# EGL configuration (if supported)
export QT_QPA_EGLFS_INTEGRATION="eglfs_viv"
export QT_QPA_EGLFS_DISABLE_INPUT="0"

# Important: Do not set plugin paths, let Qt use built-in SQLite
# Clear any external plugin paths to force built-in support
unset QT_PLUGIN_PATH
unset QT_SQL_DRIVERS

echo "Environment configured (Built-in SQLite mode)"
echo ""

# Set permissions
chmod 666 /dev/input/event* 2>/dev/null || true

# Log file
LOG_FILE="trf_builtin_sqlite.log"
echo "TRF Built-in SQLite startup log - $(date)" > $LOG_FILE
echo "System: $(uname -a)" >> $LOG_FILE
echo "Qt Version: $QT_VERSION" >> $LOG_FILE
echo "Mode: Built-in SQLite support" >> $LOG_FILE
echo "" >> $LOG_FILE

echo "Starting TRF (Built-in SQLite support version)..."
echo "Expected: SQLite driver will use Qt built-in support, no external plugins needed"
echo ""

# Start application with detailed logging
echo "Start time: $(date)" >> $LOG_FILE
./trf "$@" 2>&1 | tee -a $LOG_FILE

RESULT=$?
echo "" >> $LOG_FILE
echo "Exit code: $RESULT" >> $LOG_FILE
echo "Exit time: $(date)" >> $LOG_FILE

if [ $RESULT -ne 0 ]; then
    echo ""
    echo "Program exited abnormally (code: $RESULT)"
    echo "Details in log file: $LOG_FILE"
    echo ""
    echo "Troubleshooting:"
    echo "1. Check log file: cat $LOG_FILE"
    echo "2. Check system libraries: ldconfig -p | grep Qt5"
    echo "3. Verify SQLite support: ldd ./trf | grep -i sql"
else
    echo ""
    echo "Program exited normally"
    echo "If database functions work correctly, built-in SQLite fix successful!"
fi

exit $RESULT
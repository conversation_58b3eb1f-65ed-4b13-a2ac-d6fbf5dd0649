#include "fastmodepage.h"
#include "ui_fastmodepage.h"
#include <QWheelEvent>
#include <QScrollBar>
#include <QApplication>
#include <QTimer>
#include <QDebug>
#include <QScroller>
#include <QElapsedTimer>
#include <QFontMetrics>
#include <algorithm>
#include <QShowEvent>  // 添加QShowEvent的include
#include <cstdlib>     // 添加rand/srand支持
#include <QFile>       // 添加QFile支持
#include <QPixmap>     // 添加QPixmap支持
#include <QPalette>    // 添加QPalette支持

// 添加数据存储相关includes
#include "database/storage/FileStorage.h"
#include "database/entities/TestResult.h"
#include "database/entities/Patient.h"

// 列配置常量定义
const FastModePage::ColumnConfig FastModePage::COLUMN_NUMBER = {10, 50};
const FastModePage::ColumnConfig FastModePage::COLUMN_PATIENT = {60, 140};
const FastModePage::ColumnConfig FastModePage::COLUMN_PARAMETER = {200, 152};
const FastModePage::ColumnConfig FastModePage::COLUMN_RESULT = {352, 114};
const FastModePage::ColumnConfig FastModePage::COLUMN_DATETIME = {466, 190};
const FastModePage::ColumnConfig FastModePage::COLUMN_LOT = {656, 114};
const FastModePage::ColumnConfig FastModePage::COLUMN_CUTOFF = {770, 114};
const FastModePage::ColumnConfig FastModePage::COLUMN_TIMER = {884, 138};

FastModePage::FastModePage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::FastModePage),
    m_dataLoaded(false),
    m_fixedResultRow(nullptr),
    m_hasLatestResult(false),
    m_backgroundMask(nullptr)
{
    ui->setupUi(this);

    // Connect the add sample button to the new signal
    connect(ui->button_add_sample, &QPushButton::clicked, this, &FastModePage::addSampleClicked);
    
    // 初始化全局定时器
    m_globalTimer = new QTimer(this);
    connect(m_globalTimer, &QTimer::timeout, this, &FastModePage::onGlobalTimerTick);
    m_globalTimer->start(1000); // 每秒触发一次
    
    // Setup the scrollable table
    setupScrollableTable();
    enableMouseScrolling();
    enableTouchScrolling();
    
    // 不再预生成测试数据，等待AddSample添加
    // loadHundredRows(); // 已移除
    
    // 首先创建固定的结果显示面板
    createFixedResultRow();

    // 创建背景遮罩
    createBackgroundMask();

    // 初始化空显示状态
    initializeEmptyDisplay();

    // For debugging - 注释掉自动滚动测试，它会干扰初始显示
    // QTimer::singleShot(1000, this, &FastModePage::testScrolling);
}

FastModePage::~FastModePage()
{
    // Clean up dynamic rows
    clearAllRows();

    // Clean up fixed result row
    if (m_fixedResultRow) {
        destroyRow(m_fixedResultRow);
        m_fixedResultRow = nullptr;
    }

    // Clean up background mask
    if (m_backgroundMask) {
        delete m_backgroundMask;
        m_backgroundMask = nullptr;
    }

    delete ui;
}

void FastModePage::setupScrollableTable()
{
    // Configure scroll area properties
    ui->scrollArea->setWidgetResizable(false);
    ui->scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    ui->scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    // Enable mouse tracking and focus
    ui->scrollArea->setMouseTracking(true);
    ui->scrollArea->setFocusPolicy(Qt::WheelFocus);
    
    // Configure smooth scrolling
    ui->scrollArea->verticalScrollBar()->setSingleStep(33); // 每次滚动半行高度
    ui->scrollArea->verticalScrollBar()->setPageStep(66);   // 每页滚动一行高度
    
    // 移除硬编码的内容大小设置，让updateScrollableContent完全控制
    // ui->scrollAreaWidgetContents->setFixedSize(1012, 338); // 已移除
    
    // Connect scroll bar value changed signal for virtual scrolling
    connect(ui->scrollArea->verticalScrollBar(), &QScrollBar::valueChanged,
            this, &FastModePage::updateVisibleRows);
    
    // Force update to ensure proper scrollbar range
    ui->scrollArea->updateGeometry();
    
    // Test: temporarily show scrollbar to verify it's working
    // ui->scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn);
}

void FastModePage::enableMouseScrolling()
{
    // Install event filter to handle mouse wheel events
    ui->scrollArea->installEventFilter(this);
    ui->scrollAreaWidgetContents->installEventFilter(this);
    this->installEventFilter(this);
    
    // Set wheel focus to enable wheel events
    ui->scrollArea->setFocusPolicy(Qt::WheelFocus);
    this->setFocusPolicy(Qt::WheelFocus);
}

bool FastModePage::eventFilter(QObject *obj, QEvent *event)
{
    // 处理点击事件
    if (event->type() == QEvent::MouseButtonPress) {
        QLabel* label = qobject_cast<QLabel*>(obj);
        if (label && label->property("rowNumber").isValid()) {
            int rowNumber = label->property("rowNumber").toInt();

            // 检查是否是Timer列点击
            if (label->property("columnType").toString() == "timer") {
                handleTimerClick(rowNumber);
                return true;
            }
            // 检查是否是数字列点击
            else if (label->property("columnType").toString() == "number") {
                deleteSampleRow(rowNumber);
                return true;
            }
        }
    }
    
    if (event->type() == QEvent::Wheel) {
        QWheelEvent *wheelEvent = static_cast<QWheelEvent*>(event);
        
        // Handle wheel scrolling
        if (obj == ui->scrollArea || obj == ui->scrollAreaWidgetContents || obj == this) {
            QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
            
            // Calculate scroll amount - use direct pixel delta if available
            int pixelDelta = wheelEvent->pixelDelta().y();
            int angleDelta = wheelEvent->angleDelta().y();
            
            int scrollAmount = 0;
            if (!pixelDelta) {
                // Use angle delta if pixel delta not available
                scrollAmount = -angleDelta / 8; // Convert from 1/8 degree to pixels
            } else {
                scrollAmount = -pixelDelta;
            }
            
            // Apply scrolling with bounds checking
            int currentValue = scrollBar->value();
            int newValue = currentValue + scrollAmount;
            newValue = qMax(scrollBar->minimum(), qMin(scrollBar->maximum(), newValue));
            
            scrollBar->setValue(newValue);
            
            // Virtual scrolling will be triggered by valueChanged signal
            
            return true; // Event handled
        }
    }
    
    return QWidget::eventFilter(obj, event);
}

void FastModePage::addSampleRow(int rowNumber, const QString &patient, const QString &parameter, 
                                 const QString &result, const QString &datetime,
                                 const QString &timer, const QString &lot, const QString &cutoff)
{
    SampleRowData rowData(rowNumber, patient, parameter, result, datetime, timer, lot, cutoff);
    addSampleRow(rowData);
}

void FastModePage::addSampleRow(const SampleRowData &rowData)
{
    // 添加或更新行数据
    m_rowData[rowData.rowNumber] = rowData;
    
    // 如果UI行已存在，更新它；否则创建新的
    if (m_dynamicRows.contains(rowData.rowNumber)) {
        updateRowUI(m_dynamicRows[rowData.rowNumber], rowData);
    } else {
        DynamicRowUI *rowUI = createDataRow(rowData);
        if (rowUI) {
            m_dynamicRows[rowData.rowNumber] = rowUI;
        }
    }
    
    // 更新滚动内容
    updateScrollableContent();
}

void FastModePage::addNewSample(const QString &patient, const QString &parameter,
                                const QString &result, const QString &datetime,
                                const QString &timer, const QString &lot, const QString &cutoff)
{
    // 新数据应该添加到最上面，使用新的方法
    addNewSampleToTop(patient, parameter, result, datetime, timer, lot, cutoff);
}

void FastModePage::addNewSampleToTop(const QString &patient, const QString &parameter,
                                    const QString &result, const QString &datetime,
                                    const QString &timer, const QString &lot, const QString &cutoff)
{
    // 获取新的行号，确保新数据显示在最上面
    int newRowNumber = getNextRowNumber();
    
    // 创建样本数据
    SampleRowData rowData(newRowNumber, patient, parameter, result, datetime, timer, lot, cutoff);
    
    // 添加或更新行
    addSampleRow(rowData);
    
    // 重新排列所有行的位置（按时间倒序）
    repositionAllRows();
    
    // 确保至少显示5行
    ensureMinimumRows();
    
    // 启动新添加行的Timer流程
    qDebug() << "FastMode: Starting timer flow for new row" << newRowNumber;
    startTestForRow(newRowNumber);
    
    // 验证Timer状态
    if (m_timerStates.contains(newRowNumber)) {
        qDebug() << "FastMode: Row" << newRowNumber << "Timer state set to stage:" << m_timerStates[newRowNumber].currentStage;
    }
    
    qDebug() << "FastMode: Added new sample to top, row number" << newRowNumber << ", patient:" << patient << ", timer flow started";
}

int FastModePage::getNextRowNumber()
{
    // 获取当前最大的行号，新行号 = 最大行号 + 1
    // 这样确保新数据有更大的行号，在排序时会显示在最上面
    int maxRowNumber = 0;
    for (auto it = m_rowData.constBegin(); it != m_rowData.constEnd(); ++it) {
        maxRowNumber = qMax(maxRowNumber, it.key());
    }
    return maxRowNumber + 1;
}

void FastModePage::removeRow(int rowNumber)
{
    // 移除数据
    if (m_rowData.contains(rowNumber)) {
        m_rowData.remove(rowNumber);
    }
    
    // 销毁UI
    if (m_dynamicRows.contains(rowNumber)) {
        destroyRow(m_dynamicRows[rowNumber]);
        m_dynamicRows.remove(rowNumber);
    }
    
    // 重新排列剩余行的位置
    repositionAllRows();
    updateScrollableContent();
}

void FastModePage::clearAllRows()
{
    // 清空所有动态行
    for (auto it = m_dynamicRows.begin(); it != m_dynamicRows.end(); ++it) {
        destroyRow(it.value());
    }
    m_dynamicRows.clear();
    m_rowData.clear();
    m_visibleRows.clear();
    
    updateScrollableContent();
}

DynamicRowUI* FastModePage::createDataRow(const SampleRowData &rowData)
{
    DynamicRowUI *rowUI = new DynamicRowUI();
    
    // 创建行容器widget，宽度设为1021以匹配原有的widget_r1设计
    rowUI->widget = new QWidget(ui->scrollAreaWidgetContents);
    rowUI->widget->setObjectName(QString("widget_r%1").arg(rowData.rowNumber));
    rowUI->widget->resize(1021, ROW_HEIGHT); // 使用1021宽度匹配原有设计
    
    // 获取显示位置用于序号显示
    int displayIndex = getRowDisplayIndex(rowData.rowNumber);
    
    // 创建各列的标签 - 序号列显示连续序号，空行也显示序号
    QString numberText = QString::number(displayIndex + 1);
    rowUI->numberLabel = createColumnLabel(rowUI->widget, COLUMN_NUMBER, numberText);

    // 为数字列添加属性标识和事件过滤器
    rowUI->numberLabel->setProperty("rowNumber", rowData.rowNumber);
    rowUI->numberLabel->setProperty("columnType", "number");
    rowUI->numberLabel->installEventFilter(this);
    rowUI->patientLabel = createColumnLabel(rowUI->widget, COLUMN_PATIENT, rowData.patient);
    rowUI->paramLabel = createColumnLabel(rowUI->widget, COLUMN_PARAMETER, rowData.parameter);
    rowUI->resultLabel = createColumnLabel(rowUI->widget, COLUMN_RESULT, rowData.result);
    rowUI->datetimeLabel = createColumnLabel(rowUI->widget, COLUMN_DATETIME, rowData.datetime);
    rowUI->lotLabel = createColumnLabel(rowUI->widget, COLUMN_LOT, rowData.lot);
    rowUI->cutoffLabel = createColumnLabel(rowUI->widget, COLUMN_CUTOFF, rowData.cutoff);
    
    // 创建Timer列背景QLabel（显示图片）
    rowUI->timerBgLabel = new QLabel(rowUI->widget);
    rowUI->timerBgLabel->setGeometry(COLUMN_TIMER.x, 0, COLUMN_TIMER.width, ROW_HEIGHT);
    rowUI->timerBgLabel->setScaledContents(true);
    
    // 创建Timer列文字QLabel（显示文字，背景透明）
    rowUI->timerLabel = new QLabel(rowUI->widget);
    rowUI->timerLabel->setGeometry(COLUMN_TIMER.x, 0, COLUMN_TIMER.width, ROW_HEIGHT);
    rowUI->timerLabel->setText(rowData.timer);
    rowUI->timerLabel->setAlignment(Qt::AlignCenter);
    rowUI->timerLabel->setWordWrap(true);
    rowUI->timerLabel->setStyleSheet("background-color: transparent; border: none;");

    // 为Timer列添加属性标识和事件过滤器
    rowUI->timerLabel->setProperty("rowNumber", rowData.rowNumber);
    rowUI->timerLabel->setProperty("columnType", "timer");
    rowUI->timerLabel->installEventFilter(this);
    
    // 初始化Timer状态数据
    bool isEmptyRow = rowData.patient.isEmpty() && rowData.parameter.isEmpty() && 
                     rowData.result.isEmpty() && rowData.datetime.isEmpty();
    
    TimerStateData& timerState = m_timerStates[rowData.rowNumber];
    if (isEmptyRow) {
        timerState.currentStage = TimerStateData::STAGE_0;
    } else {
        timerState.currentStage = TimerStateData::STAGE_5;
    }
    
    updateTimerUI(rowData.rowNumber);
    
    // 为Timer列添加点击事件（通过eventFilter实现）
    if (rowUI->timerLabel) {
        rowUI->timerLabel->installEventFilter(this);
        rowUI->timerLabel->setProperty("rowNumber", rowData.rowNumber);
    }
    
    // 应用样式
    applyRowStyles(rowUI);
    
    // 设置位置
    positionRow(rowUI, displayIndex);
    
    // 显示行
    rowUI->widget->show();
    
    return rowUI;
}

QLabel* FastModePage::createColumnLabel(QWidget *parent, const ColumnConfig &config, const QString &text)
{
    QLabel *label = new QLabel(parent);
    label->setGeometry(config.x, 0, config.width, ROW_HEIGHT);
    label->setText(text);
    
    // 启用自动换行和文本优化
    label->setWordWrap(true);
    label->setAlignment(Qt::AlignCenter);
    label->setTextFormat(Qt::PlainText);
    label->setTextInteractionFlags(Qt::TextSelectableByMouse);
    
    // 设置省略号策略（当文本过长时）
    QFontMetrics metrics(label->font());
    QString elidedText = metrics.elidedText(text, Qt::ElideRight, config.width - 4);
    if (elidedText != text) {
        label->setToolTip(text); // 完整文本作为提示
    }
    
    return label;
}

void FastModePage::positionRow(DynamicRowUI *rowUI, int rowIndex)
{
    if (!rowUI || !rowUI->widget) return;
    
    int yPos = rowIndex * ROW_HEIGHT;
    // 应用-12像素的x偏移以实现精确对齐
    rowUI->widget->move(-12, yPos);
}

void FastModePage::applyRowStyles(DynamicRowUI *rowUI)
{
    if (!rowUI) return;
    
    // 应用数字列样式（深灰背景）
    if (rowUI->numberLabel) {
        rowUI->numberLabel->setStyleSheet(
            "border: 1px solid black; "
            "padding: 4px; "
            "background-color: #A9A9A9; "
            "color: white; "
            "font-weight: bold; "
            "font-size: 11pt; "
            "qproperty-alignment: AlignCenter;"
        );
    }
    
    // 应用患者和参数列样式（支持换行）
    QString patientParamStyle = 
        "border: 1px solid black; "
        "padding: 2px; "
        "background-color: white; "
        "color: #333333; "
        "font-size: 10pt; "
        "qproperty-alignment: AlignCenter; "
        "qproperty-wordWrap: true;";
        
    if (rowUI->patientLabel) {
        rowUI->patientLabel->setStyleSheet(patientParamStyle);
        rowUI->patientLabel->setWordWrap(true);
    }
    
    if (rowUI->paramLabel) {
        rowUI->paramLabel->setStyleSheet(patientParamStyle);
        rowUI->paramLabel->setWordWrap(true);
    }
    
    // 应用结果列样式（红色字体）
    if (rowUI->resultLabel) {
        rowUI->resultLabel->setStyleSheet(
            "border: 1px solid black; "
            "padding: 4px; "
            "background-color: white; "
            "color: #D32F2F; "
            "font-weight: bold; "
            "font-size: 11pt; "
            "qproperty-alignment: AlignCenter;"
        );
    }
    
    // 应用其他列的通用样式（支持自动换行）
    QString generalStyle = 
        "border: 1px solid black; "
        "padding: 2px; "
        "background-color: white; "
        "color: #333333; "
        "font-size: 10pt; "
        "qproperty-alignment: AlignCenter; "
        "qproperty-wordWrap: true;";
    
    if (rowUI->datetimeLabel) {
        rowUI->datetimeLabel->setStyleSheet(generalStyle);
        rowUI->datetimeLabel->setWordWrap(true);
    }
    if (rowUI->lotLabel) {
        rowUI->lotLabel->setStyleSheet(generalStyle);
        rowUI->lotLabel->setWordWrap(true);
    }
    if (rowUI->cutoffLabel) {
        rowUI->cutoffLabel->setStyleSheet(generalStyle);
        rowUI->cutoffLabel->setWordWrap(true);
    }
    
    // Timer列背景应用基础样式（边框等）
    if (rowUI->timerBgLabel) {
        rowUI->timerBgLabel->setStyleSheet(generalStyle);
    }
    
    // Timer列文字标签保持透明背景
    if (rowUI->timerLabel) {
        rowUI->timerLabel->setStyleSheet("background-color: transparent; border: none; "
                                        "color: #333333; font-size: 10pt; "
                                        "qproperty-alignment: AlignCenter; "
                                        "qproperty-wordWrap: true;");
        rowUI->timerLabel->setWordWrap(true);
    }
    
    // 结果列也启用自动换行
    if (rowUI->resultLabel) {
        rowUI->resultLabel->setWordWrap(true);
    }
}

void FastModePage::destroyRow(DynamicRowUI *rowUI)
{
    if (!rowUI) return;
    
    // 删除所有标签
    delete rowUI->numberLabel;
    delete rowUI->patientLabel;
    delete rowUI->paramLabel;
    delete rowUI->resultLabel;
    delete rowUI->datetimeLabel;
    delete rowUI->lotLabel;
    delete rowUI->cutoffLabel;
    delete rowUI->timerBgLabel;
    delete rowUI->timerLabel;
    
    // 删除容器widget
    delete rowUI->widget;
    
    // 删除结构体
    delete rowUI;
}

void FastModePage::updateRowUI(DynamicRowUI *rowUI, const SampleRowData &rowData)
{
    if (!rowUI) return;
    
    // 获取显示位置用于序号显示
    int displayIndex = getRowDisplayIndex(rowData.rowNumber);
    
    // 更新各列内容 - 序号列显示连续序号，空行也显示序号
    if (rowUI->numberLabel) rowUI->numberLabel->setText(QString::number(displayIndex + 1));
    if (rowUI->patientLabel) rowUI->patientLabel->setText(rowData.patient);
    if (rowUI->paramLabel) rowUI->paramLabel->setText(rowData.parameter);
    if (rowUI->resultLabel) rowUI->resultLabel->setText(rowData.result);
    if (rowUI->datetimeLabel) rowUI->datetimeLabel->setText(rowData.datetime);
    if (rowUI->lotLabel) rowUI->lotLabel->setText(rowData.lot);
    if (rowUI->cutoffLabel) rowUI->cutoffLabel->setText(rowData.cutoff);
    if (rowUI->timerLabel) rowUI->timerLabel->setText(rowData.timer);
    
    // 更新Timer状态（如果存在）
    if (m_timerStates.contains(rowData.rowNumber)) {
        updateTimerUI(rowData.rowNumber);
    }
}

int FastModePage::getRowDisplayIndex(int rowNumber)
{
    // 获取行的显示索引（按行号倒序排序，最大行号显示在最上面）
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());
    
    return sortedRowNumbers.indexOf(rowNumber);
}

void FastModePage::repositionAllRows()
{
    // 重新排列所有行的位置 - 使用与updateVisibleRows相同的排序逻辑
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const SampleRowData& rowDataA = m_rowData[a];
        const SampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    for (int i = 0; i < sortedRowNumbers.size(); ++i) {
        int rowNumber = sortedRowNumbers[i];
        if (m_dynamicRows.contains(rowNumber)) {
            // 重新设置位置
            positionRow(m_dynamicRows[rowNumber], i);

            // 更新序号显示为连续序号
            if (m_dynamicRows[rowNumber]->numberLabel) {
                m_dynamicRows[rowNumber]->numberLabel->setText(QString::number(i + 1));
            }
        }
    }
}

const SampleRowData* FastModePage::getRowData(int rowNumber) const
{
    auto it = m_rowData.find(rowNumber);
    return (it != m_rowData.end()) ? &it.value() : nullptr;
}

void FastModePage::initializeWithTestData()
{
    // 添加5行测试数据作为默认显示
    QStringList patientNames = {
        "Musterman, Otti, 22202301030006",
        "Muster, Otto, 225", 
        "Smith, John, 22202301030007",
        "Brown, Lisa, 22202301030008",
        "Johnson, Mary, 22202301030009"
    };
    
    QStringList parameters = {"hs-CRP [mg/L]", "CRP [mg/L]", "PCT [ng/mL]", "IL-6 [pg/mL]", "TNF-α [pg/mL]"};
    QStringList results = {">10.00", "70.33", "5.45", "15.20", "8.75"};
    QStringList lots = {"20220501", "20220502", "20220503", "20220504", "20220505"};
    QStringList cutoffs = {"<3.00", "<3.00", "<2.50", "<10.00", "<1.00"};
    
    // 添加5行测试数据作为默认显示
    for (int i = 1; i <= 5; ++i) {
        QString patient = patientNames[i - 1];
        QString parameter = parameters[i - 1];
        QString result = results[i - 1];
        QString datetime = QString("2023.01.0%1 17:2%2, S").arg(i + 2).arg(i);
        QString lot = lots[i - 1];
        QString cutoff = cutoffs[i - 1];
        
        addSampleRow(i, patient, parameter, result, datetime, "--:--", lot, cutoff);
    }
    
    qDebug() << "Initialized with" << getRowCount() << "default test data rows";
}

// 添加虚拟滚动相关方法
void FastModePage::updateVisibleRows()
{
    if (!ui->scrollArea) return;
    
    // 获取滚动区域的可见范围
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    int scrollTop = scrollBar->value();
    int viewportHeight = ui->scrollArea->viewport()->height();
    
    // 获取排序后的行号列表 - 关键修复：确保有数据的行排在前面，空行排在后面
    QList<int> sortedRowNumbers = m_rowData.keys();
    
    // 自定义排序：先按是否有数据排序，再按行号倒序排序
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), [this](int a, int b) {
        const SampleRowData& rowDataA = m_rowData[a];
        const SampleRowData& rowDataB = m_rowData[b];
        
        // 检查行是否为空
        bool isEmptyA = rowDataA.patient.isEmpty() && rowDataA.parameter.isEmpty() && 
                       rowDataA.result.isEmpty() && rowDataA.datetime.isEmpty();
        bool isEmptyB = rowDataB.patient.isEmpty() && rowDataB.parameter.isEmpty() && 
                       rowDataB.result.isEmpty() && rowDataB.datetime.isEmpty();
        
        // 有数据的行排在前面
        if (isEmptyA != isEmptyB) {
            return !isEmptyA; // 非空行排在前面
        }
        
        // 如果都是有数据或都是空行，按行号倒序排序（大行号在前）
        return a > b;
    });
    
    QVector<int> newVisibleRows;

    if (scrollTop == 0) {
        // 特殊处理：滚动位置为0时，显示前5行
        for (int i = 0; i < qMin(5, sortedRowNumbers.size()); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    } else {
        // 正常的虚拟滚动逻辑
        int firstVisibleRow = qMax(0, (scrollTop / ROW_HEIGHT) - 1);
        int visibleRowCount = qMin((viewportHeight / ROW_HEIGHT) + 3, 6);
        int lastVisibleRow = qMin(sortedRowNumbers.size() - 1, firstVisibleRow + visibleRowCount - 1);

        for (int i = firstVisibleRow; i <= lastVisibleRow && i < sortedRowNumbers.size(); ++i) {
            newVisibleRows.append(sortedRowNumbers[i]);
        }
    }
    
    // 隐藏不再可见的行
    for (int rowNumber : m_visibleRows) {
        if (!newVisibleRows.contains(rowNumber)) {
            if (m_dynamicRows.contains(rowNumber)) {
                m_dynamicRows[rowNumber]->widget->hide();
            }
        }
    }

    // 显示新可见的行
    for (int rowNumber : newVisibleRows) {
        if (m_dynamicRows.contains(rowNumber)) {
            if (!m_dynamicRows[rowNumber]->widget->isVisible()) {
                m_dynamicRows[rowNumber]->widget->show();
                // 确保Timer标签也显示
                if (m_dynamicRows[rowNumber]->timerBgLabel) {
                    m_dynamicRows[rowNumber]->timerBgLabel->show();
                }
                if (m_dynamicRows[rowNumber]->timerLabel) {
                    m_dynamicRows[rowNumber]->timerLabel->show();
                }
            }
        }
    }
    
    m_visibleRows = newVisibleRows;
    
    qDebug() << "Scroll position:" << scrollTop << "Visible rows:" << m_visibleRows.size() 
             << "out of" << m_rowData.size() << "total";
}

void FastModePage::updateScrollableContent()
{
    // 计算内容总高度：只考虑前4行的滚动区域（第5行为固定结果行）
    int totalRows = m_rowData.size();

    // 确保至少4行的内容高度，第5行为固定结果行不参与滚动
    int minRows = 4;
    int effectiveRows = qMax(minRows, totalRows);
    // 内容高度只包含前4行，第5行固定在底部
    int contentHeight = effectiveRows * ROW_HEIGHT;
    
    // 更新滚动区域内容大小
    ui->scrollAreaWidgetContents->setMinimumHeight(contentHeight);
    ui->scrollAreaWidgetContents->setMaximumHeight(contentHeight);
    ui->scrollAreaWidgetContents->resize(CONTENT_WIDTH, contentHeight);
    
    // 强制更新滚动条范围
    ui->scrollArea->updateGeometry();

    // 更新固定结果行位置
    updateFixedResultRowPosition();

    // 确保滚动位置不超出新的边界
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    if (scrollBar->value() > scrollBar->maximum()) {
        scrollBar->setValue(scrollBar->maximum());
    }
    
    qDebug() << "FastMode: Updated scroll content: totalRows=" << totalRows 
             << "effectiveRows=" << effectiveRows << "contentHeight=" << contentHeight 
             << "scrollRange=" << scrollBar->minimum() << "to" << scrollBar->maximum();
}

void FastModePage::testScrolling()
{
    QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
    qDebug() << "ScrollBar range:" << scrollBar->minimum() << "to" << scrollBar->maximum();
    qDebug() << "ScrollBar current value:" << scrollBar->value();
    qDebug() << "ScrollArea size:" << ui->scrollArea->size();
    qDebug() << "Content size:" << ui->scrollAreaWidgetContents->size();
    
    // Test programmatic scrolling
    if (scrollBar->maximum() > 0) {
        scrollBar->setValue(scrollBar->maximum() / 2);
        qDebug() << "Scrolled to middle position:" << scrollBar->value();
    } else {
        qDebug() << "No scrolling available - content fits in view";
    }
}

void FastModePage::enableTouchScrolling()
{
    // Enable kinetic scrolling for touch devices
    QScroller::grabGesture(ui->scrollArea, QScroller::LeftMouseButtonGesture);
    
    // Configure scrolling properties for better touch experience
    QScrollerProperties properties = QScroller::scroller(ui->scrollArea)->scrollerProperties();
    
    // 彻底禁用弹性滚动，解决"往下拉会拉过头"问题
    properties.setScrollMetric(QScrollerProperties::DragVelocitySmoothingFactor, 0.02);
    properties.setScrollMetric(QScrollerProperties::MinimumVelocity, 0.0);
    properties.setScrollMetric(QScrollerProperties::MaximumVelocity, 0.5);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickMaximumTime, 0.4);
    properties.setScrollMetric(QScrollerProperties::AcceleratingFlickSpeedupFactor, 1.2);
    properties.setScrollMetric(QScrollerProperties::SnapPositionRatio, 0.2);
    properties.setScrollMetric(QScrollerProperties::MaximumClickThroughVelocity, 0);
    properties.setScrollMetric(QScrollerProperties::DragStartDistance, 0.001);
    properties.setScrollMetric(QScrollerProperties::MousePressEventDelay, 0.1);
    
    // 关键修复：恢复垂直弹性滚动，但禁用水平
    properties.setScrollMetric(QScrollerProperties::HorizontalOvershootPolicy, QScrollerProperties::OvershootAlwaysOff);
    properties.setScrollMetric(QScrollerProperties::VerticalOvershootPolicy, QScrollerProperties::OvershootWhenScrollable);
    
    // 弹性参数调优 - 控制弹性范围，避免拉过头
    properties.setScrollMetric(QScrollerProperties::OvershootDragResistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootDragDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollDistanceFactor, 0.33);
    properties.setScrollMetric(QScrollerProperties::OvershootScrollTime, 0.4);
    
    // 设置减速参数
    properties.setScrollMetric(QScrollerProperties::DecelerationFactor, 0.85);
    properties.setScrollMetric(QScrollerProperties::FrameRate, QScrollerProperties::Fps30);
    
    // Apply the properties
    QScroller::scroller(ui->scrollArea)->setScrollerProperties(properties);
    
    // 关键修复：添加状态监听，防止拉过头后数据显示不全
    connect(QScroller::scroller(ui->scrollArea), &QScroller::stateChanged, 
            this, [this](QScroller::State newState) {
        if (newState == QScroller::Inactive) {
            // 滚动结束时检查并修正边界
            QScrollBar *scrollBar = ui->scrollArea->verticalScrollBar();
            int currentValue = scrollBar->value();
            int minValue = scrollBar->minimum();
            int maxValue = scrollBar->maximum();
            
            // 如果超出边界，强制回到边界内
            if (currentValue < minValue) {
                scrollBar->setValue(minValue);
                qDebug() << "FastMode: Corrected upper boundary, from" << currentValue << "to" << minValue;
            } else if (currentValue > maxValue) {
                scrollBar->setValue(maxValue);
                qDebug() << "FastMode: Corrected lower boundary, from" << currentValue << "to" << maxValue;
            }
        }
    });
    
    // 移除边界检查逻辑，让QScroller自己处理边界
    // 不再需要自定义边界检查，因为已经禁用了弹性效果
    qDebug() << "FastMode: Touch scrolling enabled with vertical elastic bounce and boundary protection";
} 

void FastModePage::loadHundredRows()
{
    // 清空现有数据
    clearAllRows();
    
    // 扩展测试数据集
    QStringList patientNames = {
        "Musterman, Otti, 22202301030006", "Muster, Otto, 225", "Smith, John, 22202301030007",
        "Brown, Lisa, 22202301030008", "Johnson, Mary, 22202301030009", "Wilson, David, 22202301030010",
        "Garcia, Ana, 22202301030011", "Miller, Robert, 22202301030012", "Davis, Sarah, 22202301030013",
        "Thompson, Mike, 22202301030014", "Anderson, Kate, 22202301030015", "Taylor, James, 22202301030016",
        "Moore, Emma, 22202301030017", "Jackson, Alex, 22202301030018", "Martin, Lucy, 22202301030019",
        "Lee, Kevin, 22202301030020", "White, Jennifer, 22202301030021", "Harris, Michael, 22202301030022",
        "Clark, Susan, 22202301030023", "Lewis, Paul, 22202301030024", "Robinson, Nicole, 22202301030025",
        "Walker, Daniel, 22202301030026", "Hall, Amanda, 22202301030027", "Allen, Christopher, 22202301030028",
        "Young, Melissa, 22202301030029", "King, Joshua, 22202301030030"
    };
    
    QStringList parameters = {
        "hs-CRP [mg/L]", "CRP [mg/L]", "PCT [ng/mL]", "IL-6 [pg/mL]", 
        "TNF-α [pg/mL]", "ESR [mm/h]", "WBC [×10³/μL]", "Hb [g/dL]"
    };
    
    QStringList results = {
        ">10.00", "70.33", "5.45", "15.20", "8.75", "25.60", "12.30", "45.20", 
        "3.15", "18.90", "2.80", "35.50", "6.25", "42.10", "9.85", "21.30",
        "14.75", "38.60", "7.20", "29.40"
    };
    
    QStringList lots = {
        "20220501", "20220502", "20220503", "20220504", "20220505",
        "20220506", "20220507", "20220508", "20220509", "20220510"
    };
    
    QStringList cutoffs = {"<3.00", "<5.00", "<2.50", "<10.00", "<1.00"};
    
    qDebug() << "Starting to load 100 test data...";
    QElapsedTimer timer;
    timer.start();
    
    // 添加100行测试数据
    for (int i = 1; i <= 100; ++i) {
        QString patient = patientNames[i % patientNames.size()];
        QString parameter = parameters[i % parameters.size()];
        QString result = results[i % results.size()];
        QString datetime = QString("2023.%1.%2 %3:%4, S")
                          .arg((i % 12) + 1, 2, 10, QChar('0'))
                          .arg((i % 28) + 1, 2, 10, QChar('0'))
                          .arg((i % 24), 2, 10, QChar('0'))
                          .arg((i % 60), 2, 10, QChar('0'));
        QString lot = lots[i % lots.size()];
        QString cutoff = cutoffs[i % cutoffs.size()];
        
        addSampleRow(i, patient, parameter, result, datetime, "--:--", lot, cutoff);
        
        // 每10行输出一次进度
        if (i % 10 == 0) {
            qDebug() << "Loaded" << i << "rows, time:" << timer.elapsed() << "ms";
        }
    }
    
    int totalTime = timer.elapsed();
    qDebug() << "Completed loading 100 data, total time:" << totalTime << "ms, average per row:" << (totalTime / 100.0) << "ms";
    qDebug() << "Actual UI rows in memory:" << m_dynamicRows.size();
    qDebug() << "Current visible rows:" << m_visibleRows.size();
    
    // 触发虚拟滚动更新
    updateVisibleRows();
} 

void FastModePage::initializeEmptyDisplay()
{
    // Set scroll position to 0 (top)
    ui->scrollArea->verticalScrollBar()->setValue(0);

    // Clear all data
    clearAllRows();

    // Ensure at least 4 rows are displayed (all empty rows)
    ensureMinimumRows();

    // 确保固定结果面板显示
    if (m_fixedResultRow) {
        ensureFixedResultRowVisible();
    }

    qDebug() << "FastMode: Initialize at least 4 rows display, all empty rows, and fixed result row"
             << "Fixed row visible:" << (m_fixedResultRow && m_fixedResultRow->widget && m_fixedResultRow->widget->isVisible());
}

void FastModePage::ensureMinimumRows()
{
    const int MIN_ROWS = 4;  // 最少显示4行，第5行为固定结果面板
    
    // Count rows with data
    int dataRows = 0;
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());
    
    for (int rowNumber : sortedRowNumbers) {
        const SampleRowData& rowData = m_rowData[rowNumber];
        if (!rowData.patient.isEmpty() || !rowData.parameter.isEmpty() || 
            !rowData.result.isEmpty() || !rowData.datetime.isEmpty()) {
            dataRows++;
        }
    }
    
    // If data rows are less than minimum, add empty rows
    if (dataRows < MIN_ROWS) {
        int emptyRowsNeeded = MIN_ROWS - dataRows;
        
        // Find the minimum row number for adding empty rows (ensure empty rows are displayed below)
        int minRowNumber = 1;
        if (!m_rowData.isEmpty()) {
            QList<int> allRowNumbers = m_rowData.keys();
            minRowNumber = *std::min_element(allRowNumbers.begin(), allRowNumbers.end());
            if (minRowNumber > 1) {
                minRowNumber = 1; // Start adding empty rows from 1
            }
        }
        
        // Add empty rows using smaller row numbers
        for (int i = 0; i < emptyRowsNeeded; ++i) {
            int emptyRowNumber = minRowNumber - i - 1;
            if (emptyRowNumber <= 0) {
                emptyRowNumber = -(i + 1); // Use negative row numbers to ensure they are placed at the bottom
            }
            
            if (!m_rowData.contains(emptyRowNumber)) {
                SampleRowData emptyRowData(emptyRowNumber, "", "", "", "", "", "", "");
                addSampleRow(emptyRowData);
            }
        }
    }
    
    // Update scroll content
    updateScrollableContent();
    
    // Key fix: ensure visible rows are updated so empty rows are displayed correctly
    updateVisibleRows();
    
    qDebug() << "FastMode: Ensure minimum" << MIN_ROWS << "rows, data rows:" << dataRows 
             << ", total rows:" << m_rowData.size();
}

int FastModePage::findFirstEmptyRowOrGetNext()
{
    // Iterate through existing rows to find the first empty row
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end());
    
    for (int rowNumber : sortedRowNumbers) {
        const SampleRowData& rowData = m_rowData[rowNumber];
        if (rowData.patient.isEmpty() && rowData.parameter.isEmpty() && 
            rowData.result.isEmpty() && rowData.datetime.isEmpty()) {
            // Found an empty row
            qDebug() << "FastMode: Found empty row" << rowNumber << "for replacement";
            return rowNumber;
        }
    }
    
    // No empty row found, return the next row number
    int nextRowNumber = m_rowData.size() + 1;
    qDebug() << "FastMode: No empty row found, using new row number" << nextRowNumber;
    return nextRowNumber;
}

void FastModePage::loadInitialDataFromDatabase()
{
    if (m_dataLoaded) {
        qDebug() << "FastMode: Data already loaded, skipping duplicate load";
        return;
    }
    
    qDebug() << "FastMode: Starting to load initial data from database...";
    
    // Use FileStorage system
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        qDebug() << "FastMode: Failed to initialize file storage:" << storage->getLastError();
        m_dataLoaded = true;
        ensureMinimumRows();
        return;
    }
    
    // Query the latest 20 test results for FastMode (sorted by time descending)
    QList<TestResult> testResults = storage->getTestResultsByMode("FAST_MODE");
    
    // Sort by test datetime descending and limit to 20
    std::sort(testResults.begin(), testResults.end(), [](const TestResult& a, const TestResult& b) {
        return a.getTestDateTime() > b.getTestDateTime();
    });
    if (testResults.size() > 20) {
        testResults = testResults.mid(0, 20);
    }
    
    if (testResults.isEmpty()) {
        qDebug() << "FastMode: No FastMode data in the database";
        m_dataLoaded = true;
        ensureMinimumRows(); // Ensure at least 5 empty rows are displayed
        return;
    }
    
    qDebug() << "FastMode: Loaded" << testResults.size() << "test results from database";
    
    // Clear existing data
    clearAllRows();
    
    // Convert and add data (testResults are already sorted by time descending)
    // Assign row numbers starting from 1 to ensure positive row numbers
    int rowNumber = 1;
    for (const TestResult& testResult : testResults) {
        // Get patient information
        Patient patient = storage->getPatientById(testResult.getPatientId());
        
        // Convert to row data
        SampleRowData rowData;
        convertTestResultToRowData(testResult, patient, rowData);
        rowData.rowNumber = rowNumber++;
        
        // Add to the page
        addSampleRow(rowData);
    }
    
    // Ensure at least 4 rows are displayed
    ensureMinimumRows();

    // 强制确保第五行存在并显示
    ensureFixedResultRowVisible();

    // Reposition rows
    repositionAllRows();

    // Update scroll content
    updateScrollableContent();

    m_dataLoaded = true;
    qDebug() << "FastMode: Initial data loading completed with fixed result row";
}

void FastModePage::convertTestResultToRowData(const TestResult& testResult, const Patient& patient, SampleRowData& rowData)
{
    // 设置数据库记录ID，用于删除操作
    rowData.resultId = testResult.getResultId();
    
    // Format patient information: LastName, FirstName, ID
    QString patientInfo;
    if (patient.isValid()) {
        QString lastName = patient.getLastName().isEmpty() ? "" : patient.getLastName();
        QString firstName = patient.getFirstName().isEmpty() ? "" : patient.getFirstName();
        patientInfo = QString("%1,%2,%3")
                     .arg(lastName)
                     .arg(firstName)
                     .arg(testResult.getPatientId());
    } else {
        patientInfo = QString("Unknown,Patient,%1").arg(testResult.getPatientId());
    }
    
    // Format date and time: YYYY.MM.DD HH:MM, Sample Type
    QString datetime = testResult.getTestDateTime().toString("yyyy.MM.dd hh:mm") + 
                      ", " + testResult.getSampleType();
    
    // Set row data
    rowData.patient = patientInfo;
    rowData.parameter = testResult.getParameterType();
    rowData.result = testResult.getTestResult();
    rowData.datetime = datetime;
    rowData.timer = "--:--"; // Fixed display
    rowData.lot = testResult.getLotNumber();
    rowData.cutoff = testResult.getCutoffValue();
}

void FastModePage::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // Load data when the page is first displayed
    if (!m_dataLoaded) {
        qDebug() << "FastMode: Page first displayed, triggering data load";
        loadInitialDataFromDatabase();
    }
}

void FastModePage::clearFastModeData()
{
    qDebug() << "FastMode: Manually clearing FastMode data";
    
    // Stop all Timers
    if (m_globalTimer) {
        m_globalTimer->stop();
    }
    
    // Clear Timer states
    m_timerStates.clear();
    
    // Reset data loaded status
    m_dataLoaded = false;
    
    // Clear all UI and data
    clearAllRows();
    
    // Initialize empty display state (display 4 empty rows + fixed result row)
    initializeEmptyDisplay();

    // 强制确保第五行存在并显示
    ensureFixedResultRowVisible();

    // Restart Timer
    if (m_globalTimer) {
        m_globalTimer->start(1000); // 1 second interval
    }

    qDebug() << "FastMode: Data cleared, reset to empty state with fixed result row";
}

void FastModePage::refreshAfterDataClear()
{
    qDebug() << "FastMode: Received data clear signal, starting page refresh";
    clearFastModeData();
    qDebug() << "FastMode: Page refresh completed";
}

// Timer related methods implementation
void FastModePage::startTestForRow(int rowNumber)
{
    // Check if row data exists
    if (!m_rowData.contains(rowNumber)) {
        qDebug() << "FastMode: Row" << rowNumber << "data does not exist, cannot start test";
        return;
    }

    DynamicRowUI* rowUI = m_dynamicRows.value(rowNumber);
    if (!rowUI || !rowUI->timerLabel) {
        qDebug() << "FastMode: Row" << rowNumber << "UI components do not exist, cannot start test";
        return;
    }

    TimerStateData& timerState = m_timerStates[rowNumber];

    // 获取行数据中的项目类型和样本类型
    const SampleRowData& rowData = m_rowData[rowNumber];
    QString projectType = rowData.parameter;  // 参数类型作为项目类型
    QString sampleType = rowData.datetime;    // 从datetime字段提取样本类型（格式：日期, 样本类型）

    // 提取样本类型（从datetime字段的最后部分）
    if (sampleType.contains(",")) {
        QStringList parts = sampleType.split(",");
        if (parts.size() > 1) {
            sampleType = parts.last().trimmed();
        }
    }

    // 保存项目和样本类型到Timer状态
    timerState.projectType = projectType;
    timerState.sampleType = sampleType;

    // 检查是否是CRP项目
    bool isCRPProject = shouldSkipStages01ForProject(projectType, sampleType);

    // Check if it can start immediately (no pre-incubation stage 1-2 tests)
    bool canStart = !hasTestsInPreIncubationStages();

    if (isCRPProject) {
        // CRP项目：需要判断当前状态
        bool hasCRPIncubating = hasCRPInIncubation();

        if (hasCRPIncubating) {
            // 已有CRP在温育，进入WAITING状态
            timerState.currentStage = TimerStateData::STAGE_WAITING;
            timerState.remainingSeconds = 0;
            timerState.isClickable = false;
            qDebug() << "FastMode: Row" << rowNumber << "CRP project, but another CRP is incubating, entering WAITING";
        } else {
            // 没有CRP在温育，直接进入阶段2
            timerState.currentStage = TimerStateData::STAGE_2;
            timerState.remainingSeconds = 0;
            timerState.isClickable = true;  // 阶段2可点击
            qDebug() << "FastMode: Row" << rowNumber << "CRP project, no CRP incubating, entering stage 2";
        }
    } else if (canStart) {
        // 其他项目：正常进入阶段1
        timerState.currentStage = TimerStateData::STAGE_1;
        timerState.remainingSeconds = 30; // 30 second countdown
        timerState.isClickable = false;
        qDebug() << "FastMode: Row" << rowNumber << "non-CRP project, starting test immediately, entering stage 1";
    } else {
        // Enter waiting queue (force override previous stage 5)
        timerState.currentStage = TimerStateData::STAGE_0;
        timerState.remainingSeconds = 0;
        timerState.isClickable = false;
        qDebug() << "FastMode: Row" << rowNumber << "entering waiting queue, stage 0";
    }

    updateTimerUI(rowNumber);
}

// New Timer management methods implementation

void FastModePage::updateTimerUI(int rowNumber)
{
    DynamicRowUI* rowUI = m_dynamicRows.value(rowNumber);
    if (!rowUI || !rowUI->timerLabel || !rowUI->timerBgLabel) {
        qDebug() << "FastMode: updateTimerUI - Row" << rowNumber << "UI components missing, m_dynamicRows contains:" << m_dynamicRows.contains(rowNumber) << 
                 ", m_rowData contains:" << m_rowData.contains(rowNumber);
        return;
    }
    
    if (!m_timerStates.contains(rowNumber)) {
        qDebug() << "FastMode: updateTimerUI - Row" << rowNumber << "missing state data";
        return;
    }
    
    qDebug() << "FastMode: updateTimerUI - Row" << rowNumber << "starting update, current stage:" << m_timerStates[rowNumber].currentStage;
    
    const TimerStateData& timerState = m_timerStates[rowNumber];
    QString text;
    QString textStyle;
    QString bgStyle;
    QString imagePath;
    
    // Resource check (only used for development)
    static bool diagnosed = false;
    if (!diagnosed) {
        diagnosed = true;
        qDebug() << "FastMode: Timer column resource check:";
        qDebug() << "  next_sample.png:" << QFile(":/images/fast_mode/next_sample.png").exists();
        qDebug() << "  add_80.png:" << QFile(":/images/fast_mode/add_80.png").exists();
        qDebug() << "  incubation.png:" << QFile(":/images/fast_mode/incubation.png").exists();
        qDebug() << "  read.png:" << QFile(":/images/fast_mode/read.png").exists();
    }
    
    // Set text, style, and background image based on stage
    switch (timerState.currentStage) {
        case TimerStateData::STAGE_0:
            text = "";
            bgStyle = "QLabel { "
                     "border: 1px solid black; "
                     "padding: 2px; "
                     "background-color: white; "
                     "}";
            textStyle = "QLabel { "
                       "background-color: transparent; "
                       "border: none; "
                       "color: #666666; "
                       "font-size: 10pt; "
                       "font-weight: bold; "
                       "text-align: center; "
                       "}";
            imagePath = ""; // No background image
            break;
            
                case TimerStateData::STAGE_1:
            text = QString("Next Sample\n%1").arg(timerState.remainingSeconds, 2, 10, QChar('0'));
            bgStyle = "QLabel { "
                     "border: 1px solid black; "
                     "padding: 2px; "
                     "}";
            textStyle = "QLabel { "
                       "background-color: transparent; "
                       "border: none; "
                       "color: white; "
                       "font-size: 10pt; "
                       "font-weight: bold; "
                       "text-align: center; "
                       "}";
            imagePath = ":/images/fast_mode/next_sample.png";
            qDebug() << "FastMode: Row" << rowNumber << "stage 1 display -" << text << "background:" << imagePath;
            break;
            
        case TimerStateData::STAGE_2:
            text = "80ul vol.\nadded";
            bgStyle = "QLabel { "
                     "border: 1px solid black; "
                     "padding: 2px; "
                     "}";
            textStyle = "QLabel { "
                       "background-color: transparent; "
                       "border: none; "
                       "color: white; "
                       "font-size: 10pt; "
                       "font-weight: bold; "
                       "text-align: center; "
                       "}";
            imagePath = ":/images/fast_mode/add_80.png";
            break;
            
        case TimerStateData::STAGE_3:
            {
                int minutes = timerState.remainingSeconds / 60;
                int seconds = timerState.remainingSeconds % 60;
                text = QString("Incubation\n-%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
                bgStyle = "QLabel { "
                         "border: 1px solid black; "
                         "padding: 2px; "
                         "}";
                textStyle = "QLabel { "
                           "background-color: transparent; "
                           "border: none; "
                           "color: black; "
                           "font-size: 10pt; "
                           "font-weight: bold; "
                           "text-align: center; "
                           "}";
                imagePath = ":/images/fast_mode/incubation.png";
            }
            break;
            
        case TimerStateData::STAGE_4:
            {
                // CRP项目显示倒计时，其他项目显示正计时
                bool isCRPProject = timerState.projectType.contains("CRP", Qt::CaseInsensitive);

                if (isCRPProject) {
                    // CRP项目：显示倒计时
                    text = QString("Read\n-%1").arg(timerState.remainingSeconds, 2, 10, QChar('0'));
                } else {
                    // 其他项目：显示正计时（经过时间）
                    text = QString("Read\n%1").arg(timerState.remainingSeconds, 2, 10, QChar('0'));
                }

                bgStyle = "QLabel { "
                         "border: 1px solid black; "
                         "padding: 2px; "
                         "}";
                textStyle = "QLabel { "
                           "background-color: transparent; "
                           "border: none; "
                           "color: white; "
                           "font-size: 10pt; "
                           "font-weight: bold; "
                           "text-align: center; "
                           "}";
                imagePath = ":/images/fast_mode/read.png";
            }
            break;

        case TimerStateData::STAGE_WAITING:
            text = "Waiting";
            bgStyle = "QLabel { "
                     "border: 1px solid black; "
                     "padding: 2px; "
                     "}";
            textStyle = "QLabel { "
                       "background-color: transparent; "
                       "border: none; "
                       "color: white; "
                       "font-size: 10pt; "
                       "font-weight: bold; "
                       "text-align: center; "
                       "}";
            imagePath = ":/images/fast_mode/next_sample.png"; // 使用和阶段1相同的背景图
            break;

        case TimerStateData::STAGE_5:
        default:
            {
                // Display result based on time difference
                const int THRESHOLD_SECONDS = 60; // 1 minute threshold
                qint64 diffSeconds = timerState.remainingSeconds; // Time difference saved during click handling
                
                QString textColor;
                if (diffSeconds <= THRESHOLD_SECONDS) {
                    text = "--:--";
                    textColor = "#666666"; // Gray
                } else {
                    // More than threshold, display red +mm:ss format
                    int minutes = diffSeconds / 60;
                    int seconds = diffSeconds % 60;
                    text = QString("+%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
                    textColor = "#FF0000"; // Red
                }
                
                bgStyle = "QLabel { "
                         "border: 1px solid black; "
                         "padding: 2px; "
                         "background-color: #E0E0E0; "
                         "}";
                textStyle = QString("QLabel { "
                                   "background-color: transparent; "
                                   "border: none; "
                                   "color: %1; "
                                   "font-size: 10pt; "
                                   "font-weight: bold; "
                                   "text-align: center; "
                                   "}").arg(textColor);
                imagePath = ""; // No background image, use gray background
            }
            break;
    }
    
    // Set background label style and image
    rowUI->timerBgLabel->setStyleSheet(bgStyle);
    if (!imagePath.isEmpty() && QFile::exists(imagePath)) {
        QPixmap pixmap(imagePath);
        if (!pixmap.isNull()) {
            // Scale image to fit label size, maintaining aspect ratio
            QPixmap scaledPixmap = pixmap.scaled(rowUI->timerBgLabel->size(), 
                                               Qt::KeepAspectRatioByExpanding, 
                                               Qt::SmoothTransformation);
            rowUI->timerBgLabel->setPixmap(scaledPixmap);
            // qDebug() << "FastMode: Successfully set background image" << imagePath << "size:" << scaledPixmap.size();
        } else {
            qDebug() << "FastMode: Could not load image" << imagePath;
            rowUI->timerBgLabel->clear(); // Clear background image
        }
    } else {
        rowUI->timerBgLabel->clear(); // Clear background image (Stage 0 and Stage 5)
    }
    
    // Set text label
    rowUI->timerLabel->setText(text);
    rowUI->timerLabel->setStyleSheet(textStyle);
    rowUI->timerLabel->setAlignment(Qt::AlignCenter);
    rowUI->timerLabel->setWordWrap(true);
}

void FastModePage::advanceTimerStage(int rowNumber)
{
    // TODO: Implement Timer stage advancement logic
    qDebug() << "FastMode: advanceTimerStage" << rowNumber << "to be implemented";
}

void FastModePage::handleTimerClick(int rowNumber)
{
    if (!m_timerStates.contains(rowNumber)) {
        return;
    }
    
    TimerStateData& timerState = m_timerStates[rowNumber];
    
    switch (timerState.currentStage) {
        case TimerStateData::STAGE_2:
            // Stage 2 click → Enter stage 3 incubation
            timerState.currentStage = TimerStateData::STAGE_3;
            // 根据项目类型设置温育时间
            timerState.remainingSeconds = getIncubationTimeForProject(timerState.projectType);
            timerState.incubationStartTime = QDateTime::currentDateTime();
            timerState.isClickable = false;

            updateTimerUI(rowNumber);

            // 当有样本进入温育阶段时，其他阶段2的样本变为Waiting状态
            convertStage2ToWaiting(rowNumber);

            // Immediately start the next waiting test (when the current test has entered incubation stage)
            startNextWaitingTest();

            qDebug() << "FastMode: Row" << rowNumber << "entering stage 3 incubation ("
                     << timerState.remainingSeconds << "seconds), next waiting test started";
            break;
            
        case TimerStateData::STAGE_4:
            // Stage 4 click → Start reading (only for non-CRP projects)
            {
                // 检查是否是CRP项目
                bool isCRPProject = timerState.projectType.contains("CRP", Qt::CaseInsensitive);

                if (isCRPProject) {
                    // CRP项目在阶段4不需要手动点击，已经自动处理
                    qDebug() << "FastMode: Row" << rowNumber << "CRP project stage 4 - automatic processing, no manual click needed";
                    return;
                }

                QDateTime clickTime = QDateTime::currentDateTime();
                qint64 diffSeconds = timerState.stage4StartTime.secsTo(clickTime);

                timerState.isClickable = false;

                qDebug() << "FastMode: Row" << rowNumber << "non-CRP project starting reading, time difference:" << diffSeconds << "seconds";

                // Simulate a 10-second reading process
                QTimer::singleShot(10000, this, [this, rowNumber, diffSeconds]() {
                    if (!m_timerStates.contains(rowNumber)) return;

                    TimerStateData& timerState = m_timerStates[rowNumber];
                    timerState.currentStage = TimerStateData::STAGE_5;
                    timerState.remainingSeconds = static_cast<int>(diffSeconds); // Save time difference
                    timerState.isClickable = false;

                    // Generate simulated result and update Result column
                    generateAndUpdateResult(rowNumber);

                    updateTimerUI(rowNumber);

                    qDebug() << "FastMode: Row" << rowNumber << "non-CRP reading completed, time difference:" << diffSeconds << "seconds";
                });
            }
            break;
            
        default:
            // qDebug() << "FastMode: Row" << rowNumber << "stage" << timerState.currentStage << "not clickable";
            break;
    }
}

// Timer auxiliary methods implementation
bool FastModePage::hasTestsInPreIncubationStages() const
{
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        
        // Only check for actual existing valid rows (excluding negative row numbers)
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue;
        }
        
        TimerStateData::Stage stage = it.value().currentStage;
        if (stage == TimerStateData::STAGE_1 || stage == TimerStateData::STAGE_2 || stage == TimerStateData::STAGE_WAITING) {
            return true;
        }
    }
    return false;
}

int FastModePage::getNextWaitingRow() const
{
    QList<int> waitingRows;
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        
        // Only check for actual existing valid rows (excluding negative row numbers)
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue;
        }
        
        if (it.value().currentStage == TimerStateData::STAGE_0) {
            waitingRows.append(rowNumber);
            qDebug() << "FastMode: Found waiting row" << rowNumber << "stage 0";
        }
    }
    
    if (waitingRows.isEmpty()) {
        qDebug() << "FastMode: No waiting rows";
        return -1; // No waiting rows
    }
    
    // Sort by row number, prioritize the smallest row number (earliest added)
    std::sort(waitingRows.begin(), waitingRows.end());
    qDebug() << "FastMode: Waiting row list:" << waitingRows << ", selecting:" << waitingRows.first();
    return waitingRows.first();
}

void FastModePage::generateAndUpdateResult(int rowNumber)
{
    DynamicRowUI* rowUI = m_dynamicRows.value(rowNumber);
    if (!rowUI || !rowUI->resultLabel) {
        return;
    }
    
    // Generate simulated result
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<uint>(QDateTime::currentMSecsSinceEpoch() % 1000));
        seeded = true;
    }
    
    double randomValue = (rand() % 1000) / 100.0; // 0-10.00 range
    QString result = QString::number(15.25 + randomValue, 'f', 2);
    
    // Update UI and data
    rowUI->resultLabel->setText(result);
    if (m_rowData.contains(rowNumber)) {
        m_rowData[rowNumber].result = result;
    }

    // 将完成的结果移动到固定结果行并删除原行
    moveCompletedResultToFixedRow(rowNumber);

    qDebug() << "FastMode: Row" << rowNumber << "generated result:" << result;
}

void FastModePage::cleanupInvalidTimerStates()
{
    QList<int> invalidRows;
    
    // Find all invalid Timer states
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        int rowNumber = it.key();
        // Cleanup conditions: 1) negative row numbers 2) not in data 3) not in UI
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            invalidRows.append(rowNumber);
        }
    }
    
    // Clean up invalid states
    for (int rowNumber : invalidRows) {
        m_timerStates.remove(rowNumber);
        qDebug() << "FastMode: Cleaned invalid Timer state for row" << rowNumber;
    }
    
    if (!invalidRows.isEmpty()) {
        qDebug() << "FastMode: Cleaned" << invalidRows.size() << "invalid Timer states";
    }
}

void FastModePage::startNextWaitingTest()
{
    qDebug() << "FastMode: startNextWaitingTest called";
    
    // First, clean up all invalid Timer states
    cleanupInvalidTimerStates();
    
    // Check if there are still pre-incubation stage 1-2 tests
    if (hasTestsInPreIncubationStages()) {
        qDebug() << "FastMode: Still pre-incubation stage 1-2 tests, do not start new test";
        return;
    }
    
    // Get the next waiting test (should all be valid after cleanup)
    int nextRowNumber = getNextWaitingRow();
    if (nextRowNumber == -1) {
        qDebug() << "FastMode: No waiting tests";
        return;
    }
    
    // Start the next test
    TimerStateData& timerState = m_timerStates[nextRowNumber];
    timerState.currentStage = TimerStateData::STAGE_1;
    timerState.remainingSeconds = 30; // 30 second countdown
    timerState.isClickable = false;
    
    updateTimerUI(nextRowNumber);
    
    qDebug() << "FastMode: Starting waiting test row" << nextRowNumber << "entering stage 1, remaining time:" << timerState.remainingSeconds;
}

// 项目配置方法实现
int FastModePage::getIncubationTimeForProject(const QString& projectType) const
{
    // 根据项目类型返回温育时间（秒）
    if (projectType.contains("CRP", Qt::CaseInsensitive) || projectType.contains("HS-CRP", Qt::CaseInsensitive)) {
        return 90; // CRP/HS-CRP项目：1分30秒
    } else if (projectType.contains("PCT", Qt::CaseInsensitive)) {
        return 15 * 60; // PCT项目：15分钟
    } else {
        return 15 * 60; // 默认：15分钟
    }
}

bool FastModePage::shouldSkipStages01ForProject(const QString& projectType, const QString& sampleType) const
{
    Q_UNUSED(sampleType); // 不再校验样本类型，避免编译警告
    // CRP/HS-CRP项目跳过阶段0和1
    return projectType.contains("CRP", Qt::CaseInsensitive);
}

bool FastModePage::isProjectSampleMatch(const QString& projectType, const QString& sampleType) const
{
    // 去掉样本类型校验，所有项目都匹配
    Q_UNUSED(projectType);
    Q_UNUSED(sampleType);
    return true;
}

bool FastModePage::hasCRPInIncubation() const
{
    // 检查是否有CRP/HS-CRP项目在温育阶段(STAGE_3)
    for (auto it = m_timerStates.constBegin(); it != m_timerStates.constEnd(); ++it) {
        const TimerStateData& timerState = it.value();

        // 检查是否在温育阶段且是CRP项目
        if (timerState.currentStage == TimerStateData::STAGE_3 &&
            timerState.projectType.contains("CRP", Qt::CaseInsensitive)) {
            return true;
        }
    }
    return false;
}

void FastModePage::convertStage2ToWaiting(int excludeRowNumber)
{
    // 将除了excludeRowNumber之外的所有阶段2样本转换为Waiting状态
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        TimerStateData& timerState = it.value();

        if (rowNumber != excludeRowNumber && timerState.currentStage == TimerStateData::STAGE_2) {
            timerState.currentStage = TimerStateData::STAGE_WAITING;
            timerState.remainingSeconds = 0;
            timerState.isClickable = false;
            updateTimerUI(rowNumber);
            qDebug() << "FastMode: Row" << rowNumber << "converted from stage 2 to waiting";
        }
    }
}

void FastModePage::restoreWaitingToStage2()
{
    // 将所有Waiting状态的样本恢复到阶段2
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        TimerStateData& timerState = it.value();

        if (timerState.currentStage == TimerStateData::STAGE_WAITING) {
            timerState.currentStage = TimerStateData::STAGE_2;
            timerState.remainingSeconds = 0;
            timerState.isClickable = true;
            updateTimerUI(rowNumber);
            qDebug() << "FastMode: Row" << rowNumber << "restored from waiting to stage 2";
        }
    }
}

void FastModePage::onGlobalTimerTick()
{
    // First, clean up invalid Timer states
    cleanupInvalidTimerStates();
    
    // Update countdown for all active Timers
    for (auto it = m_timerStates.begin(); it != m_timerStates.end(); ++it) {
        int rowNumber = it.key();
        TimerStateData& timerState = it.value();
        
        // Verify if the row still exists and is a valid row number
        if (rowNumber < 0 || !m_rowData.contains(rowNumber) || !m_dynamicRows.contains(rowNumber)) {
            continue; // Skip non-existent or invalid rows
        }
        
        bool needsUpdate = false;
        
        switch (timerState.currentStage) {
            case TimerStateData::STAGE_1:
                // Stage 1 countdown
                if (timerState.remainingSeconds > 0) {
                    timerState.remainingSeconds--;
                    needsUpdate = true;
                    
                    if (timerState.remainingSeconds <= 0) {
                        // Automatically enter stage 2
                        timerState.currentStage = TimerStateData::STAGE_2;
                        timerState.isClickable = true;
                        qDebug() << "FastMode: Row" << rowNumber << "automatically entering stage 2";
                    }
                }
                break;
                
            case TimerStateData::STAGE_3:
                // Stage 3 incubation countdown
                if (timerState.remainingSeconds > 0) {
                    timerState.remainingSeconds--;
                    needsUpdate = true;
                    
                    if (timerState.remainingSeconds <= 0) {
                        // Automatically enter stage 4
                        timerState.currentStage = TimerStateData::STAGE_4;
                        timerState.remainingSeconds = 0;
                        timerState.stage4StartTime = QDateTime::currentDateTime();

                        // 检查是否是CRP项目
                        bool isCRPProject = timerState.projectType.contains("CRP", Qt::CaseInsensitive);

                        if (isCRPProject) {
                            // CRP项目：自动开始读数倒计时，不需要用户点击
                            timerState.isClickable = false;
                            timerState.remainingSeconds = 10; // 10秒读数倒计时

                            qDebug() << "FastMode: Row" << rowNumber << "CRP project automatically starting 10-second reading countdown";
                        } else {
                            // 非CRP项目：需要用户点击
                            timerState.isClickable = true;
                        }

                        // 当温育完成时，恢复所有Waiting状态的样本到阶段2
                        restoreWaitingToStage2();

                        qDebug() << "FastMode: Row" << rowNumber << "automatically entering stage 4, waiting samples restored";
                    }
                }
                break;
                
            case TimerStateData::STAGE_4:
                // Stage 4 timer - CRP项目使用倒计时，其他项目记录经过时间
                {
                    bool isCRPProject = timerState.projectType.contains("CRP", Qt::CaseInsensitive);

                    if (isCRPProject) {
                        // CRP项目：倒计时读数
                        if (timerState.remainingSeconds > 0) {
                            timerState.remainingSeconds--;
                            needsUpdate = true;

                            if (timerState.remainingSeconds <= 0) {
                                // 读数倒计时结束，自动进入阶段5
                                QDateTime currentTime = QDateTime::currentDateTime();
                                qint64 diffSeconds = timerState.stage4StartTime.secsTo(currentTime);

                                timerState.currentStage = TimerStateData::STAGE_5;
                                timerState.remainingSeconds = static_cast<int>(diffSeconds);
                                timerState.isClickable = false;

                                // Generate simulated result and update Result column
                                generateAndUpdateResult(rowNumber);

                                qDebug() << "FastMode: Row" << rowNumber << "CRP reading countdown completed, automatically entering stage 5";
                            }
                        }
                    } else {
                        // 非CRP项目：记录经过时间（正计时）
                        timerState.remainingSeconds++;
                        needsUpdate = true;
                    }
                }
                break;
                
            default:
                // Other stages do not need periodic updates
                break;
        }
        
        if (needsUpdate) {
            updateTimerUI(rowNumber);
        }
    }
}

// 固定结果行管理方法实现

void FastModePage::createFixedResultRow()
{
    if (m_fixedResultRow) {
        // 如果已存在，先清理
        destroyRow(m_fixedResultRow);
        m_fixedResultRow = nullptr;
    }

    m_fixedResultRow = new DynamicRowUI();

    // 创建行容器widget，作为scrollArea的直接子控件，不受滚动影响
    m_fixedResultRow->widget = new QWidget(ui->scrollArea);
    m_fixedResultRow->widget->setObjectName("widget_fixed_result");
    m_fixedResultRow->widget->resize(1021, ROW_HEIGHT);

    // 固定位置在第5行（索引4），始终在最底部
    updateFixedResultRowPosition();

    // 创建合并的数字+患者列（数字列和患者列合并）
    int mergedWidth = COLUMN_NUMBER.width + COLUMN_PATIENT.width;
    m_fixedResultRow->numberLabel = new QLabel(m_fixedResultRow->widget);
    // 使用原始坐标，因为现在widget位置已经调整好了
    m_fixedResultRow->numberLabel->setGeometry(COLUMN_NUMBER.x, 0, mergedWidth, ROW_HEIGHT);
    m_fixedResultRow->numberLabel->setText(""); // 初始为空
    m_fixedResultRow->numberLabel->setAlignment(Qt::AlignCenter);
    m_fixedResultRow->numberLabel->setWordWrap(true);

    // 患者列设为nullptr，因为已合并到数字列
    m_fixedResultRow->patientLabel = nullptr;

    // 创建其他列，使用原始坐标
    m_fixedResultRow->paramLabel = createColumnLabel(m_fixedResultRow->widget, COLUMN_PARAMETER, "");
    m_fixedResultRow->resultLabel = createColumnLabel(m_fixedResultRow->widget, COLUMN_RESULT, "");
    m_fixedResultRow->datetimeLabel = createColumnLabel(m_fixedResultRow->widget, COLUMN_DATETIME, "");
    m_fixedResultRow->lotLabel = createColumnLabel(m_fixedResultRow->widget, COLUMN_LOT, "");
    m_fixedResultRow->cutoffLabel = createColumnLabel(m_fixedResultRow->widget, COLUMN_CUTOFF, "");

    // 创建Timer列，使用原始坐标
    m_fixedResultRow->timerBgLabel = new QLabel(m_fixedResultRow->widget);
    m_fixedResultRow->timerBgLabel->setGeometry(COLUMN_TIMER.x, 0, COLUMN_TIMER.width, ROW_HEIGHT);
    m_fixedResultRow->timerBgLabel->setScaledContents(true);

    m_fixedResultRow->timerLabel = new QLabel(m_fixedResultRow->widget);
    m_fixedResultRow->timerLabel->setGeometry(COLUMN_TIMER.x, 0, COLUMN_TIMER.width, ROW_HEIGHT);
    m_fixedResultRow->timerLabel->setText("");
    m_fixedResultRow->timerLabel->setAlignment(Qt::AlignCenter);
    m_fixedResultRow->timerLabel->setWordWrap(true);
    m_fixedResultRow->timerLabel->setStyleSheet("background-color: transparent; border: none;");

    // 应用样式
    applyFixedResultRowStyles();

    // 显示行
    m_fixedResultRow->widget->show();

    qDebug() << "FastMode: Created fixed result row at position" << (4 * ROW_HEIGHT);
}

void FastModePage::applyFixedResultRowStyles()
{
    if (!m_fixedResultRow) return;

    // 标准表格边框样式，与普通行保持一致
    QString standardStyle =
        "border: 1px solid black; "
        "padding: 2px; "
        "background-color: white; "
        "color: #333333; "
        "font-size: 10pt; "
        "qproperty-alignment: AlignCenter; "
        "qproperty-wordWrap: true;";

    // 合并的数字+患者列样式
    if (m_fixedResultRow->numberLabel) {
        m_fixedResultRow->numberLabel->setStyleSheet(standardStyle);
        m_fixedResultRow->numberLabel->setWordWrap(true);
    }

    // 其他列使用标准样式
    if (m_fixedResultRow->paramLabel) {
        m_fixedResultRow->paramLabel->setStyleSheet(standardStyle);
        m_fixedResultRow->paramLabel->setWordWrap(true);
    }
    if (m_fixedResultRow->resultLabel) {
        m_fixedResultRow->resultLabel->setStyleSheet(
            "border: 1px solid black; "
            "padding: 4px; "
            "background-color: white; "
            "color: #D32F2F; "  // 红色结果
            "font-weight: bold; "
            "font-size: 11pt; "
            "qproperty-alignment: AlignCenter; "
            "qproperty-wordWrap: true;"
        );
        m_fixedResultRow->resultLabel->setWordWrap(true);
    }
    if (m_fixedResultRow->datetimeLabel) {
        m_fixedResultRow->datetimeLabel->setStyleSheet(standardStyle);
        m_fixedResultRow->datetimeLabel->setWordWrap(true);
    }
    if (m_fixedResultRow->lotLabel) {
        m_fixedResultRow->lotLabel->setStyleSheet(standardStyle);
        m_fixedResultRow->lotLabel->setWordWrap(true);
    }
    if (m_fixedResultRow->cutoffLabel) {
        m_fixedResultRow->cutoffLabel->setStyleSheet(standardStyle);
        m_fixedResultRow->cutoffLabel->setWordWrap(true);
    }

    // Timer列样式
    if (m_fixedResultRow->timerBgLabel) {
        m_fixedResultRow->timerBgLabel->setStyleSheet(standardStyle);
    }
    if (m_fixedResultRow->timerLabel) {
        m_fixedResultRow->timerLabel->setStyleSheet("background-color: transparent; border: none; "
                                                   "color: #333333; font-size: 10pt; "
                                                   "qproperty-alignment: AlignCenter; "
                                                   "qproperty-wordWrap: true;");
        m_fixedResultRow->timerLabel->setWordWrap(true);
    }
}

void FastModePage::updateFixedResultRow()
{
    if (!m_fixedResultRow) {
        return;
    }

    // 第五行始终显示，即使没有最新结果也显示空行
    if (!m_hasLatestResult) {
        // 显示空的第五行，但保持边框样式
        if (m_fixedResultRow->numberLabel) {
            m_fixedResultRow->numberLabel->setText("");
        }
        if (m_fixedResultRow->paramLabel) {
            m_fixedResultRow->paramLabel->setText("");
        }
        if (m_fixedResultRow->resultLabel) {
            m_fixedResultRow->resultLabel->setText("");
        }
        if (m_fixedResultRow->datetimeLabel) {
            m_fixedResultRow->datetimeLabel->setText("");
        }
        if (m_fixedResultRow->lotLabel) {
            m_fixedResultRow->lotLabel->setText("");
        }
        if (m_fixedResultRow->cutoffLabel) {
            m_fixedResultRow->cutoffLabel->setText("");
        }
        if (m_fixedResultRow->timerLabel) {
            m_fixedResultRow->timerLabel->setText("");
        }

        // 确保样式正确应用（包括边框）
        applyFixedResultRowStyles();

        // 显示空的固定行
        if (m_fixedResultRow->widget) {
            m_fixedResultRow->widget->show();
            m_fixedResultRow->widget->raise(); // 确保在最上层
        }
        qDebug() << "FastMode: Showing empty fixed result row with borders";
        return;
    }

    // 更新固定结果行的显示内容
    if (m_fixedResultRow->numberLabel) {
        // 合并列显示患者信息（不显示数字）
        m_fixedResultRow->numberLabel->setText(m_latestResult.patient);
    }

    if (m_fixedResultRow->paramLabel) {
        m_fixedResultRow->paramLabel->setText(m_latestResult.parameter);
    }

    if (m_fixedResultRow->resultLabel) {
        m_fixedResultRow->resultLabel->setText(m_latestResult.result);
    }

    if (m_fixedResultRow->datetimeLabel) {
        m_fixedResultRow->datetimeLabel->setText(m_latestResult.datetime);
    }

    if (m_fixedResultRow->lotLabel) {
        m_fixedResultRow->lotLabel->setText(m_latestResult.lot);
    }

    if (m_fixedResultRow->cutoffLabel) {
        m_fixedResultRow->cutoffLabel->setText(m_latestResult.cutoff);
    }

    if (m_fixedResultRow->timerLabel) {
        m_fixedResultRow->timerLabel->setText(m_latestResult.timer);
    }

    // 显示固定行
    if (m_fixedResultRow->widget) {
        m_fixedResultRow->widget->show();
        m_fixedResultRow->widget->raise(); // 确保在最上层
    }

    qDebug() << "FastMode: Updated fixed result row with latest result:" << m_latestResult.patient;
}

void FastModePage::updateLatestResult(int rowNumber)
{
    // 检查行是否存在且已完成测试
    if (!m_rowData.contains(rowNumber) || !m_timerStates.contains(rowNumber)) {
        return;
    }

    const TimerStateData& timerState = m_timerStates[rowNumber];
    if (timerState.currentStage != TimerStateData::STAGE_5) {
        // 只有完成状态的测试才更新到结果行
        return;
    }

    // 更新最新结果数据（不删除原行，仅用于显示更新）
    m_latestResult = m_rowData[rowNumber];
    m_hasLatestResult = true;

    // 更新固定结果行显示
    updateFixedResultRow();

    qDebug() << "FastMode: Updated latest result from row" << rowNumber << "patient:" << m_latestResult.patient;
}

void FastModePage::deleteSampleRow(int rowNumber)
{
    qDebug() << "FastMode: Deleting sample row" << rowNumber;

    // 检查行是否存在
    if (!m_rowData.contains(rowNumber)) {
        qDebug() << "FastMode: Row" << rowNumber << "does not exist, cannot delete";
        return;
    }

    // 获取行数据，准备从数据库删除对应记录
    SampleRowData rowData = m_rowData[rowNumber];
    
    // 从JSON文件数据库删除对应的TestResult记录
    bool databaseDeleteSuccess = false;
    if (rowData.resultId > 0) {
        qDebug() << "FastMode: Attempting to delete TestResult from database, resultId:" << rowData.resultId;
        
        FileStorage* storage = FileStorage::getInstance();
        if (storage->initialize()) {
            databaseDeleteSuccess = storage->deleteTestResult(rowData.resultId);
            if (databaseDeleteSuccess) {
                qDebug() << "FastMode: Successfully deleted TestResult from database, resultId:" << rowData.resultId;
            } else {
                qDebug() << "FastMode: Failed to delete TestResult from database, resultId:" << rowData.resultId 
                         << "Error:" << storage->getLastError();
            }
        } else {
            qDebug() << "FastMode: Failed to initialize FileStorage for deletion, resultId:" << rowData.resultId
                     << "Error:" << storage->getLastError();
        }
    } else {
        qDebug() << "FastMode: Row" << rowNumber << "has invalid resultId (" << rowData.resultId 
                 << "), attempting alternative deletion method";
        
        // 备用删除方法：通过患者信息和参数类型查找并删除对应记录
        databaseDeleteSuccess = deleteTestResultByRowData(rowData);
    }

    // 停止该行的Timer状态
    if (m_timerStates.contains(rowNumber)) {
        TimerStateData& timerState = m_timerStates[rowNumber];

        // 如果删除的是正在孵育的样本，需要恢复其他等待样本
        if (timerState.currentStage == TimerStateData::STAGE_3) {
            qDebug() << "FastMode: Deleting incubating sample, restoring waiting samples";
            restoreWaitingToStage2();
        }

        // 移除Timer状态
        m_timerStates.remove(rowNumber);
    }

    // 销毁UI组件
    if (m_dynamicRows.contains(rowNumber)) {
        DynamicRowUI* rowUI = m_dynamicRows[rowNumber];
        destroyRow(rowUI);
        m_dynamicRows.remove(rowNumber);
    }

    // 移除行数据
    m_rowData.remove(rowNumber);

    // 更新可见行列表
    m_visibleRows.removeAll(rowNumber);

    // 重新排序和更新行号显示
    updateRowNumbers();

    // 重新排列所有行位置，确保下面的行往上移
    repositionAllRows();

    // 更新滚动内容
    updateScrollableContent();

    // 更新可见行
    updateVisibleRows();

    // 确保至少显示4行（如果删除后行数不足，补充空行）
    ensureMinimumRows();

    qDebug() << "FastMode: Successfully deleted row" << rowNumber;
}

void FastModePage::updateRowNumbers()
{
    // 获取所有行号并排序
    QList<int> sortedRowNumbers = m_rowData.keys();
    std::sort(sortedRowNumbers.begin(), sortedRowNumbers.end(), std::greater<int>());

    // 更新每行的显示序号
    for (int i = 0; i < sortedRowNumbers.size(); ++i) {
        int rowNumber = sortedRowNumbers[i];
        if (m_dynamicRows.contains(rowNumber)) {
            DynamicRowUI* rowUI = m_dynamicRows[rowNumber];
            if (rowUI && rowUI->numberLabel) {
                QString numberText = QString::number(i + 1);
                rowUI->numberLabel->setText(numberText);
            }
        }
    }

    qDebug() << "FastMode: Updated row numbers for" << sortedRowNumbers.size() << "rows";
}

void FastModePage::updateFixedResultRowPosition()
{
    if (!m_fixedResultRow || !m_fixedResultRow->widget) {
        return;
    }

    // 计算固定位置：在scrollArea内部的底部，相对于scrollArea的坐标
    // 第5行的位置（索引4），但相对于scrollArea而不是scrollAreaWidgetContents
    int yPos = 4 * ROW_HEIGHT;

    // 考虑scrollArea和scrollAreaWidgetContents之间的坐标差异
    // scrollAreaWidgetContents通常在scrollArea内部有边距
    QPoint contentPos = ui->scrollAreaWidgetContents->pos();
    int xPos = contentPos.x() - 12; // scrollAreaWidgetContents的x位置 + 普通行的偏移

    // 设置固定位置，不受滚动影响
    m_fixedResultRow->widget->move(xPos, yPos);

    // 确保固定结果行在最上层显示
    m_fixedResultRow->widget->raise();

    qDebug() << "FastMode: Updated fixed result row position to x=" << xPos << ", y=" << yPos
             << "ScrollArea geometry:" << ui->scrollArea->geometry()
             << "ScrollAreaWidgetContents geometry:" << ui->scrollAreaWidgetContents->geometry()
             << "Content position:" << contentPos;
}

void FastModePage::ensureFixedResultRowVisible()
{
    // 确保第五行存在
    if (!m_fixedResultRow) {
        createFixedResultRow();
        qDebug() << "FastMode: Created missing fixed result row";
    }

    // 确保第五行可见并应用样式
    if (m_fixedResultRow && m_fixedResultRow->widget) {
        // 确保样式正确应用
        applyFixedResultRowStyles();

        // 更新显示内容
        updateFixedResultRow();

        // 确保位置正确
        updateFixedResultRowPosition();

        // 显示并置顶
        m_fixedResultRow->widget->show();
        m_fixedResultRow->widget->raise();

        qDebug() << "FastMode: Ensured fixed result row is visible with proper styling";
    }

    // 确保背景遮罩位置正确
    updateBackgroundMaskPosition();
}

void FastModePage::createBackgroundMask()
{
    if (m_backgroundMask) {
        delete m_backgroundMask;
        m_backgroundMask = nullptr;
    }

    // 创建背景遮罩，作为scrollArea的直接子控件
    m_backgroundMask = new QWidget(ui->scrollArea);
    m_backgroundMask->setObjectName("background_mask");

    // 设置背景样式，与页面背景一致
    m_backgroundMask->setStyleSheet(
        "QWidget#background_mask {"
        "    background-color: #f0f0f0;"  // 与页面背景色一致
        "    border: none;"
        "}"
    );

    // 更新位置和大小
    updateBackgroundMaskPosition();

    // 显示遮罩
    m_backgroundMask->show();

    qDebug() << "FastMode: Created background mask";
}

void FastModePage::updateBackgroundMaskPosition()
{
    if (!m_backgroundMask) {
        return;
    }

    // 遮罩位置：从第5行下方开始，覆盖到scrollArea底部
    int maskTop = 5 * ROW_HEIGHT;  // 第5行下方
    int maskHeight = ui->scrollArea->height() - maskTop;

    // 如果高度为负数或0，隐藏遮罩
    if (maskHeight <= 0) {
        m_backgroundMask->hide();
        return;
    }

    // 设置遮罩的位置和大小
    m_backgroundMask->setGeometry(0, maskTop, ui->scrollArea->width(), maskHeight);
    m_backgroundMask->show();

    qDebug() << "FastMode: Updated background mask position - top:" << maskTop
             << "height:" << maskHeight;
}

void FastModePage::moveCompletedResultToFixedRow(int rowNumber)
{
    // 检查行是否存在且已完成测试
    if (!m_rowData.contains(rowNumber) || !m_timerStates.contains(rowNumber)) {
        return;
    }

    const TimerStateData& timerState = m_timerStates[rowNumber];
    if (timerState.currentStage != TimerStateData::STAGE_5) {
        // 只有完成状态的测试才移动到结果行
        return;
    }

    // 更新最新结果数据
    m_latestResult = m_rowData[rowNumber];
    m_hasLatestResult = true;

    // 更新固定结果行显示
    updateFixedResultRow();

    qDebug() << "FastMode: Moving completed result from row" << rowNumber << "to fixed row, patient:" << m_latestResult.patient;

    // 删除原行（包括UI和数据）
    deleteSampleRow(rowNumber);

    // 确保所有行重新排列，下面的行往上移
    repositionAllRows();

    // 确保最小行数（如果删除后行数不足，添加空行）
    ensureMinimumRows();

    qDebug() << "FastMode: Successfully moved result to fixed row and repositioned remaining rows";
}

bool FastModePage::deleteTestResultByRowData(const SampleRowData& rowData)
{
    qDebug() << "FastMode: Attempting alternative deletion method for row data";
    
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        qDebug() << "FastMode: Failed to initialize FileStorage for alternative deletion, error:" << storage->getLastError();
        return false;
    }
    
    // 从行数据中提取患者信息
    QString patientInfo = rowData.patient;
    QStringList patientParts = patientInfo.split(",");
    if (patientParts.size() < 3) {
        qDebug() << "FastMode: Invalid patient info format for alternative deletion:" << patientInfo;
        return false;
    }
    
    // 解析患者ID（最后一部分）
    QString patientIdStr = patientParts.last().trimmed();
    bool ok;
    int patientId = patientIdStr.toInt(&ok);
    if (!ok) {
        qDebug() << "FastMode: Invalid patient ID for alternative deletion:" << patientIdStr;
        return false;
    }
    
    // 获取该患者的所有FastMode测试结果
    QList<TestResult> patientResults = storage->getTestResultsByPatientId(patientId);
    QList<TestResult> fastModeResults;
    for (const TestResult& result : patientResults) {
        if (result.getTestMode() == SourcePageType::FAST_MODE) {
            fastModeResults.append(result);
        }
    }
    
    if (fastModeResults.isEmpty()) {
        qDebug() << "FastMode: No FastMode results found for patient ID:" << patientId;
        return false;
    }
    
    // 尝试通过参数类型和结果匹配找到对应记录
    QString parameter = rowData.parameter;
    QString result = rowData.result;
    
    TestResult* matchedResult = nullptr;
    int bestMatchScore = 0;
    
    for (int i = 0; i < fastModeResults.size(); ++i) {
        const TestResult& testResult = fastModeResults[i];
        int matchScore = 0;
        
        // 参数类型完全匹配 (+3分)
        if (testResult.getParameterType() == parameter) {
            matchScore += 3;
        }
        
        // 结果匹配 (+2分)
        if (testResult.getTestResult() == result) {
            matchScore += 2;
        }
        
        // 批号匹配 (+1分)
        if (testResult.getLotNumber() == rowData.lot) {
            matchScore += 1;
        }
        
        // 截止值匹配 (+1分)
        if (testResult.getCutoffValue() == rowData.cutoff) {
            matchScore += 1;
        }
        
        // 更新最佳匹配
        if (matchScore > bestMatchScore) {
            bestMatchScore = matchScore;
            matchedResult = const_cast<TestResult*>(&testResult);
        }
    }
    
    // 至少需要参数类型匹配才进行删除
    if (bestMatchScore >= 3 && matchedResult != nullptr) {
        int resultId = matchedResult->getResultId();
        bool deleteSuccess = storage->deleteTestResult(resultId);
        
        if (deleteSuccess) {
            qDebug() << "FastMode: Alternative deletion successful, deleted TestResult ID:" << resultId
                     << "match score:" << bestMatchScore << "parameter:" << parameter;
            return true;
        } else {
            qDebug() << "FastMode: Alternative deletion failed for TestResult ID:" << resultId
                     << "error:" << storage->getLastError();
            return false;
        }
    } else {
        qDebug() << "FastMode: No suitable match found for alternative deletion, best score:" << bestMatchScore
                 << "parameter:" << parameter << "result:" << result;
        return false;
    }
}
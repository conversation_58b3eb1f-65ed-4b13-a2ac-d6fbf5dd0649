# 160901(1)_1_45_translate_20250526171032 - 第 4 部分

页面 16 - 20

---

## 第 16 页

HumaFIA |用户手册
18
3 日常使用和测量
3.1 仪器操作和样品制备
HumaFIA分析仪是一种半自动仪器。用户必须准备样本，孵育试剂盒（在分析仪内自动或在外部手动），将样
本试剂盒插入分析仪的卡匣输入槽，并开始读取结果。更多详情请参见下文。
对于样本制备、孵育和试剂盒处理，请遵循各HumaFIA参数的使用说明，因为样本类型、抗凝剂、样本体积和
缓冲液的使用也取决于参数。
3.1.1 样本
1. 通常，可以使用全血、血清和血浆。
2. 应在采血后2小时内通过离心分离血清或血浆。
3. 立即或在样本采集后2小时内检测样本。如果无法在2小时内完成检测
将血清或血浆在+2°+8°C下储存8小时；如果需要更长时间的储存，将血清或血浆在-20°C以下储存3
个月。不要加热灭活样品。丢弃溶血样品。
3.1.2 程序
严格按照描述的程序进行：
1. 在进行样品测试前，请仔细阅读本说明书和适用的仪器用户手册。使用冷藏测试盒前，请使其达到工作
温度(18-28℃）。将测试盒放置在水平的平面上。
2. 检查测试盒的批号是否与校准卡一致。
3. 将校准卡插入仪器的校准卡端口进行测试。
4. 冷藏的血清或血浆标本必须在检测前达到室温，冷冻的血浆或血清标本解冻后应进行涡旋和离心处理，
上清液应收集并达到室温后方可检测。
5. 如果需要，将血清／血浆／全血移入稀释剂小瓶中（参见参数IFU)。重新盖上小瓶盖，将小瓶倒置10
次以混合样品混合物。
6. 将80µL（稀释）样品精确移入检测盒的样品口。启动计时器，反应需要几分钟（根据IFU)。
7. 反应时间结束后，将测试盒插入分析仪中，选择以下三种模式之一的准确样本类型来运行样本。分析仪
会自动检测并检查测试盒类型。一旦分析仪读取测试结果，屏幕上就会显示结果，分析完成后可以打
印。
8. 完成检测后，将试剂盒从分析仪中取出。


---

## 第 17 页

HumaFIA |用户手册
19
9. 根据任何适用法规处置用过的滤芯和移液管头。
3.1.3 校准
主曲线校准：每个HumaFIA SR试剂盒都有一个校准卡[CAL]，其中包含用于校准的批次特定信息。从卡中上传
校准曲线后，该批次的校准将存储在仪器上。所有后续样本都可以在无需进一步上传的情况下进行测试。校
准卡可以随时上传。
3.1.4 结果计算
HumaFIA系统自动计算每个样本的分析物浓度。参见第3.3.5节“ 质控模块中的校准卡概述” 中的校准曲线上
传。
3.1.5 质量控制
质量控制方面，使用人类提供的相应质量控制产品：
每天一次，在测试前。
如果有任何意外的测试结果。
每次上传校准后。
质量控制结果不在可接受范围内可能表明试验结果无效。
3.1.6 参考值
IFU中提供了参考值，可将每个参数的参考值/范围存储在分析仪中。
建议每个实验室为其服务的人群建立自己的预期值。
3.1.7 结果解释
如果样品中的浓度超过线性范围，则在检测前用5 % BSA生理盐水溶液稀释样品。HumaFIA提供软件选项，可
自动计算预稀释样品的值。每个样品最多可稀释3次。
检测结果不能作为诊断的唯一依据，应结合其他临床和实验室数据进行临床诊断，如果检测结果与临床评估
不一致，应进一步检查。
3.1.8 局限性
某些含有针对试剂组分的抗体的样本可能会造成干扰。因此，应考虑患者的临床病史以及进行的任何其他检
测的结果来解释检测结果。
如果样本中含有高浓度的甘油三酯、胆固醇、胆红素或溶血，检测结果将受到影响。
与使用小鼠抗体的任何试验一样，样本中的人抗小鼠抗体（HAMA）可能产生干扰。试验的制定旨在将这种干
扰降至最低；然而，样本来自


---

## 第 18 页

HumaFIA |用户手册
20
经常接触动物产品或动物血清的患者可能含有异嗜性抗体，这可能导致错误的结果。详情请参见IFU。
注：每个药筒的内包装/袋必须密封严密，无渗漏现象，测试卡表面应光滑、无毛刺，颜色均匀。
安全须知：所有患者样本、校准品和质控品均应视为可能具有传染性的物质处理。所有来源为人类的供体单
位均已使用批准的方法检测了HBsAg、HIV和HCV抗体，结果均未呈阳性反应。所有动物源材料可避免使用人血
清（如乙型肝炎、丙型肝炎和HIV)带来的诸多风险。然而，所有来源为人或动物的材料仍应视为可能具有传
染性的物质处理。
3.2 取样模式
HumaFIA软件提供了3种模式来运行样本。
a)
自动采样模式
b)
STAT样本模式
c)
快速模式
3.2.1 自动采样模式，具有内部孵育功能
选择自动取样后，将显示以下屏幕。
图3-1自动样本模式，用于自动孵育和读取测试
自动取样模式允许对测试进行自动取样。分析仪在分析仪内完成测试孵育后，自动管理孵育时间和读取测试
结果。
如果孵育时间为1.5分钟，例如CRP参数，则在此孵育/测试时间内不能进行其他试验。
使用“ 添加样本” 按钮，通过选择患者姓名/ID和参数类型，将新测试添加到工作列表中。请参阅下面的第
3.2.4章“ 添加样本、患者数据和参数类型” 。


---

## 第 19 页

HumaFIA |用户手册
21
按下“ 插入培养盒” 按钮，关闭样本详细信息界面并启动软件计时器。
将患者样本加入到试剂盒中，然后将试剂盒放入分析仪中，最后按下此按钮。
按下此按钮，启动自动孵育计时器。孵育时间结束后，测试读数为
由软件自动触发。结果的准确性取决于正确的孵育时间。因此，在准备试剂盒和启动软件计时器时，时间非
常重要。
剩余孵育时间以mm：ss格式显示在沙漏旁边的计时器列中。在LOT列中，显示所用试剂盒的LOT编号。在Cut-
off列中，显示临床临界值。
分析仪自动读取结果后，会将结果及其对应的单位括号显示。黑色表示正常，红色则用箭头标记，表明结果
超出正常范围。向上箭头表示值高于正常水平，向下箭头则表示值低于正常范围。显示读取的日期和时间，
以及样本类型。S=血清，B=全血，P=血浆。
注：3合1检测，如cTnI/Myo/CK-MB显示在3行中。3合1试剂盒的每个参数在屏幕上显示在单独的一行中。参数
名称以两个点开始和结束（例如，.. cTnI..，或.. Myo. .)。
3.2.2 STAT样本模式，灵活测试
在主屏幕上或永久性标题栏中选择STAT样本，以进入以下菜单。
图3-2 STAT样本模式，便于灵活读取检测结果
STAT模式允许在分析仪外孵育样本，并在孵育时间允许的情况下，在中间运行另一个样本。在这种模式下，
分析仪不监测孵育时间。因此，操作员必须通过手动方法确保正确的孵育时间，例如使用计时器。


---

## 第 20 页

HumaFIA |用户手册
22
首先，使用“ 添加样本” 按钮定义下一个样本。这将导致第3.2.4章“ 添加样本、患者数据和参数类型” 中描
述的过程。
按下“ 读取” 按钮，将培养盒置于上一个屏幕“ 添加样本” 上，关闭样本详细信息界面并开
始读取培养盒。
将孵育后的试剂盒插入分析仪中，并在孵育时间结束时按下此按钮。
按下此按钮立即开始读取结果。结果的准确性取决于正确的孵育时间。因此，在准备试剂盒、启动计时器和
然后开始读取时，时间非常重要。
注：3合1检测，如cTnI/Myo/CK-MB，显示在3行中。3合1检测盒的每个参数在屏幕上单独显示一行。参数名称
以两个点开头和结尾（例如，.. cTnI..，或.. Myo. .)。
3.2.3 快速模式采样
在主屏幕上或永久性标题栏中选择快速模式，以访问以下菜单。
图3-3快速模式，用于读取一系列测试
快速模式允许用户运行一系列测试，并每20秒提供一个测试结果。工作流管理软件通过管
理以下内容支持快速测试：
-何时将样本添加到卡盒中，
-何时将完全孵育的试剂盒插入分析仪进行读取
-保证质量结果的正确孵育时间
这样，可以轻松地管理一系列测试，甚至不同参数类型的测试。
首先，通过“ 添加样本” 按钮定义患者工作列表以添加样本。这将导致第3.2.4章“ 添
加样本、患者数据和参数类型” 中描述的过程。


---


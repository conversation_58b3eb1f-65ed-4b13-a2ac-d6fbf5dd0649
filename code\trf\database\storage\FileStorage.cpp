#include "FileStorage.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QMutexLocker>
#include <QDir>
#include <QFileInfo>
#include <QTemporaryFile>

// 静态成员变量定义
FileStorage* FileStorage::m_instance = nullptr;
QMutex FileStorage::m_mutex;

FileStorage* FileStorage::getInstance()
{
    QMutexLocker locker(&m_mutex);
    if (m_instance == nullptr) {
        m_instance = new FileStorage();
    }
    return m_instance;
}

FileStorage::FileStorage(QObject *parent)
    : QObject(parent)
    , m_patientsLoaded(false)
    , m_testResultsLoaded(false)
    , m_configLoaded(false)
    , m_patientsModified(false)
    , m_testResultsModified(false)
    , m_configModified(false)
{
}

FileStorage::~FileStorage()
{
    // 保存所有修改的数据
    if (m_patientsModified) {
        savePatientData();
    }
    if (m_testResultsModified) {
        saveTestResultData();
    }
    if (m_configModified) {
        saveConfigData();
    }
}

bool FileStorage::initialize()
{
    qDebug() << "Initializing file storage system (SQLite replacement)...";
    
    // 设置存储目录
    m_storageDir = getStorageDir();
    
    // 创建存储目录
    QDir dir;
    if (!dir.mkpath(m_storageDir)) {
        m_lastError = QString("Cannot create storage directory: %1").arg(m_storageDir);
        qCritical() << m_lastError;
        return false;
    }
    
    // 加载现有数据
    if (!loadConfigData()) {
        qWarning() << "Configuration data loading failed, using default settings";
    }
    
    if (!loadPatientData()) {
        qWarning() << "Patient data loading failed, starting with empty data";
    }
    
    if (!loadTestResultData()) {
        qWarning() << "Test result data loading failed, starting with empty data";
    }
    
    // 设置默认配置
    if (getConfig("db_version").isEmpty()) {
        setConfig("db_version", "1.0_file_storage");
        setConfig("max_test_results", "10000");
        setConfig("auto_cleanup_enabled", "true");
        setConfig("storage_type", "file_json");
        setConfig("created_at", QDateTime::currentDateTime().toString(Qt::ISODate));
    }
    
    qDebug() << "File storage system initialized successfully";
    qDebug() << "Storage directory:" << m_storageDir;
    qDebug() << "Patient count:" << m_patients.size();
    qDebug() << "Test result count:" << m_testResults.size();
    
    return true;
}

QString FileStorage::getStorageDir()
{
    // 使用相对路径，在可执行文件当前目录下创建data文件夹
    QStringList candidatePaths = {
        "./data",                  // 当前目录下的data文件夹
        QCoreApplication::applicationDirPath() + "/data"  // 应用程序目录下的data文件夹
    };
    
    for (const QString& path : candidatePaths) {
        QDir dir(path);
        QFileInfo info(path);
        
        // 检查路径是否可写
        if (info.exists() && info.isWritable()) {
            return path;
        }
        
        // 尝试创建目录
        if (dir.mkpath(".")) {
            QFileInfo newInfo(path);
            if (newInfo.isWritable()) {
                qDebug() << "Selected storage directory:" << path;
                return path;
            }
        }
    }
    
    // 回退到应用程序目录
    QString fallbackPath = QCoreApplication::applicationDirPath() + "/data";
    qWarning() << "Using fallback storage directory:" << fallbackPath;
    return fallbackPath;
}

QString FileStorage::getPatientFilePath()
{
    return m_storageDir + "/patients.json";
}

QString FileStorage::getTestResultFilePath()
{
    return m_storageDir + "/test_results.json";
}

QString FileStorage::getConfigFilePath()
{
    return m_storageDir + "/config.json";
}

// === 患者管理实现 ===

bool FileStorage::savePatient(const Patient& patient)
{
    if (!m_patientsLoaded) {
        loadPatientData();
    }
    
    // 检查是否是新患者
    bool isNew = true;
    for (int i = 0; i < m_patients.size(); ++i) {
        if (m_patients[i].getPatientId() == patient.getPatientId()) {
            m_patients[i] = patient;
            isNew = false;
            break;
        }
    }
    
    if (isNew) {
        Patient newPatient = patient;
        if (newPatient.getPatientId() <= 0) {
            newPatient.setPatientId(getNextPatientId());
        }
        m_patients.append(newPatient);
    }
    
    m_patientsModified = true;
    return savePatientData();
}

QList<Patient> FileStorage::getAllPatients()
{
    if (!m_patientsLoaded) {
        loadPatientData();
    }
    return m_patients;
}

Patient FileStorage::getPatientById(int patientId)
{
    if (!m_patientsLoaded) {
        loadPatientData();
    }
    
    for (const Patient& patient : m_patients) {
        if (patient.getPatientId() == patientId) {
            return patient;
        }
    }
    
    return Patient(); // 返回空患者
}

bool FileStorage::updatePatient(const Patient& patient)
{
    return savePatient(patient); // 同样的逻辑
}

bool FileStorage::deletePatient(int patientId)
{
    if (!m_patientsLoaded) {
        loadPatientData();
    }
    
    for (int i = 0; i < m_patients.size(); ++i) {
        if (m_patients[i].getPatientId() == patientId) {
            m_patients.removeAt(i);
            m_patientsModified = true;
            return savePatientData();
        }
    }
    
    return false; // 患者不存在
}

// === 测试结果管理实现 ===

bool FileStorage::saveTestResult(const TestResult& result)
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    
    TestResult newResult = result;
    if (newResult.getResultId() <= 0) {
        newResult.setResultId(getNextTestResultId());
    }
    
    // 检查是否已存在
    bool isNew = true;
    for (int i = 0; i < m_testResults.size(); ++i) {
        if (m_testResults[i].getResultId() == newResult.getResultId()) {
            m_testResults[i] = newResult;
            isNew = false;
            break;
        }
    }
    
    if (isNew) {
        m_testResults.append(newResult);
    }
    
    m_testResultsModified = true;
    return saveTestResultData();
}

QList<TestResult> FileStorage::getAllTestResults()
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    return m_testResults;
}

QList<TestResult> FileStorage::getTestResultsByPatientId(int patientId)
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    
    QList<TestResult> results;
    for (const TestResult& result : m_testResults) {
        if (result.getPatientId() == patientId) {
            results.append(result);
        }
    }
    
    return results;
}

QList<TestResult> FileStorage::getTestResultsByMode(const QString& testMode)
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    
    QList<TestResult> results;
    for (const TestResult& result : m_testResults) {
        if (TestResult::testModeToString(result.getTestMode()) == testMode) {
            results.append(result);
        }
    }
    
    return results;
}

bool FileStorage::deleteTestResult(int resultId)
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    
    for (int i = 0; i < m_testResults.size(); ++i) {
        if (m_testResults[i].getResultId() == resultId) {
            m_testResults.removeAt(i);
            m_testResultsModified = true;
            return saveTestResultData();
        }
    }
    
    return false;
}

// === 配置管理实现 ===

bool FileStorage::setConfig(const QString& key, const QString& value)
{
    if (!m_configLoaded) {
        loadConfigData();
    }
    
    m_config[key] = value;
    m_configModified = true;
    return saveConfigData();
}

QString FileStorage::getConfig(const QString& key, const QString& defaultValue)
{
    if (!m_configLoaded) {
        loadConfigData();
    }
    
    return m_config.value(key, defaultValue);
}

// === 数据清理实现 ===

bool FileStorage::clearAllData()
{
    m_patients.clear();
    m_testResults.clear();
    m_config.clear();
    
    m_patientsModified = true;
    m_testResultsModified = true;
    m_configModified = true;
    
    return savePatientData() && saveTestResultData() && saveConfigData();
}

bool FileStorage::clearPatients()
{
    m_patients.clear();
    m_patientsModified = true;
    return savePatientData();
}

bool FileStorage::clearTestResults()
{
    m_testResults.clear();
    m_testResultsModified = true;
    return saveTestResultData();
}

// === 数据统计实现 ===

int FileStorage::getPatientCount()
{
    if (!m_patientsLoaded) {
        loadPatientData();
    }
    return m_patients.size();
}

int FileStorage::getTestResultCount()
{
    if (!m_testResultsLoaded) {
        loadTestResultData();
    }
    return m_testResults.size();
}

// === ID管理实现 ===

int FileStorage::getNextPatientId()
{
    int maxId = 0;
    for (const Patient& patient : m_patients) {
        if (patient.getPatientId() > maxId) {
            maxId = patient.getPatientId();
        }
    }
    return maxId + 1;
}

int FileStorage::getNextTestResultId()
{
    int maxId = 0;
    for (const TestResult& result : m_testResults) {
        if (result.getResultId() > maxId) {
            maxId = result.getResultId();
        }
    }
    return maxId + 1;
}

// === 文件操作实现 ===

bool FileStorage::writeJsonFile(const QString& filePath, const QJsonDocument& doc)
{
    qDebug() << "Writing JSON file to:" << filePath;
    
    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        qDebug() << "Directory does not exist, creating:" << dir.absolutePath();
        if (!dir.mkpath(".")) {
            m_lastError = QString("Cannot create directory: %1").arg(dir.absolutePath());
            qCritical() << m_lastError;
            return false;
        }
    }
    
    // 直接写入文件（简化版本，避免Windows临时文件重命名问题）
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        m_lastError = QString("Cannot open file for writing: %1 - %2").arg(filePath, file.errorString());
        qCritical() << m_lastError;
        return false;
    }
    
    QByteArray jsonData = doc.toJson();
    qDebug() << "Writing" << jsonData.size() << "bytes to file";
    
    qint64 bytesWritten = file.write(jsonData);
    if (bytesWritten == -1) {
        m_lastError = QString("Failed to write to file: %1 - %2").arg(filePath, file.errorString());
        qCritical() << m_lastError;
        file.close();
        return false;
    }
    
    if (bytesWritten != jsonData.size()) {
        m_lastError = QString("Incomplete write to file: %1 (wrote %2 of %3 bytes)").arg(filePath).arg(bytesWritten).arg(jsonData.size());
        qCritical() << m_lastError;
        file.close();
        return false;
    }
    
    file.close();
    qDebug() << "Successfully wrote JSON file:" << filePath;
    return true;
}

QJsonDocument FileStorage::readJsonFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        if (file.exists()) {
            m_lastError = QString("Cannot open file: %1 - %2").arg(filePath, file.errorString());
        }
        return QJsonDocument();
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        m_lastError = QString("JSON parsing error: %1").arg(error.errorString());
        return QJsonDocument();
    }
    
    return doc;
}

// === 数据转换实现 ===

QJsonObject FileStorage::patientToJson(const Patient& patient)
{
    QJsonObject obj;
    obj["patient_id"] = patient.getPatientId();
    obj["patient_name"] = patient.getPatientName();
    obj["last_name"] = patient.getLastName();
    obj["first_name"] = patient.getFirstName();
    obj["birthday"] = patient.getBirthday().toString(Qt::ISODate);
    obj["gender"] = patient.getGender();
    obj["remarks"] = patient.getRemarks();
    obj["created_at"] = patient.getCreatedAt().toString(Qt::ISODate);
    obj["updated_at"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    return obj;
}

Patient FileStorage::patientFromJson(const QJsonObject& json)
{
    Patient patient;
    patient.setPatientId(json["patient_id"].toInt());
    patient.setPatientName(json["patient_name"].toString());
    patient.setLastName(json["last_name"].toString());
    patient.setFirstName(json["first_name"].toString());
    patient.setBirthday(QDate::fromString(json["birthday"].toString(), Qt::ISODate));
    patient.setGender(json["gender"].toString());
    patient.setRemarks(json["remarks"].toString());
    patient.setCreatedAt(QDateTime::fromString(json["created_at"].toString(), Qt::ISODate));
    
    return patient;
}

QJsonObject FileStorage::testResultToJson(const TestResult& result)
{
    QJsonObject obj;
    obj["result_id"] = result.getResultId();
    obj["patient_id"] = result.getPatientId();
    obj["test_mode"] = TestResult::testModeToString(result.getTestMode());
    obj["parameter_type"] = result.getParameterType();
    obj["sample_type"] = result.getSampleType();
    obj["test_result"] = result.getTestResult();
    obj["result_status"] = result.getResultStatus();
    obj["test_datetime"] = result.getTestDateTime().toString(Qt::ISODate);
    obj["lot_number"] = result.getLotNumber();
    obj["cutoff_value"] = result.getCutoffValue();
    obj["dilution_factor"] = result.getDilutionFactor();
    obj["created_at"] = result.getCreatedAt().toString(Qt::ISODate);
    
    return obj;
}

TestResult FileStorage::testResultFromJson(const QJsonObject& json)
{
    TestResult result;
    result.setResultId(json["result_id"].toInt());
    result.setPatientId(json["patient_id"].toInt());
    result.setTestMode(TestResult::stringToTestMode(json["test_mode"].toString()));
    result.setParameterType(json["parameter_type"].toString());
    result.setSampleType(json["sample_type"].toString());
    result.setTestResult(json["test_result"].toString());
    result.setResultStatus(json["result_status"].toString());
    result.setTestDateTime(QDateTime::fromString(json["test_datetime"].toString(), Qt::ISODate));
    result.setLotNumber(json["lot_number"].toString());
    result.setCutoffValue(json["cutoff_value"].toString());
    result.setDilutionFactor(json["dilution_factor"].toDouble());
    result.setCreatedAt(QDateTime::fromString(json["created_at"].toString(), Qt::ISODate));
    
    return result;
}

// === 数据加载/保存实现 ===

bool FileStorage::savePatientData()
{
    QJsonArray array;
    for (const Patient& patient : m_patients) {
        array.append(patientToJson(patient));
    }
    
    QJsonObject root;
    root["version"] = "1.0";
    root["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    root["count"] = array.size();
    root["patients"] = array;
    
    QJsonDocument doc(root);
    bool success = writeJsonFile(getPatientFilePath(), doc);
    
    if (success) {
        m_patientsModified = false;
        qDebug() << "Patient data saved:" << array.size() << "records";
    }
    
    return success;
}

bool FileStorage::loadPatientData()
{
    QJsonDocument doc = readJsonFile(getPatientFilePath());
    if (doc.isNull()) {
        m_patientsLoaded = true;
        return true; // 文件不存在是正常的
    }
    
    QJsonObject root = doc.object();
    QJsonArray array = root["patients"].toArray();
    
    m_patients.clear();
    for (const QJsonValue& value : array) {
        Patient patient = patientFromJson(value.toObject());
        m_patients.append(patient);
    }
    
    m_patientsLoaded = true;
    m_patientsModified = false;
    
    qDebug() << "Patient data loaded:" << m_patients.size() << "records";
    return true;
}

bool FileStorage::saveTestResultData()
{
    QJsonArray array;
    for (const TestResult& result : m_testResults) {
        array.append(testResultToJson(result));
    }
    
    QJsonObject root;
    root["version"] = "1.0";
    root["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    root["count"] = array.size();
    root["test_results"] = array;
    
    QJsonDocument doc(root);
    bool success = writeJsonFile(getTestResultFilePath(), doc);
    
    if (success) {
        m_testResultsModified = false;
        qDebug() << "Test result data saved:" << array.size() << "records";
    }
    
    return success;
}

bool FileStorage::loadTestResultData()
{
    QJsonDocument doc = readJsonFile(getTestResultFilePath());
    if (doc.isNull()) {
        m_testResultsLoaded = true;
        return true; // 文件不存在是正常的
    }
    
    QJsonObject root = doc.object();
    QJsonArray array = root["test_results"].toArray();
    
    m_testResults.clear();
    for (const QJsonValue& value : array) {
        TestResult result = testResultFromJson(value.toObject());
        m_testResults.append(result);
    }
    
    m_testResultsLoaded = true;
    m_testResultsModified = false;
    
    qDebug() << "Test result data loaded:" << m_testResults.size() << "records";
    return true;
}

bool FileStorage::saveConfigData()
{
    QJsonObject root;
    root["version"] = "1.0";
    root["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonObject configObj;
    for (auto it = m_config.begin(); it != m_config.end(); ++it) {
        configObj[it.key()] = it.value();
    }
    root["config"] = configObj;
    
    QJsonDocument doc(root);
    bool success = writeJsonFile(getConfigFilePath(), doc);
    
    if (success) {
        m_configModified = false;
        qDebug() << "Configuration data saved:" << m_config.size() << "items";
    }
    
    return success;
}

bool FileStorage::loadConfigData()
{
    QJsonDocument doc = readJsonFile(getConfigFilePath());
    if (doc.isNull()) {
        m_configLoaded = true;
        return true; // 文件不存在是正常的
    }
    
    QJsonObject root = doc.object();
    QJsonObject configObj = root["config"].toObject();
    
    m_config.clear();
    for (auto it = configObj.begin(); it != configObj.end(); ++it) {
        m_config[it.key()] = it.value().toString();
    }
    
    m_configLoaded = true;
    m_configModified = false;
    
    qDebug() << "Configuration data loaded:" << m_config.size() << "items";
    return true;
} 
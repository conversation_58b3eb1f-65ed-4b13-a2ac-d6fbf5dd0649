# Qt 5.5.1 Compatibility Guide for TRF Project

## 🎯 Target Environment
- **System**: Linux myd-y6ull14x14 4.1.15+ armv7l
- **Qt Version**: 5.5.1 (Ubuntu 16.04 ARMv7 packages)
- **Build Environment**: Ubuntu 16.04 ARM cross-compilation
- **C++ Standard**: C++11 (`-std=c++0x`)

## ⚠️ Qt 5.5.1 Limitations & Fixes

### 1. High DPI Attributes (Qt 5.6+ only)
**Problem**: `Qt::AA_EnableHighDpiScaling` doesn't exist in Qt 5.5.1
```cpp
// ❌ Causes compilation error in Qt 5.5.1
QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling, false);

// ✅ Fixed with version check
#if QT_VERSION >= QT_VERSION_CHECK(5, 6, 0) && QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling, false);
    QCoreApplication::setAttribute(Qt::AA_UseHighDpiPixmaps, false);
#endif
```

### 2. Lambda Expression Limitations
**Problem**: Qt 5.5.1 has limited lambda support in signals/slots
```cpp
// ❌ May cause issues in Qt 5.5.1
connect(button, &QPushButton::clicked, [this](){ doSomething(); });

// ✅ Traditional slot connection (always works)
connect(button, &QPushButton::clicked, this, &MyClass::doSomething);
```

### 3. C++ Standard Configuration
**Configuration**: Use C++11 instead of newer standards
```qmake
# ✅ Compatible with Qt 5.5.1
CONFIG += c++11

# ❌ May not be supported
CONFIG += c++17
```

## 🔧 Applied Fixes

### main.cpp
- Added version checks for High DPI attributes
- Ensured Qt 5.5.1 compatibility for application attributes
- Added debug output showing Qt version for verification

### Connection Syntax
- Replaced all lambda expressions with traditional slot functions
- Added compatibility slot functions in header files
- Ensured signal/slot connections work with Qt 5.5.1

### Project Configuration
- Set `CONFIG += c++11` for maximum compatibility
- Removed dependencies on Qt 5.6+ features

## 📊 Build Environment Details

From actual build log:
```
QMake version 3.0
Using Qt version 5.5.1 in /usr/lib/arm-linux-gnueabihf
Qt 5 libraries available:
- libqt5core5a:armhf              5.5.1+dfsg-16ubuntu7.7
- libqt5gui5:armhf                5.5.1+dfsg-16ubuntu7.7  
- libqt5widgets5:armhf            5.5.1+dfsg-16ubuntu7.7
- libqt5sql5:armhf                5.5.1+dfsg-16ubuntu7.7
```

## ✅ Compatibility Verification

### Compilation Flags Used:
```bash
g++ -std=c++0x -Wall -W -D_REENTRANT -fPIC \
    -DQT_DISABLE_DEPRECATED_BEFORE=0x050600 \
    -DTARGET_SYSTEM_MYD_Y6ULL14X14 \
    -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SQL_LIB -DQT_CORE_LIB
```

### Target System Match:
- **Expected**: Qt 5.6.2 on myd-y6ull14x14
- **Build Environment**: Qt 5.5.1 on Ubuntu 16.04 ARMv7
- **Compatibility**: ✅ Forward compatible (Qt 5.5.1 → Qt 5.6.2)

## 🚀 Next Steps

1. **Verify Build Success**: Check that compilation completes without errors
2. **Runtime Testing**: Test on actual myd-y6ull14x14 hardware with Qt 5.6.2
3. **Feature Validation**: Ensure all features work correctly across Qt versions

## 📝 Development Guidelines

When adding new features:
- Always check Qt version before using newer APIs
- Prefer traditional Qt patterns over modern C++ features
- Test compilation with Qt 5.5.1 in CI/CD pipeline
- Use feature flags for version-specific optimizations

---
**Last Updated**: 2025-01-18  
**Applies to**: TRF v1.0.0+ ARM embedded builds 
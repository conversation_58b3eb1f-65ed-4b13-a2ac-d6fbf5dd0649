# Timer列边框显示调试

## 问题症状
用户反馈：Timer列文字有显示，但背景和边框都不正确显示。

## 问题分析

### 可能原因
1. **CSS优先级冲突**：FastModePage的全局CSS可能覆盖了TimerWidget的样式
2. **层次结构问题**：内部QLabel可能遮挡了边框
3. **样式应用时机**：样式可能在设置后被重置

### FastModePage全局CSS影响
FastModePage.ui中有这些可能影响TimerWidget的样式：
```css
QWidget#FastModePage {
    background-color: #7f7f7f;
}

QLabel[objectName*="label_r"] {
    border: 1px solid black;
    padding: 4px;
    background-color: white;
    color: #333333;
    font-size: 11pt;
    alignment: AlignCenter;
}
```

## 修复尝试

### 尝试1：缩小文字标签尺寸 ✅
```cpp
// 给边框留出2px空间
m_textLabel->setGeometry(2, 2, 134, 62);
```

### 尝试2：设置对象名避免CSS冲突 ✅
```cpp
setObjectName("TimerWidget");
```

### 尝试3：强制重绘 ✅
```cpp
setStyleSheet(styleSheet);
update(); // 强制重绘
```

### 尝试4：使用!important提高CSS优先级 ✅
```css
QWidget {
    border: 1px solid black !important;
    padding: 2px !important;
    background-color: white !important;
}
```

## 调试信息

### 添加的调试输出
```cpp
qDebug() << "TimerWidget: 设置样式" << styleSheet;
```

### 预期调试输出
- 阶段0：白色背景 + 黑色边框
- 阶段1-4：背景图片 + 黑色边框
- 阶段5：浅灰背景 + 黑色边框

## 测试计划

### 1. 编译测试
```bash
# 使用Qt Creator编译
# 查看调试输出中的样式设置
```

### 2. 视觉验证
- [ ] Timer列是否有黑色边框
- [ ] 空行是否显示白色背景
- [ ] 有数据行是否显示"--:--"
- [ ] 边框是否与其他列一致

### 3. 功能验证
- [ ] 添加新样本时Timer是否正确初始化
- [ ] 不同阶段的背景是否正确切换
- [ ] 文字颜色是否按需求显示

## 备用方案

如果当前修复不奏效，可以尝试：

### 方案A：使用QFrame替代QWidget
```cpp
class TimerWidget : public QFrame // 替代QWidget
{
    // QFrame有更好的边框支持
};
```

### 方案B：手动绘制边框
```cpp
void TimerWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setPen(QPen(Qt::black, 1));
    painter.drawRect(0, 0, width()-1, height()-1);
    // 手动绘制边框
}
```

### 方案C：使用嵌套Widget结构
```cpp
// 外层Widget：负责边框和背景
// 内层QLabel：负责文字显示
```

## 状态跟踪

- [x] 问题识别和分析
- [x] 多种修复方案尝试
- [x] 调试信息添加
- [ ] 编译测试验证
- [ ] 用户反馈确认

## 技术学习

通过这个问题学到：
1. Qt CSS优先级和冲突解决
2. QWidget边框显示的最佳实践
3. 自定义控件与父组件样式的协调 
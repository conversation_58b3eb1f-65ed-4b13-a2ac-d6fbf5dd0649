#include "qcmodulepage.h"
#include "database/storage/FileStorage.h"
#include <QDebug>

QCModulePage::QCModulePage(QWidget *parent) :
    QWidget(parent),
    clearDataButton(nullptr),
    titleLabel(nullptr),
    descriptionLabel(nullptr)
{
    setupUi();
}

QCModulePage::~QCModulePage()
{
    // UI components will be cleaned up automatically
}

void QCModulePage::setupUi()
{
    // Create main layout
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(50, 50, 50, 50);
    mainLayout->setSpacing(30);

    // Create title
    titleLabel = new QLabel("QC Module", this);
    titleLabel->setStyleSheet(
        "QLabel {"
        "    font-size: 24px;"
        "    font-weight: bold;"
        "    color: #333333;"
        "    text-align: center;"
        "}"
    );
    titleLabel->setAlignment(Qt::AlignCenter);

    // Create description label
    descriptionLabel = new QLabel("Temporary Data Cleanup Tool", this);
    descriptionLabel->setStyleSheet(
        "QLabel {"
        "    font-size: 16px;"
        "    color: #666666;"
        "    text-align: center;"
        "    margin: 20px 0;"
        "}"
    );
    descriptionLabel->setAlignment(Qt::AlignCenter);

    // Create clear data button
    clearDataButton = new QPushButton("Clear User Data", this);
    clearDataButton->setFixedSize(200, 60);
    clearDataButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #dc3545;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 8px;"
        "    font-size: 16px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #c82333;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #a71e2a;"
        "}"
    );

    // Connect signals and slots
    connect(clearDataButton, &QPushButton::clicked, this, &QCModulePage::onClearDataButtonClicked);

    // Layout components
    mainLayout->addWidget(titleLabel);
    mainLayout->addWidget(descriptionLabel);
    
    // Create button center layout
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(clearDataButton);
    buttonLayout->addStretch();
    
    mainLayout->addLayout(buttonLayout);
    mainLayout->addStretch();

    setLayout(mainLayout);
}

void QCModulePage::onClearDataButtonClicked()
{
    // Show confirmation dialog
    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(this, "Confirm Data Clear", 
                                 "Are you sure you want to clear all user data?\n\nThis operation will delete:\n• All patient information\n• All test results\n\nThis operation cannot be undone!",
                                 QMessageBox::Yes | QMessageBox::No,
                                 QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        clearUserData();
    }
}

void QCModulePage::clearUserData()
{
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        QMessageBox::warning(this, "Clear Failed", "Failed to initialize file storage!");
        return;
    }

    qDebug() << "Starting to clear user data...";

    // Clear all data using file storage
    if (!storage->clearAllData()) {
        QMessageBox::warning(this, "Clear Failed", "Failed to clear all data: " + storage->getLastError());
        return;
    }

    qDebug() << "User data cleared successfully";

    // Show success message
    QMessageBox::information(this, "Clear Complete",
                            "All user data has been successfully cleared.\n\nThe database has been reset to its initial state.");

    // 发射数据清空信号，通知所有页面刷新UI
    emit dataCleared();
    qDebug() << "Data cleared signal emitted to all pages";
} 
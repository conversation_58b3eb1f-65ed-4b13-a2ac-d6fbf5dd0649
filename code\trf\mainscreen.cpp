#include "MainScreen.h"
#include "ui_mainscreen.h"
#include <QTimer>
#include <QDateTime>
#include <QDebug>
#include <QEvent>

MainScreen::MainScreen(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::MainScreen)
{
    ui->setupUi(this);

    // --- Time Update Setup ---
    timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &MainScreen::updateDateTime);
    timer->start(1000); // Update every second
    updateDateTime(); // Initial call to set time immediately

    // --- Connect Buttons for Debugging ---
    connect(ui->button_auto_sample, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
    connect(ui->button_qc_module, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
    connect(ui->button_stat_sample, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
    connect(ui->button_database, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
    connect(ui->button_fast_mode, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
    connect(ui->button_settings, &QPushButton::clicked, this, &MainScreen::on_main_button_clicked);
}

MainScreen::~MainScreen()
{
    delete ui;
}

// Add implementations for the button getters
QPushButton *MainScreen::getAutoSampleButton()
{
    return ui->button_auto_sample;
}

QPushButton *MainScreen::getStatSampleButton()
{
    return ui->button_stat_sample;
}

QPushButton *MainScreen::getFastModeButton()
{
    return ui->button_fast_mode;
}

QPushButton *MainScreen::getQcModuleButton()
{
    return ui->button_qc_module;
}

QPushButton *MainScreen::getDatabaseButton()
{
    return ui->button_database;
}

QPushButton *MainScreen::getSettingsButton()
{
    return ui->button_settings;
}

void MainScreen::updateDateTime()
{
    QDateTime currentDateTime = QDateTime::currentDateTime();
    QString currentTime = currentDateTime.toString("hh:mm:ss");
    QString currentDate = currentDateTime.toString("yyyy-MM-dd");
    
    ui->label_time->setText(currentTime);
    ui->label_date->setText(currentDate);
}

void MainScreen::on_main_button_clicked()
{
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (button) {
        qDebug() << "Button clicked:" << button->text();
    }
}

bool MainScreen::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress) {
        QWidget* widget = qobject_cast<QWidget*>(obj);
        if (widget) {
            QString widgetName = widget->objectName();
            qDebug() << "Widget clicked:" << widgetName;
            
            // Handle different widget clicks
            if (widgetName == "widget_auto_sample") {
                qDebug() << "Auto Sample clicked";
            } else if (widgetName == "widget_qc_module") {
                qDebug() << "QC Module clicked";
            } else if (widgetName == "widget_stat_sample") {
                qDebug() << "STAT Sample clicked";
            } else if (widgetName == "widget_database") {
                qDebug() << "Database clicked";
            } else if (widgetName == "widget_fast_mode") {
                qDebug() << "Fast Mode clicked";
            } else if (widgetName == "widget_settings") {
                qDebug() << "Settings clicked";
            }
        }
    }
    return QWidget::eventFilter(obj, event);
} 

# 160901(1)_1_45_translate_20250526171032 - 第 8 部分

页面 36 - 40

---

## 第 36 页

HumaFIA |用户手册
38
4.4 参数列表选择
图4-4设置优先选择7个参数
在许多屏幕上，仅显示7个参数供选择。按下“ 其他” 按钮后，才能看到所有其他参数。此屏幕允许用户定义
参数的显示顺序。
Prio栏允许用户更改参数的显示顺序。1-7定义了第一页上的位置，其他所有位置都在其他页面上。使用键盘
将数字输入到选定的字段中。
个位数表示单个参数测试的优先级/位置。
如果参数是联合检测的一部分，则会显示两位数字。第一位数字表示该参数作为单独检测的位置，而第二位
数字则表示联合检测的位置。例如，如果Prio列显示cTnI为1.7，则单独的cTnI检测将显示在位置1，而联合
检测cTnI、Myo和CK-MB将出现在位置7（Myo和CK-MB这两个参数的第二位数字也将是7）。
对于每个参数，可以定义低和高截止值。在这两个值之间，结果被视为正常，并以黑色显示。如果结果较
高，则在测量屏幕上的结果旁边显示红色箭头。
低(L)表示患者显示异常低值的范围。此时，会出现一个红色向下箭头。
正常(N)表示患者健康或正常的参数范围。
高(H)表示患者显示异常高的值。在这种情况下，出现红色向上箭头。
按Page up/Page down可进入参数列表的其余部分。
按下“ 添加” 按钮后，会弹出如下界面，允许用户为上传的某个参数创建自定义参考范围。在下面的例子
中，创建了一个针对新生儿PCT的自定义参数。例如，新生儿根据年龄（以小时计）有不同的参考范围。这
里，创建了12小时大的新生儿的参考范围。选择“ Card名称” 和“ 参数” 为PCT。名称为


---

## 第 37 页

HumaFIA |用户手册
39
新参数为neo12h，可任意选择名称，然后将正常值范围设置为0.5至8.0 ng/ml，在弹出窗口中点击添加，创
建具有自定义参考范围的新参数。
最后一步是为新参数定义优先级。
图4-5添加自定义参考范围
在图4-5中，为PCT添加了一个新的参考范围。新参考范围用neo12h表示。在参数列表中，现在可以找到PCT_
neo12h。如果使用此参数（代替PCT)，则结果标记将采用新的参考范围。定义了PCT的新低(L)阈值和高(H)阈
值：
分别为0.5和8 ng/ml。
Add将自定义参考范围添加到新参数中，例如示例中的PCT_neo12h。
取消将关闭窗口，但不会应用任何更改。
回到设置屏幕，Del Mode允许用户通过按下“ 备注” 字段中的相应行来删除任何参数校准信息。
退出将应用所选设置并关闭窗口。


---

## 第 38 页

HumaFIA |用户手册
40
4.5 启用标志的设置
图4-6设置选项启用标志
结果数据中显示的所有标志都可以通过启用或禁用标志来设置为显示或隐藏。
如果激活了孵育时间不正确（长IL，短IS)的标志，则会显示IS和IL标志。如果这些标志出现，则由于孵育时
间不正确，必须重复测量。
红色箭头标志表示结果超出正常范围，如果激活“ 异常样本” 、“ 红色箭头上下” ，则显示该结果。
如果激活了“ 低、正常、高” 分类样本结果，则显示L、N和H标志。
如果激活了“ 测试卡／质控品过期” ，则显示Tx和Cx标志，表示测试批次（Tx）或质控品批次（Cx）已过
期。
如果激活了“ 控制超出范围（过低或过高）” ，则显示CL和CH标志。这表示控制结果超出目标值范围。
如果超出线性范围，但显示结果被激活，则会显示LH和LL标志。在这种情况下，即使结果超出线性范围，结
果仍然会在结果屏幕上显示。如果不启用，超出线性范围的结果不会显示。
退出将应用所选设置并关闭窗口。


---

## 第 39 页

HumaFIA |用户手册
41
4.6 常规（2）设置、软件更新、自动警告
注意：此设置仅在服务模式下可访问。请先从用户或管理员模式切换到服务模式。
图4-7运行软件更新并启用警告
失效品盒/测试
如果启用“ cart./test过期时停止操作” 设置，则软件将停止过期测试批次的操作。这保证了仅使用有效试
剂，从而获得正确结果。
如果设置Flag结果，如果已启用“ 测试盒已过期” （Tx-flag），则如果正在使用的测试批次已过期，则将结
果标记为Tx。
有效期控制
如果设置的Flag结果为“ 如果控制已过期（Cx-flag）启用” ，则如果正在使用的控制LOT过期，则用Cx标记
控制结果。
更新软件
必须将新软件版本传输到U盘并导入系统。如果接受，U盘会立即开始闪烁。按“ 更新” 按钮，软件更新将自
动从U盘上传。
注：只能使用串行连接端口旁边的USB端口来上传软件更新。
显示软件版本打开一个屏幕，显示系统上运行的当前软件版本。
退出将应用选定的设置并关闭窗口。


---

## 第 40 页

HumaFIA |用户手册
42
4.7 数据库中数据字段的选择
图4-8自定义数据库模块中数据字段的显示
数据库容量较大，存储了大量数据字段。此设置页面允许用户通过启用（?）或禁用(x)相应数据字段的选
项，自定义数据库中显示的内容或打印内容。
退出将应用选定的设置并关闭窗口。
4.8 建立连接通道的设置
注：此设置仅在服务模式下可访问。请先从用户或管理员模式切换到服务模式。
图4-9设置以太网连接及其他数据端口
要连接到internet，请添加IP地址、子网掩码、PC IP地址和PC端口信息。按每个字段以输入值。


---


#!/bin/bash

# TRF 显示问题诊断脚本
# 用于检查和解决framebuffer显示冲突

echo "========================================"
echo "TRF 显示问题诊断工具"
echo "========================================"
echo ""

echo "🔍 检查系统信息..."
echo "系统: $(uname -a)"
echo "主机名: $(hostname)"
echo ""

echo "🔍 检查framebuffer设备..."
if [ -c /dev/fb0 ]; then
    echo "✅ /dev/fb0 存在"
    ls -l /dev/fb0
    
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        FB_SIZE=$(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null || echo "unknown")
        echo "  虚拟尺寸: $FB_SIZE"
    fi
    
    if [ -r /sys/class/graphics/fb0/name ]; then
        FB_NAME=$(cat /sys/class/graphics/fb0/name 2>/dev/null || echo "unknown")
        echo "  设备名称: $FB_NAME"
    fi
else
    echo "❌ /dev/fb0 不存在"
fi
echo ""

echo "🔍 检查触摸设备..."
for event in /dev/input/event*; do
    if [ -c "$event" ]; then
        echo "✅ $event 存在"
        ls -l "$event"
    fi
done
echo ""

echo "🔍 检查可能占用显示的进程..."
DISPLAY_PROCESSES=$(ps aux | grep -E "(qt|Qt|GUI|gui|X11|wayland|fb|trf)" | grep -v grep | grep -v "$$" | grep -v "check_display")
if [ -n "$DISPLAY_PROCESSES" ]; then
    echo "⚠ 发现可能占用显示的进程:"
    echo "$DISPLAY_PROCESSES"
    echo ""
    echo "💡 建议操作:"
    echo "1. 确认哪些进程可以安全关闭"
    echo "2. 使用 kill <PID> 关闭不需要的进程"
    echo "3. 或者使用 ./run.sh --clear-display 自动清理"
else
    echo "✅ 未发现明显的显示设备占用"
fi
echo ""

echo "🔍 检查Qt环境..."
if command -v qmake >/dev/null 2>&1; then
    echo "✅ Qt开发环境可用"
    qmake -v 2>/dev/null || echo "  qmake版本信息获取失败"
else
    echo "ℹ Qt开发环境不可用（运行时不需要）"
fi

# 检查Qt库
echo "🔍 检查Qt运行时库..."
QT_LIBS=$(ldconfig -p | grep -i qt5 | wc -l)
if [ "$QT_LIBS" -gt 0 ]; then
    echo "✅ 发现 $QT_LIBS 个Qt5库"
    echo "主要Qt5库:"
    ldconfig -p | grep -E "libQt5(Core|Gui|Widgets)" | head -3
else
    echo "⚠ 未发现Qt5库"
fi
echo ""

echo "🔍 检查TRF程序..."
if [ -f "./trf" ]; then
    echo "✅ TRF可执行文件存在"
    ls -l ./trf
    
    if [ -x "./trf" ]; then
        echo "✅ TRF具有执行权限"
    else
        echo "⚠ TRF缺少执行权限，正在修复..."
        chmod +x ./trf
        echo "✅ 执行权限已修复"
    fi
else
    echo "❌ TRF可执行文件不存在"
fi
echo ""

echo "🔍 检查TRF资源文件..."
RESOURCE_COUNT=0
for dir in images styles plugins data; do
    if [ -d "./$dir" ]; then
        echo "✅ $dir 目录存在"
        RESOURCE_COUNT=$((RESOURCE_COUNT + 1))
    else
        echo "⚠ $dir 目录不存在"
    fi
done

if [ $RESOURCE_COUNT -ge 3 ]; then
    echo "✅ 资源文件检查通过"
else
    echo "⚠ 部分资源文件缺失"
fi
echo ""

echo "========================================"
echo "诊断完成"
echo "========================================"
echo ""

if [ -c /dev/fb0 ] && [ -f "./trf" ] && [ $RESOURCE_COUNT -ge 3 ]; then
    echo "✅ 基本环境检查通过"
    echo ""
    echo "🚀 建议的启动步骤:"
    echo "1. 如果有其他图形程序在运行，请先关闭"
    echo "2. 运行: ./run.sh"
    echo "3. 如果界面不显示，运行: ./run.sh --clear-display"
    echo "4. 如果问题持续，检查上面列出的进程冲突"
else
    echo "❌ 环境检查发现问题"
    echo ""
    echo "🔧 需要解决的问题:"
    if [ ! -c /dev/fb0 ]; then
        echo "- framebuffer设备 /dev/fb0 不可用"
    fi
    if [ ! -f "./trf" ]; then
        echo "- TRF可执行文件缺失"
    fi
    if [ $RESOURCE_COUNT -lt 3 ]; then
        echo "- 资源文件不完整"
    fi
fi

echo ""
echo "========================================"

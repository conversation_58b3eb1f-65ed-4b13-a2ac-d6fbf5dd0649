<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AddSamplePage</class>
 <widget class="QWidget" name="AddSamplePage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>475</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="widget_header_patient" native="true">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>5</y>
     <width>190</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_patient">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/patient_id.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_patient">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Patient, ID</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_parameter" native="true">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>5</y>
     <width>152</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_parameter">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>152</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/parameter.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_parameter">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>152</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Parameter</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_result" native="true">
   <property name="geometry">
    <rect>
     <x>352</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_result">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/result.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_result">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Result</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_datetime" native="true">
   <property name="geometry">
    <rect>
     <x>466</x>
     <y>5</y>
     <width>190</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_datetime">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/date_time.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_datetime">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Date&amp;sample type</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_timer" native="true">
   <property name="geometry">
    <rect>
     <x>656</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_timer">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/timer.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_timer">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Timer</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_lot" native="true">
   <property name="geometry">
    <rect>
     <x>770</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_lot">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/lot.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_lot">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Lot</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_header_cutoff" native="true">
   <property name="geometry">
    <rect>
     <x>884</x>
     <y>5</y>
     <width>138</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_cutoff">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>138</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/cut_off.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_cutoff">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>138</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
    </property>
    <property name="text">
     <string>Cut-off</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignmentFlag::AlignCenter</set>
    </property>
   </widget>
  </widget>
  <widget class="QGroupBox" name="groupBox_patient_data">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>80</y>
     <width>181</width>
     <height>381</height>
    </rect>
   </property>
   <property name="title">
    <string/>
   </property>
   <widget class="QLineEdit" name="lineEdit_last_name">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>50</y>
      <width>151</width>
      <height>41</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>Last name</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_name">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>110</y>
      <width>151</width>
      <height>41</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>Name</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_birthday">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>170</y>
      <width>151</width>
      <height>41</height>
     </rect>
    </property>
    <property name="placeholderText">
     <string>Birthday</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_gender">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>240</y>
      <width>81</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>Gender</string>
    </property>
   </widget>
   <widget class="QComboBox" name="comboBox_gender">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>270</y>
      <width>151</width>
      <height>41</height>
     </rect>
    </property>
   </widget>
   <widget class="QWidget" name="widget_label_patient_id" native="true">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>10</y>
      <width>151</width>
      <height>31</height>
     </rect>
    </property>
    <widget class="QLabel" name="bg_label_patient_id">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>151</width>
       <height>31</height>
      </rect>
     </property>
     <property name="pixmap">
      <pixmap resource="resources.qrc">:/images/add_sample/patient_id.png</pixmap>
     </property>
     <property name="scaledContents">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QLabel" name="label_patient_id">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>151</width>
       <height>31</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: none; padding: 4px;</string>
     </property>
     <property name="text">
      <string>Patient id</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QLabel" name="label_pre_dil">
   <property name="geometry">
    <rect>
     <x>210</x>
     <y>80</y>
     <width>84</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Pre-Dil.</string>
   </property>
  </widget>
  <widget class="QWidget" name="predil_widget" native="true">
   <property name="geometry">
    <rect>
     <x>210</x>
     <y>170</y>
     <width>93</width>
     <height>140</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="predilLayout">
    <property name="spacing">
     <number>8</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QPushButton" name="pushButton_yes">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>Yes</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_no">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>No</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="label_sample">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>80</y>
     <width>91</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Sample</string>
   </property>
  </widget>
  <widget class="QWidget" name="sample_widget" native="true">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>170</y>
     <width>93</width>
     <height>301</height>
    </rect>
   </property>
   <layout class="QVBoxLayout" name="sampleLayout">
    <property name="spacing">
     <number>8</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QPushButton" name="pushButton_blood">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>Blood</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_serum">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>Serum</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_plasma">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>Plasma</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pushButton_capillary">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>Capillary
blood</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="widget_label_parameter" native="true">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>80</y>
     <width>191</width>
     <height>31</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_parameter">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>191</width>
      <height>31</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/parameter.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_parameter">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>191</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>10</pointsize>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: none; padding: 4px;</string>
    </property>
    <property name="text">
     <string>Parameter</string>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="parameter_widget" native="true">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>170</y>
     <width>189</width>
     <height>301</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="parameterLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>8</number>
    </property>
    <item row="0" column="0">
     <widget class="QPushButton" name="pushButton_hscrp">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>hs-CRP/C
RP</string>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QPushButton" name="pushButton_pct">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>PCT</string>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QPushButton" name="pushButton_ctni">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>cTnI</string>
      </property>
     </widget>
    </item>
    <item row="3" column="0">
     <widget class="QPushButton" name="pushButton_empty1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QPushButton" name="pushButton_empty_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="pushButton_empty_3">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
    <item row="2" column="1">
     <widget class="QPushButton" name="pushButton_empty_4">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </item>
    <item row="3" column="1">
     <widget class="QWidget" name="widget_other" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <widget class="QLabel" name="bg_label_other">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>84</width>
         <height>66</height>
        </rect>
       </property>
       <property name="pixmap">
        <pixmap resource="resources.qrc">:/images/add_sample/other.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_other">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>84</width>
         <height>66</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton { background-color: transparent; border: none; color: black; font-weight: bold; font-size: 10pt; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
       </property>
       <property name="text">
        <string>Other</string>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="widget_label_add_id" native="true">
   <property name="geometry">
    <rect>
     <x>640</x>
     <y>80</y>
     <width>261</width>
     <height>31</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_add_id">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>261</width>
      <height>31</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/patient_id.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_add_id">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>261</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>10</pointsize>
      <bold>true</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: transparent; border: none; padding: 4px;</string>
    </property>
    <property name="text">
     <string>Add ID</string>
    </property>
   </widget>
  </widget>
  <widget class="QLineEdit" name="lineEdit_add_id">
   <property name="geometry">
    <rect>
     <x>640</x>
     <y>130</y>
     <width>261</width>
     <height>39</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
  <widget class="QWidget" name="keyboard_widget" native="true">
   <property name="geometry">
    <rect>
     <x>630</x>
     <y>170</y>
     <width>281</width>
     <height>301</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <property name="spacing">
     <number>8</number>
    </property>
    <item row="0" column="0">
     <widget class="QPushButton" name="pushButton_1">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>1</string>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QPushButton" name="pushButton_2">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>2</string>
      </property>
     </widget>
    </item>
    <item row="0" column="2">
     <widget class="QPushButton" name="pushButton_3">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>3</string>
      </property>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QPushButton" name="pushButton_4">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>4</string>
      </property>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QPushButton" name="pushButton_5">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>5</string>
      </property>
     </widget>
    </item>
    <item row="1" column="2">
     <widget class="QPushButton" name="pushButton_6">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>6</string>
      </property>
     </widget>
    </item>
    <item row="2" column="0">
     <widget class="QPushButton" name="pushButton_7">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>7</string>
      </property>
     </widget>
    </item>
    <item row="2" column="1">
     <widget class="QPushButton" name="pushButton_8">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>8</string>
      </property>
     </widget>
    </item>
    <item row="2" column="2">
     <widget class="QPushButton" name="pushButton_9">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>9</string>
      </property>
     </widget>
    </item>
    <item row="3" column="0">
     <widget class="QPushButton" name="pushButton_0">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>84</width>
        <height>66</height>
       </size>
      </property>
      <property name="text">
       <string>0</string>
      </property>
     </widget>
    </item>
    <item row="3" column="2">
     <widget class="QWidget" name="widget_exit" native="true">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <widget class="QLabel" name="bg_label_exit">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>91</width>
         <height>73</height>
        </rect>
       </property>
       <property name="pixmap">
        <pixmap resource="resources.qrc">:/images/add_sample/exit.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_exit">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>91</width>
         <height>73</height>
        </rect>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton { background-color: transparent; border: none; color: black; font-weight: bold; font-size: 10pt; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
       </property>
       <property name="text">
        <string>Exit</string>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="widget_cancel" native="true">
   <property name="geometry">
    <rect>
     <x>930</x>
     <y>150</y>
     <width>84</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_cancel">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/cancel.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="button_cancel">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton { background-color: transparent; border: none; color: black; font-weight: bold; font-size: 9pt; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
    </property>
    <property name="text">
     <string>Cancel</string>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_delete" native="true">
   <property name="geometry">
    <rect>
     <x>930</x>
     <y>250</y>
     <width>84</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_delete">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/delete.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="button_delete">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton { background-color: transparent; border: none; color: black; font-weight: bold; font-size: 9pt; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
    </property>
    <property name="text">
     <string>Delete</string>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_insert" native="true">
   <property name="geometry">
    <rect>
     <x>930</x>
     <y>350</y>
     <width>84</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_insert">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/insert.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="button_insert">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>84</width>
      <height>66</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton { background-color: transparent; border: none; color: black; font-weight: bold; font-size: 9pt; padding: 1px; text-align: center; margin: 2px; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
    </property>
    <property name="text">
     <string>Insert cartridge for incubation</string>
    </property>
   </widget>
  </widget>
  <widget class="QWidget" name="widget_add_sample" native="true">
   <property name="geometry">
    <rect>
     <x>930</x>
     <y>80</y>
     <width>81</width>
     <height>51</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_add_sample">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>81</width>
      <height>51</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/add_sample/add_sample.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="pushButton_add_sample">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>81</width>
      <height>51</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton { background-color: transparent; border: none; } QPushButton:hover { background-color: rgba(255, 255, 255, 50); } QPushButton:pressed { background-color: rgba(0, 0, 0, 50); }</string>
    </property>
    <property name="text">
     <string/>
    </property>
   </widget>
  </widget>
  <widget class="QLineEdit" name="lineEdit_yes_or_no">
   <property name="geometry">
    <rect>
     <x>210</x>
     <y>130</y>
     <width>93</width>
     <height>39</height>
    </rect>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
   <property name="placeholderText">
    <string>No</string>
   </property>
  </widget>
  <widget class="QTextEdit" name="lineEdit_sample">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>130</y>
     <width>93</width>
     <height>41</height>
    </rect>
   </property>
   <property name="verticalScrollBarPolicy">
    <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
   </property>
   <property name="horizontalScrollBarPolicy">
    <enum>Qt::ScrollBarPolicy::ScrollBarAlwaysOff</enum>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
   <property name="placeholderText">
    <string>serum</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="lineEdit_parameter">
   <property name="geometry">
    <rect>
     <x>440</x>
     <y>130</y>
     <width>189</width>
     <height>39</height>
    </rect>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
   <property name="placeholderText">
    <string>PCT</string>
   </property>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>

#!/bin/bash

# TRF SQLite 驱动部署脚本
# 用于在 myd-y6ull14x14 系统上部署 SQLite 驱动

echo "=== TRF SQLite 驱动部署工具 ==="
echo "适用于 myd-y6ull14x14 最小系统"
echo "目标：解决 'QSQLITE driver not loaded' 问题"
echo "========================================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 <TRF应用目录路径>"
    echo "示例: $0 /mnt/sd/TRF-1.0.0.26-Linux-ARM-v7l"
    exit 1
fi

APP_DIR="$1"

# 验证应用目录
if [ ! -d "$APP_DIR" ]; then
    echo "❌ 错误：应用目录不存在: $APP_DIR"
    exit 1
fi

if [ ! -f "$APP_DIR/trf" ]; then
    echo "❌ 错误：未找到 trf 可执行文件在: $APP_DIR"
    exit 1
fi

echo "应用目录: $APP_DIR"
echo ""

# 步骤1：检查系统 SQLite 驱动
echo "=== 步骤1：检查系统 SQLite 驱动 ==="

SYSTEM_DRIVER_FOUND=false
SYSTEM_DRIVER_PATH=""

# 检查可能的 SQLite 驱动位置
POSSIBLE_LOCATIONS=(
    "/usr/lib/qt5/plugins/sqldrivers/libqsqlite.so"
    "/usr/lib/arm-linux-gnueabihf/qt5/plugins/sqldrivers/libqsqlite.so"
    "/usr/lib/plugins/sqldrivers/libqsqlite.so"
    "/lib/qt5/plugins/sqldrivers/libqsqlite.so"
)

for location in "${POSSIBLE_LOCATIONS[@]}"; do
    if [ -f "$location" ]; then
        echo "✓ 找到系统 SQLite 驱动: $location"
        SYSTEM_DRIVER_FOUND=true
        SYSTEM_DRIVER_PATH="$location"
        break
    fi
done

if [ "$SYSTEM_DRIVER_FOUND" = false ]; then
    echo "⚠ 标准位置未找到 SQLite 驱动，尝试搜索..."
    
    # 搜索所有可能的 SQLite 驱动
    FOUND_DRIVERS=$(find /usr /lib -name "*sqlite*.so" -type f 2>/dev/null | head -5)
    
    if [ -n "$FOUND_DRIVERS" ]; then
        echo "找到可能的 SQLite 相关库："
        echo "$FOUND_DRIVERS"
        
        # 选择第一个看起来像 Qt SQLite 驱动的文件
        for driver in $FOUND_DRIVERS; do
            if echo "$driver" | grep -q "qsqlite\|qt.*sqlite"; then
                SYSTEM_DRIVER_PATH="$driver"
                SYSTEM_DRIVER_FOUND=true
                echo "✓ 将使用: $SYSTEM_DRIVER_PATH"
                break
            fi
        done
    fi
fi

echo ""

# 步骤2：检查应用目录中的驱动
echo "=== 步骤2：检查应用插件目录 ==="

if [ -d "$APP_DIR/plugins/sqldrivers" ]; then
    echo "✓ 插件目录已存在: $APP_DIR/plugins/sqldrivers"
    
    if find "$APP_DIR/plugins/sqldrivers" -name "*.so" | grep -q .; then
        echo "✓ 发现现有驱动文件："
        find "$APP_DIR/plugins/sqldrivers" -name "*.so" -exec basename {} \;
    else
        echo "ℹ 插件目录为空"
    fi
else
    echo "ℹ 创建插件目录: $APP_DIR/plugins/sqldrivers"
    mkdir -p "$APP_DIR/plugins/sqldrivers"
fi

echo ""

# 步骤3：部署 SQLite 驱动
echo "=== 步骤3：部署 SQLite 驱动 ==="

if [ "$SYSTEM_DRIVER_FOUND" = true ]; then
    echo "正在复制系统 SQLite 驱动..."
    
    if cp "$SYSTEM_DRIVER_PATH" "$APP_DIR/plugins/sqldrivers/"; then
        echo "✓ SQLite 驱动已成功复制"
        echo "  源文件: $SYSTEM_DRIVER_PATH"
        echo "  目标: $APP_DIR/plugins/sqldrivers/$(basename "$SYSTEM_DRIVER_PATH")"
    else
        echo "❌ 复制失败，请检查权限"
        exit 1
    fi
else
    echo "❌ 未找到可用的 SQLite 驱动"
    echo ""
    echo "解决方案："
    echo "1. 检查系统是否安装了 Qt5 SQL 模块"
    echo "2. 从发布包中获取预编译的 SQLite 驱动"
    echo "3. 手动提供兼容的 libqsqlite.so 文件"
    exit 1
fi

echo ""

# 步骤4：创建优化启动脚本
echo "=== 步骤4：创建优化启动脚本 ==="

cat > "$APP_DIR/run_with_sqlite.sh" << 'EOF'
#!/bin/bash

# TRF 启动脚本（包含 SQLite 驱动支持）
echo "TRF 启动 (SQLite 驱动支持) - $(date)"
echo "====================================="

APP_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$APP_DIR"

# 检查 trf 可执行文件
if [ ! -f "./trf" ]; then
    echo "❌ 错误：未找到 trf 可执行文件"
    exit 1
fi

chmod +x ./trf

# 配置 Qt 插件路径（包含本地 SQLite 驱动）
export QT_PLUGIN_PATH="$APP_DIR/plugins:/usr/lib/qt5/plugins:/usr/lib/arm-linux-gnueabihf/qt5/plugins"
export QT_SQL_DRIVERS="$APP_DIR/plugins/sqldrivers"

# 验证 SQLite 驱动
if find "$APP_DIR/plugins/sqldrivers" -name "*.so" | grep -q .; then
    echo "✓ SQLite 驱动已加载"
    echo "  插件路径: $QT_PLUGIN_PATH"
    echo "  SQL 驱动: $QT_SQL_DRIVERS"
else
    echo "⚠ SQLite 驱动未找到，将使用系统默认路径"
fi

# 系统配置
export LC_ALL=C
export LANG=C

# 硬件配置（myd-y6ull14x14）
export QT_QPA_PLATFORM="linuxfb:fb=/dev/fb0"

# 触摸屏配置
if [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
    echo "✓ 触摸屏: /dev/input/event1"
elif [ -c /dev/input/event0 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event0"
    echo "✓ 触摸屏: /dev/input/event0"
fi

echo ""
echo "启动 TRF 应用..."
exec ./trf "$@"
EOF

chmod +x "$APP_DIR/run_with_sqlite.sh"
echo "✓ 创建启动脚本: $APP_DIR/run_with_sqlite.sh"

echo ""

# 步骤5：验证部署
echo "=== 步骤5：验证部署结果 ==="

echo "SQLite 驱动文件："
find "$APP_DIR/plugins" -name "*.so" | while read file; do
    echo "  $(basename "$file") ($(stat -c%s "$file") bytes)"
done

echo ""
echo "=== 部署完成 ==="
echo "✓ SQLite 驱动已部署到: $APP_DIR/plugins/sqldrivers/"
echo "✓ 优化启动脚本已创建: $APP_DIR/run_with_sqlite.sh"
echo ""
echo "启动应用："
echo "cd $APP_DIR && ./run_with_sqlite.sh"
echo ""
echo "如果仍然遇到问题，请检查："
echo "1. 驱动文件权限: chmod 755 $APP_DIR/plugins/sqldrivers/*.so"
echo "2. 系统兼容性: ./check.sh"
echo "3. 启动日志获取更多信息" 
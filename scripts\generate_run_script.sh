#!/bin/bash

# 生成TRF运行脚本
# 此脚本由GitHub Actions调用，生成最终的run.sh

PACKAGE_NAME="$1"
TARGET_ARCH="$2"
TARGET_KERNEL="$3"
TARGET_HOSTNAME="$4"
QT_VERSION_ARM="$5"

cat > run.sh << 'SCRIPT_EOF'
#!/bin/bash

# TRF 文件存储系统启动脚本 - 完整解决方案
# 自动生成于GitHub Actions构建过程

echo "TRF 文件存储系统启动 - 完整解决方案"
echo "=============================================="
echo "目标系统: Linux PLACEHOLDER_HOSTNAME PLACEHOLDER_KERNEL PLACEHOLDER_ARCH Qt PLACEHOLDER_QT_VERSION"
echo "解决问题: qt_resourceFeatureZlib符号版本 + 资源系统初始化"
echo "数据存储: 文件存储系统(JSON) - 无需SQLite驱动"
echo "资源修复: Q_INIT_RESOURCE + 优化编译配置"
echo "显示修复: framebuffer冲突检测 + 自动清理选项"
echo ""

# 帮助信息
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  echo "用法: $0 [选项]"
  echo ""
  echo "选项:"
  echo "  --low-memory, -m       启用低内存模式（推荐用于内存不足）"
  echo "  --clear-display, -c    清理显示设备占用（如果界面不显示）"
  echo "  --help, -h            显示此帮助信息"
  echo ""
  echo "说明:"
  echo "  如果程序因内存不足被杀死，请使用 -m 选项启用低内存模式"
  echo "  如果TRF界面不显示，可能是其他程序占用了framebuffer设备"
  echo "  请先关闭其他图形程序，或使用 -c 选项自动清理"
  exit 0
fi

# 验证资源系统修复
echo "🔍 验证资源系统修复..."
RESOURCE_CHECKS=0

# 检查1: 资源文件路径引用
if grep -r ":/images/" . >/dev/null 2>&1 || grep -r ":/styles/" . >/dev/null 2>&1; then
  echo "✓ 发现资源文件路径引用"
  RESOURCE_CHECKS=$((RESOURCE_CHECKS + 1))
fi

# 检查2: Q_INIT_RESOURCE调用
if grep -r "Q_INIT_RESOURCE" . >/dev/null 2>&1; then
  echo "✓ 发现Q_INIT_RESOURCE调用"
  RESOURCE_CHECKS=$((RESOURCE_CHECKS + 1))
fi

# 检查3: 资源编译配置
if grep -r "resources.qrc" . >/dev/null 2>&1; then
  echo "✓ 发现资源编译配置"
  RESOURCE_CHECKS=$((RESOURCE_CHECKS + 1))
fi

if [ $RESOURCE_CHECKS -gt 0 ]; then
  echo "⚠ 资源系统修复可能已应用 (通过 $RESOURCE_CHECKS/3 项检查)"
else
  echo "❌ 未检测到资源系统修复，可能存在问题"
fi

# 自动配置系统环境
echo "🔧 自动配置系统环境..."

# 检测目标系统类型
if [ -f "/proc/device-tree/model" ]; then
  DEVICE_MODEL=$(cat /proc/device-tree/model 2>/dev/null | tr -d '\0')
  if [[ "$DEVICE_MODEL" == *"myd-y6ull14x14"* ]] || [[ "$(hostname)" == *"myd-y6ull14x14"* ]]; then
    echo "🎯 检测到 myd-y6ull14x14 开发板，启用特定优化"
    
    # myd-y6ull14x14 特定配置
    export QT_QPA_PLATFORM="linuxfb:fb=/dev/fb0"
    export QT_QPA_FB_DISABLE_INPUT="0"
    export QT_QPA_FB_FORCE_FULLSCREEN="1"
    export QT_QPA_FB_DRM="1"
    export QT_QPA_FB_HIDECURSOR="1"
    
    # 触摸屏配置
    if [ -c "/dev/input/event1" ]; then
      export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
      echo "  ✓ 配置iMX6ULL触摸屏: /dev/input/event1"
    fi
    
    echo "  ✓ 使用framebuffer设备: /dev/fb0"
    echo "  ✓ 强制全屏显示已启用"
    echo "  ✓ 显示修复配置已应用（隐藏光标、设备权限）"
    echo "  ✓ myd-y6ull14x14 专用配置已启用"
  fi
fi

# 通用ARM系统配置
if [ "$(uname -m)" = "armv7l" ] || [ "$(uname -m)" = "aarch64" ]; then
  # 检测触摸设备
  for event_device in /dev/input/event*; do
    if [ -c "$event_device" ]; then
      # 检查是否为触摸设备
      if grep -q "iMX6UL" /proc/bus/input/devices 2>/dev/null; then
        export QT_QPA_GENERIC_PLUGINS="evdevtouch:$event_device"
        echo "✓ 检测到 iMX6UL 触摸控制器: $event_device"
        break
      fi
    fi
  done
  
  # 设置区域设置（避免警告）
  export LC_ALL=C
  export LANG=C
fi

# 设置Qt插件路径
CURRENT_DIR="$(pwd)"
SYSTEM_QT_PLUGINS="/usr/lib/qt5/plugins"

if [ -d "$SYSTEM_QT_PLUGINS" ]; then
  echo "  ✓ 发现系统Qt插件路径: $SYSTEM_QT_PLUGINS"
fi

# 混合适配Qt插件配置
echo "✓ 已加载 system.txt 混合适配的 Qt 插件"
echo "  平台插件: $CURRENT_DIR/plugins/platforms (应用版本)"
echo "  SQLite 驱动: 系统版本优先"
echo "  插件搜索路径: 系统优先，应用补充"

# 设置Qt插件搜索路径（系统优先）
if [ -d "$SYSTEM_QT_PLUGINS" ] && [ -d "$CURRENT_DIR/plugins/platforms" ]; then
  export QT_PLUGIN_PATH="$CURRENT_DIR/plugins/platforms:$SYSTEM_QT_PLUGINS:$CURRENT_DIR/plugins:$CURRENT_DIR"
elif [ -d "$SYSTEM_QT_PLUGINS" ]; then
  export QT_PLUGIN_PATH="$SYSTEM_QT_PLUGINS:$CURRENT_DIR/plugins:$CURRENT_DIR"
elif [ -d "$CURRENT_DIR/plugins" ]; then
  export QT_PLUGIN_PATH="$CURRENT_DIR/plugins:$CURRENT_DIR"
fi

# 验证Qt插件
echo "🔍 验证 Qt 插件..."
if [ -f "$CURRENT_DIR/plugins/platforms/libqlinuxfb.so" ] || [ -f "$SYSTEM_QT_PLUGINS/platforms/libqlinuxfb.so" ]; then
  echo "✓ LinuxFB 平台插件已找到"
else
  echo "⚠ LinuxFB 平台插件未找到，可能影响显示"
fi

# 数据存储配置
echo "🔍 数据存储配置..."
echo "  TRF数据存储: 文件存储系统(JSON)"
echo "  → 不再依赖SQLite数据库"
echo "  → 数据存储在./data目录下"
echo "  → 配置文件、患者数据、测试结果均为JSON格式"
echo "  ✓ 使用文件存储系统，无需数据库驱动"

# 确保数据目录存在
DATA_DIR="$CURRENT_DIR/data"
PLUGINS_DIR="$CURRENT_DIR/plugins/platforms"

echo "  ✓ 数据目录: $DATA_DIR"
echo "  ✓ 平台插件路径: $PLUGINS_DIR"

if [ ! -d "$DATA_DIR" ]; then
  mkdir -p "$DATA_DIR"
  echo "  ✅ 数据目录已创建"
else
  echo "  ✅ 数据目录已存在"
fi

# 配置数据存储路径
echo "🗂️ 配置数据存储路径"
echo "  → 主数据目录: $DATA_DIR"
echo "  → 患者数据: $DATA_DIR/patients.json"
echo "  → 测试结果: $DATA_DIR/test_results.json"
echo "  → 系统配置: $DATA_DIR/config.json"

# 显示配置验证
echo "🔍 显示配置验证..."
echo "  Qt平台: ${QT_QPA_PLATFORM:-默认}"
echo "  强制全屏: ${QT_QPA_FB_FORCE_FULLSCREEN:-0}"
echo "  隐藏光标: ${QT_QPA_FB_HIDECURSOR:-0}"
echo "  输入设备: ${QT_QPA_FB_DISABLE_INPUT:-1}"

# 检查framebuffer设备
if [ -c "/dev/fb0" ]; then
  echo "  ✅ Framebuffer设备: /dev/fb0 可用"
  
  # 获取framebuffer信息
  if command -v fbset >/dev/null 2>&1; then
    FB_INFO=$(fbset 2>/dev/null | grep "geometry" | head -1)
    if [ -n "$FB_INFO" ]; then
      echo "  ℹ Framebuffer几何: $FB_INFO"
    fi
  elif [ -r "/sys/class/graphics/fb0/virtual_size" ]; then
    FB_SIZE=$(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)
    echo "  ℹ Framebuffer虚拟尺寸: $FB_SIZE"
  fi
else
  echo "  ⚠ Framebuffer设备: /dev/fb0 不可用"
fi

# 检查触摸设备
if [ -c "/dev/input/event1" ]; then
  echo "  ✅ 触摸设备: /dev/input/event1 可用"
else
  echo "  ⚠ 触摸设备: /dev/input/event1 不可用"
fi

# 检查显示设备占用情况
echo "🔍 检查显示设备占用情况..."
DISPLAY_PROCESSES=$(ps aux | grep -E "(qt|Qt|GUI|gui|X11|wayland|fb)" | grep -v grep | grep -v "$0" || true)
if [ -n "$DISPLAY_PROCESSES" ]; then
  echo "  ⚠ 发现可能占用显示的进程:"
  echo "$DISPLAY_PROCESSES"
  echo "  💡 提示: 如果TRF界面不显示，请先关闭其他图形程序"
else
  echo "  ✅ 未发现明显的显示设备占用"
fi

SCRIPT_EOF

# 替换占位符
sed -i "s/PLACEHOLDER_HOSTNAME/$TARGET_HOSTNAME/g" run.sh
sed -i "s/PLACEHOLDER_KERNEL/$TARGET_KERNEL/g" run.sh
sed -i "s/PLACEHOLDER_ARCH/$TARGET_ARCH/g" run.sh
sed -i "s/PLACEHOLDER_QT_VERSION/$QT_VERSION_ARM/g" run.sh

# 添加内存优化和启动逻辑
cat >> run.sh << 'SCRIPT_EOF2'

# 内存优化选项
if [ "$1" = "--low-memory" ] || [ "$1" = "-m" ]; then
  echo "🧠 启用激进低内存模式..."

  # 清理系统内存
  echo "  🧹 清理系统内存..."
  sync
  echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true

  # 创建更大的swap空间（如果不存在）
  if [ ! -f /mnt/sd/swapfile ]; then
    echo "  📁 创建大容量swap文件 (512MB)..."
    dd if=/dev/zero of=/mnt/sd/swapfile bs=1M count=512 2>/dev/null
    chmod 600 /mnt/sd/swapfile
    mkswap /mnt/sd/swapfile 2>/dev/null
  fi

  # 启用swap
  if ! swapon -s | grep -q swapfile; then
    swapon /mnt/sd/swapfile 2>/dev/null || true
    echo "  ✅ 大容量Swap空间已启用 (512MB)"
  fi

  # 调整内存管理参数
  echo "  ⚙️ 优化内存管理参数..."
  echo 10 > /proc/sys/vm/swappiness 2>/dev/null || true
  echo 1 > /proc/sys/vm/overcommit_memory 2>/dev/null || true

  # 关闭不必要的进程
  killall psplash 2>/dev/null || true
  killall ts_calibrate 2>/dev/null || true

  # 清理系统缓存
  echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
  sync

  echo "  ✅ 低内存优化完成"
  echo "  📊 当前内存状态:"
  free -h
  sleep 1
fi

# 可选的显示清理功能
if [ "$1" = "--clear-display" ] || [ "$1" = "-c" ]; then
  echo "🧹 清理显示设备占用..."

  # 尝试清理可能的Qt进程冲突
  pkill -f "qt.*fb" 2>/dev/null || true
  pkill -f "Qt.*fb" 2>/dev/null || true

  # 清理framebuffer（谨慎操作）
  if [ -w /dev/fb0 ]; then
    echo "  🧹 清理framebuffer设备"
    dd if=/dev/zero of=/dev/fb0 bs=1024 count=1 2>/dev/null || true
  fi

  echo "  ✅ 显示设备清理完成"
  sleep 1
fi

# 设备权限处理（解决显示权限问题）
for device in /dev/input/event* /dev/fb*; do
  if [ -c "$device" ]; then
    chmod 666 "$device" 2>/dev/null || true
  fi
done

# 内存检查和警告
echo "🧠 检查系统内存..."
TOTAL_MEM=$(free -m | awk '/^Mem:/{print $2}')
AVAIL_MEM=$(free -m | awk '/^Mem:/{print $7}')

echo "  总内存: ${TOTAL_MEM}MB"
echo "  可用内存: ${AVAIL_MEM}MB"

if [ "$TOTAL_MEM" -lt 300 ]; then
  echo "  ⚠ 警告: 系统内存较少 (${TOTAL_MEM}MB < 300MB)"
  echo "  💡 建议: 使用 ./run.sh --low-memory 启用低内存模式"

  if [ "$AVAIL_MEM" -lt 150 ]; then
    echo "  🚨 严重警告: 可用内存不足 (${AVAIL_MEM}MB < 150MB)"
    echo "  🚨 程序可能因内存不足被系统杀死"
    echo "  🚨 强烈建议先运行: ./run.sh --low-memory"
  fi
else
  echo "  ✅ 内存充足"
fi

echo "✅ 环境配置完成，启动 TRF (显示修复版本)..."
echo ""

# 启动前最后的内存优化
if [ "$1" = "--low-memory" ] || [ "$1" = "-m" ]; then
  echo "🔧 启动前内存优化..."

  # 再次清理内存缓存
  sync
  echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true

  # 设置更激进的内存管理
  echo 5 > /proc/sys/vm/swappiness 2>/dev/null || true
  echo 0 > /proc/sys/vm/zone_reclaim_mode 2>/dev/null || true

  # 显示最终内存状态
  echo "  📊 启动前内存状态:"
  free -h

  # 设置Qt内存相关环境变量
  export QT_HASH_SEED=0
  export QT_NO_GLIB=1
  export QT_PLUGIN_PATH="./plugins:$QT_PLUGIN_PATH"

  echo "  ✅ 激进内存优化完成"
fi

# 启动TRF
echo "🚀 启动 TRF (显示修复版本)..."

# 使用ulimit限制内存使用
if [ "$1" = "--low-memory" ] || [ "$1" = "-m" ]; then
  # 限制虚拟内存使用到200MB
  ulimit -v 204800 2>/dev/null || true
fi

./trf

# 检查退出状态
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "✅ 程序正常退出"
else
  echo "❌ 程序异常退出，退出码: $EXIT_CODE"

  # 检查是否被OOM杀死
  if dmesg | tail -20 | grep -q "Killed.*trf"; then
    echo "🚨 检测到程序被系统因内存不足杀死"
    echo "💡 解决方案: 运行 ./run.sh --low-memory 启用低内存模式"
  fi
fi

SCRIPT_EOF2

chmod +x run.sh
echo "✅ run.sh 脚本生成完成"

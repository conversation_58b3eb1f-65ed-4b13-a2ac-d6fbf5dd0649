QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# 关键修复：确保资源系统正常工作 (所有平台通用)
DEFINES += QT_SHARED
DEFINES += QT_USE_QSTRINGBUILDER

# 资源文件编译配置 - 修复Windows编译问题
QMAKE_RESOURCE_FLAGS += -compress 9

# File Storage Configuration - SQLite替代方案
DEFINES += FILE_STORAGE_SYSTEM

# Qt版本兼容性配置 - 增强版
greaterThan(QT_MAJOR_VERSION, 5) {
    # Qt 6+版本配置
    CONFIG += c++17
    DEFINES += QT6_VERSION
} else {
    # Qt 5版本配置
    DEFINES += QT5_VERSION
    
    # 通用Qt 5.x兼容性设置 (适用于所有平台)
    DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x050600
    
    # Windows平台特定配置
    win32 {
        # Windows编译优化
        target.platform = windows
        target.arch = x86_64
        DEFINES += WINDOWS_BUILD
        
        # Windows资源系统配置
        DEFINES += QT_RESOURCE_COMPATIBILITY_FIX
        
        message("Windows编译配置: 包含资源系统修复")
    }
    
    # Linux平台配置
    linux {
        # 检测是否为ARM交叉编译
        !contains(QT_ARCH, x86_64):!contains(QT_ARCH, i386) {
            # ARM交叉编译环境 (GitHub Actions)
            target.platform = linux-arm
            target.arch = arm
            DEFINES += CROSS_COMPILE_ARM
            DEFINES += TARGET_SYSTEM_MYD_Y6ULL14X14
            DEFINES += QT_SYMBOL_COMPATIBILITY_FIX
            DEFINES += QT_RESOURCE_COMPATIBILITY_FIX
            
            # ARM专用资源编译优化
            QMAKE_RCC = rcc
            
            message("ARM交叉编译配置: 包含资源和符号兼容性修复")
        } else {
            # x86_64 Linux环境 (本地开发)
            target.platform = linux-x86_64
            target.arch = x86_64
            DEFINES += LINUX_X86_BUILD
            
            message("Linux x86_64编译配置: 标准配置")
        }
    }
}

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    mainscreen.cpp \
    loginscreen.cpp \
    commonheader.cpp \
    basepage.cpp \
    autosamplepage.cpp \
    statsamplepage.cpp \
    fastmodepage.cpp \
    addsamplepage.cpp \
    datepickerdialog.cpp \
    dilutionfactordialog.cpp \
    timerwidget.cpp \
    qcmodulepage.cpp \
    database/entities/Patient.cpp \
    database/entities/TestResult.cpp \
    controllers/TestResultController.cpp \
    database/storage/FileStorage.cpp \
    IncubationStateManager.cpp

HEADERS += \
    mainwindow.h \
    MainScreen.h \
    loginscreen.h \
    commonheader.h \
    basepage.h \
    autosamplepage.h \
    statsamplepage.h \
    fastmodepage.h \
    addsamplepage.h \
    datepickerdialog.h \
    dilutionfactordialog.h \
    timerwidget.h \
    qcmodulepage.h \
    database/entities/Patient.h \
    database/entities/TestResult.h \
    controllers/TestResultController.h \
    database/storage/FileStorage.h \
    IncubationStateManager.h

FORMS += \
    mainwindow.ui \
    mainscreen.ui \
    loginscreen.ui \
    autosamplepage.ui \
    statsamplepage.ui \
    fastmodepage.ui \
    addsamplepage.ui

RESOURCES += \
    resources.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

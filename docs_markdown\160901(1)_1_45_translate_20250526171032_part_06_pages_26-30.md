# 160901(1)_1_45_translate_20250526171032 - 第 6 部分

页面 26 - 30

---

## 第 26 页

HumaFIA |用户手册
28
图3-7从目标值表上传控制数据
为了上传控制的所有数据（批次、水平、目标值），需要控制的目标值表。
首先按下“ Card” 名称。这将打开下一个窗口，用于选择控制参数，例
如示例中的PCT。
选择控制级别。
输入批号。
选择到期日期。
输入目标值（根据参数的单位）。
要完成新控件的上载，请按“ 添加” 按钮。
要删除一个控件，请按控件左侧的深灰色按钮。如果控件已过期，可能
需要这样做。如果控件不再有效，软件会显示红色的“ Exp” 标志。
按照控制盒的使用说明准备控制。
3.3.2 在自动模式下运行控制
在QC模块屏幕中选择“ 自动模式” 控制按钮，以访问下面的屏幕。这允许用户选择在自动模式下分析的下一
个控制。


---

## 第 27 页

HumaFIA |用户手册
29
图3-8选择一个控制材料，在自动模式下运行它
屏幕提供所有已上传控制材料的列表。每个控制级别均被视为单独的控制材料，需要进行分析或运行。
通过按下下一运行的对照材料行中的深灰色按钮来选择对照。X表示已选对照材料。只能选择一种对照材料。
根据对照组的IFU值和参数，向卡盒中添加复溶的对照材料。随后按下“ 插入对照进行孵育” 按钮，在自动模
式下运行对照实验。待孵育并读取结果后，该界面关闭，结果会显示在之前的界面上。
注：列LOT中的红色Exp标志表示该批次的质控品已过期，必须用新批次替代。
3.3.3 在快速模式下运行控制
点击QC模块屏幕上的“ 添加控制到快速模式列表” 按钮，进入该窗口，可选择下一个要添加到快速模式列表
中的控制材料。


---

## 第 28 页

HumaFIA |用户手册
30
图3-9选择一个或多个要进入快速模式列表的控件
屏幕提供所有已上传控制材料的列表。每个控制级别被视为单独的控制材料，以供分析。
注：列LOT中的红色Exp标志表示该批次的质控品已过期，必须用新批次的质控品替换。
按下待分析对照材料行中的深灰色按钮，选择对照材料。X表示已选择的对照材料。可以选取一系列对照材
料，即使参数不同，也可以将其添加到下图所示屏幕上的自动模式列表中。
按下“ 添加选定控件” 按钮，将这些控件材料添加到快速模式列表中。这样就可以以系列方式运行这些控
件。新增一行，如果在孵育时间后有可用的时间段来读取控件，则该行显示“ Next Con.” ，更多详情如下。
图3-10快速模式下要运行的控制的工作列表
按照以下说明对每种选定的对照品进行快速模式下的控制。


---

## 第 29 页

HumaFIA |用户手册
31
在快速模式工作列表的“ 计时器” 列中，会出现“ 下一个控制材料” 按钮。秒数表示可以
开始快速模式下的第一个（或下一个）控制材料的时间。如果秒数达到00，测试就可以开
始了。此时，会显示添加的“ 80µl卷” 按钮。
下一步是将80µl的对照品加入到卡盒中。完成后，立即按下此按钮。这将开始监测该对照
读数的孵育时间。详情请参见IFU。
对于该对照，剩余孵育时间现在以mm：ss的格式显示。当该对照的孵育时间快结束时，将
出现“ 读取” 按钮。
现在还有10秒时间将测试盒插入分析仪并按下“ 读取” 按钮。为了获得最佳结果，请在计
时器达到00时按下“ 读取” 按钮。读取过程仅需几秒钟，随后将显示该控制参数的结果。
如果时间正确，--：- -出现，表明孵育时间正确或在可接受范围内。
如果出现红色数字，例如+02：11，则孵育时间延长了2分钟11秒，表明结果无效，必须重
复检测。
要运行一系列控制材料，请启动上述用于下一个控制的工作流。
一旦第一个控制的孵育时间开始，这个按钮就会显示出来，提示可以准备快速工作列表
中的下一个控制。工作流程程序会运行一个计时器，允许用户准备下一个控制。当计时
器归零时，用户可以选择这个控制进行移液操作。
现在进行第二次对照，将80µl的对照体积移液到卡盒中。完成后立即按下此按钮。现在
开始监测工作列表中第二次对照的孵育时间。
对上述系列进行处理，执行第一次和所有后续控制，直到快速工作列表为空。
一旦快速工作列表为空，则每行将显示控制材料的以下信息：批次、读取日期和时间、控制低和高目标值、
控制水平和控制参数的记录结果。
注：计时器栏中的红色数字表示孵育时间不正确。必须使用另一个试剂盒重复测量。
3.3.4 QC模块中的数据库
QC控制测量的所有结果均显示在以下屏幕上，可通过按下QC模块主屏幕上的数据库按钮访问：


---

## 第 30 页

HumaFIA |用户手册
32
图3-11质控结果数据库
可以通过点击要删除的数据集的深灰色按钮来删除数据集。通过“ 向上翻页” 和“ 向下翻页” 按钮可以访问
更多的数据集。
3.3.5 QC模块中的校准卡概述
按下QC模块主屏幕上的“ 加载新校准卡” 按钮，可查看已上传至分析仪的所有校准卡、其批次、失效日期和
参数。
图3-12 QC模块中上传的校准卡概述
添加校准卡：上传当前插入分析仪的校准卡的数据。一旦将参数的校准卡插入分析仪，就会自动进行此操
作。通过按下要删除的数据集的深灰色按钮，可以删除数据集。


---


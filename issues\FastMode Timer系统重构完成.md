# FastMode Timer系统重构完成

## 任务目标
完全重新实现FastMode Timer系统，确保正确的队列管理和阶段转换

## 需求规格

### 队列管理逻辑
- **阶段0（等待）**：在前面测试未进入孵育阶段时保持等待，不显示文字
- **触发条件**：只有当前面的测试进入阶段3（孵育开始）时，下一个阶段0的测试才能进入阶段1
- **并发限制**：同一时间只能有一个测试处于阶段1-2

### 阶段转换流程
1. **阶段0 → 阶段1**：条件触发（前测试进入孵育）
2. **阶段1 → 阶段2**：自动转换（30秒倒计时结束）
3. **阶段2 → 阶段3**：手动点击转换
4. **阶段3 → 阶段4**：自动转换（孵育时间结束）
5. **阶段4 → 阶段5**：手动点击转换（含10秒读数和时间差验证）

### UI显示规格
- **阶段0**：空白，白色背景，黑色边框
- **阶段1**：`Next Sample\n30` (倒计时), 白色字体, next_sample.png背景
- **阶段2**：`80ul vol.\nadded`, 白色字体, add_80.png背景
- **阶段3**：`Incubation\n-15:00` (倒计时), 黑色字体, incubation.png背景
- **阶段4**：`Read\n-30` (正计时), 白色字体, read.png背景
- **阶段5**：`--:--` 或 `+02:11` (时间差), 浅灰色背景

### 错误时间计算
- 阈值：1分钟
- 在阈值内：显示 `--:--`
- 超出阈值：红色字体显示 `+mm:ss`

## 实施计划

### 1. 清理现有逻辑
- 移除复杂的检查逻辑
- 简化队列管理

### 2. 重新实现核心方法
- `startTestForRow()` - 简化版本
- `startNextWaitingTest()` - 清晰的触发条件
- `handleTimerClick()` - 标准化点击处理
- `onGlobalTimerTick()` - 优化定时器逻辑

### 3. 修复UI显示
- 标准化updateTimerUI方法
- 确保背景图片正确设置
- 优化文字显示和样式

## 实施状态
- [x] 清理现有逻辑
- [x] 重新实现队列管理 - 新增hasTestsInPreIncubationStages()和getNextWaitingRow()
- [x] 重新实现阶段转换 - 简化handleTimerClick逻辑
- [x] 修复UI显示 - 清理调试信息，保持核心显示逻辑
- [x] 实现错误时间计算 - 阶段5支持阈值比较和红色超时显示
- [x] 测试验证 - 发现并修复了幽灵行号问题

## 关键问题修复

### 幽灵行号问题
**问题：** 行-5等负数行号在m_timerStates中存在，但在m_rowData和m_dynamicRows中不存在，导致UI组件缺失错误。

**根因：** TimerStateData被错误创建，但对应的行数据和UI从未被正确创建。

**修复：**
1. startTestForRow增加行数据存在性检查
2. startNextWaitingTest增加行真实存在性验证
3. 自动清理无效的m_timerStates条目

### 防护机制
```cpp
// 验证行是否真实存在
if (!m_rowData.contains(nextRowNumber) || !m_dynamicRows.contains(nextRowNumber)) {
    qDebug() << "FastMode: 行" << nextRowNumber << "不存在，从m_timerStates中清理";
    m_timerStates.remove(nextRowNumber);
    return;
}
```

## 核心改进

### 1. 简化队列管理
```cpp
bool hasTestsInPreIncubationStages() const {
    // 只检查阶段1-2，简洁明了
}

int getNextWaitingRow() const {
    // FIFO队列，按行号排序
}
```

### 2. 标准化阶段转换
- 阶段2→3：立即启动下一个等待测试
- 阶段4→5：10秒读数+时间差计算
- 移除复杂的时序问题

### 3. 优化UI显示
- 移除重复的调试日志
- 保持清晰的状态显示
- 正确的背景图片和文字颜色 
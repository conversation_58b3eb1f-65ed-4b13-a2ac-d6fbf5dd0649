@echo off
echo ===============================================
echo TRF Windows 本地构建测试脚本
echo ===============================================
echo.

echo 🔍 检查Qt环境...
qmake -v
if errorlevel 1 (
    echo ❌ Qt未正确安装或未在PATH中
    pause
    exit /b 1
)
echo.

echo 🔍 验证资源系统修复...
findstr /n "Q_INIT_RESOURCE" main.cpp >nul
if errorlevel 1 (
    echo ⚠ main.cpp中未找到Q_INIT_RESOURCE修复
) else (
    echo ✅ main.cpp包含Q_INIT_RESOURCE修复
)

findstr /n "QT_SHARED\|QT_USE_QSTRINGBUILDER" trf.pro >nul
if errorlevel 1 (
    echo ⚠ trf.pro中未找到资源系统配置
) else (
    echo ✅ trf.pro包含资源系统配置
)
echo.

echo 🧹 清理旧构建文件...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release
if exist *.obj del *.obj
if exist moc_*.cpp del moc_*.cpp
if exist ui_*.h del ui_*.h
if exist qrc_*.cpp del qrc_*.cpp
echo ✅ 清理完成
echo.

echo ⚙️  运行qmake (Windows配置)...
qmake trf.pro CONFIG+=release DEFINES+=QT_RESOURCE_COMPATIBILITY_FIX
if errorlevel 1 (
    echo ❌ qmake失败
    pause
    exit /b 1
)
echo ✅ qmake完成
echo.

echo 🔨 开始构建...
nmake
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo.

echo 🔍 验证构建结果...
if exist release\trf.exe (
    echo ✅ trf.exe构建成功
    dir release\trf.exe
    echo.
    
    echo 🔍 检查资源系统修复状态...
    strings release\trf.exe | findstr /i "Q_INIT_RESOURCE qInitResources" >nul
    if errorlevel 1 (
        echo ℹ 资源系统修复检查: 二进制中未找到明显标识 ^(这是正常的^)
        echo ℹ 修复状态取决于源码中的Q_INIT_RESOURCE调用
    ) else (
        echo ✅ 资源系统初始化修复已应用
    )
) else (
    echo ❌ trf.exe未生成
    if exist release (
        echo 📁 release目录内容:
        dir release
    )
    pause
    exit /b 1
)
echo.

echo 🎉 Windows本地构建测试完成!
echo ✅ 编译成功
echo ✅ 资源系统修复已包含
echo ✅ 可执行文件已生成: release\trf.exe
echo.
echo 🚀 测试运行 (可选):
echo    cd release
echo    trf.exe
echo.
pause 
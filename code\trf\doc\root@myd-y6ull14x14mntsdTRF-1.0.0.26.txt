root@myd-y6ull14x14:/mnt/sd/TRF-********-Linux-ARM-v7l# ./run.sh 
TRF ARM launch script - <PERSON><PERSON> Jan 18 05:21:32 UTC 2022
=========================
Specifically adapted target system: Linux myd-y6ull14x14 4.1.15+ armv7l Qt 5.6.2
Build environment: Ubuntu 16.04 armv7l Qt 5.6.x (exact match)
Using touch device: /dev/input/event1 (iMX6UL TouchScreen Controller)
./run.sh: line 53: warning: setlocale: LC_ALL: cannot change locale (C.UTF-8): No such file or directory
Starting TRF (ARMv7l - myd-y6ull14x14 specific build)...
Qt version: 5.5.1 - optimized for embedded ARM systems
Detected target system myd-y6ull14x14, enabling dedicated configuration...
  Using touch device: /dev/input/event1
  myd-y6ull14x14 dedicated configuration enabled
  LinuxFB graphics output configured
  Touch input configured
  EGL/OpenGL ES support enabled
  Font configuration: Using Qt built-in fonts (no external font dependency)
Qt Version Compatibility Config:
  Build Version: 5.5.1
  Runtime Version: 5.6.2
  UTF-8 encoding configured
  ARM system optimization enabled
  Target system: myd-y6ull14x14 (Linux 4.1.15+ armv7l Qt 5.6.2)
  Precise matching mode activated
"Starting TRF application initialization..."
"QApplication created successfully"
Starting Qt plugin path configuration...
  Current plugin paths:
    - "/usr/lib/qt5/plugins" (exists)
    - "/mnt/sd/TRF-********-Linux-ARM-v7l" (exists)
  Configuring ARM system input devices...
  鉁Found touch device: "/dev/input/event0"
  鉁Found touch device: "/dev/input/event1"
  鉁Found touch device: "/dev/input/event2"
  鈫Setting input plugins to: "evdevtouch:/dev/input/event0"
  鉁Found platforms plugin: "/usr/lib/qt5/plugins/platforms"
Setting up optimized font configuration...
Available font families: 14
Using optimized system fonts (English only)
Selected font: "DejaVu Sans" size: 10
"=== System Environment Diagnostics ==="
"Qt Version: 5.5.1"
"Qt Runtime Version: 5.6.2"
"Application Directory: /mnt/sd/TRF-********-Linux-ARM-v7l"
"Executable Path: /mnt/sd/TRF-********-Linux-ARM-v7l/trf"
"Qt Plugin Search Paths:"
"  - /usr/lib/qt5/plugins (exists: Yes)"
"  - /mnt/sd/TRF-********-Linux-ARM-v7l (exists: Yes)"
"Available SQL Drivers:"
"platforms directory: /mnt/sd/TRF-********-Linux-ARM-v7l/platforms (exists: No)"
"Available Styles:"
"  - Windows"
"  - Fusion"
"=== Checking Resource Files ==="
"Global Style File: Exists"
"Image Resource :/images/background_login.png: Exists"
"Image Resource :/images/header_home.png: Exists"
"Image Resource :/images/common/red_sample.png: Exists"
"Starting HumaFIA system database initialization..."
Initializing HumaFIA database...
QSqlDatabase: QSQLITE driver not loaded
QSqlDatabase: available drivers: 
Database path: "/mnt/sd/TRF-********-Linux-ARM-v7l/humafia_system.db"
"Failed to open database: Driver not loaded Driver not loaded"
"Cannot establish database connection"
"Database initialization failed: Cannot establish database connection"-------------------
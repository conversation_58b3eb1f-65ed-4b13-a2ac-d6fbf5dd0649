#ifndef AUTOSAMPLEPAGE_H
#define AUTOSAMPLEPAGE_H

#include <QWidget>
#include <QScrollArea>
#include <QLabel>
#include <QVBoxLayout>
#include <QScroller>
#include <QVector>
#include <QMap>
#include <QDateTime>

// 前向声明
class TestResult;
class Patient;
class QTimer;
class IncubationStateManager;

namespace Ui {
class AutoSamplePage;
}

// Timer状态数据结构 (复制自FastMode)
struct AutoTimerStateData {
    enum Stage {
        STAGE_0 = 0,    // 等待状态
        STAGE_1 = 1,    // 等待板条进入 "Insert Strip"
        STAGE_2 = 2,    // Timer倒计时阶段
        STAGE_3 = 3,    // 温育倒计时 "Incubation\n-mm:ss"
        STAGE_4 = 4,    // 读数等待 "Read\n-ss"
        STAGE_5 = 5,    // 完成状态 "--:--"或"+mm:ss"
        STAGE_WAITING = 6  // 等待状态（当有其他样本在温育时）
    };

    Stage currentStage;
    int remainingSeconds;           // 剩余秒数
    QDateTime stage4StartTime;      // 阶段4开始时间
    QDateTime incubationStartTime;  // 温育开始时间
    bool isClickable;              // 是否可点击
    QString projectType;           // 项目类型（CRP、PCT等）
    QString sampleType;            // 样本类型（Blood、Serum等）

    AutoTimerStateData() : currentStage(STAGE_0), remainingSeconds(0), isClickable(false) {}
};

// 行数据结构 (适用于AutoSample)
struct AutoSampleRowData {
    int rowNumber;
    int resultId;        // 数据库TestResult记录的ID，用于删除操作
    QString patient;
    QString parameter;
    QString result;
    QString datetime;
    QString timer;
    QString lot;
    QString cutoff;
    
    AutoSampleRowData() : rowNumber(0), resultId(-1) {}
    AutoSampleRowData(int num, const QString &pat, const QString &param, 
                      const QString &res, const QString &dt, const QString &tim,
                      const QString &l, const QString &co)
        : rowNumber(num), resultId(-1), patient(pat), parameter(param), result(res),
          datetime(dt), timer(tim), lot(l), cutoff(co) {}
    
    // 带resultId的构造函数，用于从数据库加载数据时使用
    AutoSampleRowData(int num, int resId, const QString &pat, const QString &param, 
                      const QString &res, const QString &dt, const QString &tim,
                      const QString &l, const QString &co)
        : rowNumber(num), resultId(resId), patient(pat), parameter(param), result(res),
          datetime(dt), timer(tim), lot(l), cutoff(co) {}
};

// 动态行UI结构 (适用于AutoSample)
struct AutoSampleDynamicRowUI {
    QWidget *widget;
    QLabel *patientLabel;
    QLabel *paramLabel;
    QLabel *resultLabel;
    QLabel *datetimeLabel;
    QLabel *timerBgLabel;    // Timer列背景图标签
    QLabel *timerLabel;      // Timer列文字标签
    QLabel *lotLabel;
    QLabel *cutoffLabel;
    
    AutoSampleDynamicRowUI() : widget(nullptr), patientLabel(nullptr), paramLabel(nullptr),
                               resultLabel(nullptr), datetimeLabel(nullptr), 
                               timerBgLabel(nullptr), timerLabel(nullptr),
                               lotLabel(nullptr), cutoffLabel(nullptr) {}
};

class AutoSamplePage : public QWidget
{
    Q_OBJECT

public:
    // AutoSample列位置和宽度配置
    struct AutoSampleColumnConfig {
        int x;
        int width;
    };

    explicit AutoSamplePage(QWidget *parent = nullptr);
    ~AutoSamplePage();

    // Methods for managing scrollable table
    void addSampleRow(const QString &patient, const QString &parameter,
                     const QString &result, const QString &datetime,
                     const QString &timer, const QString &lot, const QString &cutoff);
    void addSampleRow(const AutoSampleRowData &rowData);
    void addNewSample(const QString &patient, const QString &parameter,
                     const QString &result, const QString &datetime,
                     const QString &timer, const QString &lot, const QString &cutoff);  // 新增：自动启动Timer测试的样本添加
    void removeRow(int rowNumber);
    void clearAllRows();
    void updateScrollableContent();
    
    // 懒加载相关方法
    void loadInitialDataFromDatabase();
    void convertTestResultToRowData(const TestResult& testResult, const Patient& patient, AutoSampleRowData& rowData);
    bool isDataLoaded() const { return m_dataLoaded; }
    
    // 新增的动态行管理方法
    AutoSampleDynamicRowUI* createDataRow(const AutoSampleRowData &rowData);
    void positionRow(AutoSampleDynamicRowUI *rowUI, int rowIndex);
    void applyRowStyles(AutoSampleDynamicRowUI *rowUI);
    void destroyRow(AutoSampleDynamicRowUI *rowUI);
    
    // 辅助方法
    QLabel* createColumnLabel(QWidget *parent, const AutoSampleColumnConfig &config, const QString &text);
    
    // 数据清空后刷新方法
    void refreshAfterDataClear();
    void updateRowUI(AutoSampleDynamicRowUI *rowUI, const AutoSampleRowData &rowData);
    int getRowDisplayIndex(int rowNumber);
    void repositionAllRows();
    void initializeWithTestData();
    void initializeEmptyDisplay(); // 初始化空显示状态
    void loadHundredRows(); // 加载100行测试数据（已废弃）
    void ensureMinimumRows(); // 确保至少显示5行
    int findFirstEmptyRowOrGetNext(); // 找到第一个空行或获取下一个行号
    int getNextRowNumber(); // 新方法：获取下一个行号用于新数据插入到最上面
    
    // Timer相关方法
    void startTestForRow(int rowNumber);              // 开始测试流程
    void updateTimerUI(int rowNumber);                // 更新Timer列的UI显示
    void advanceTimerStage(int rowNumber);            // 推进Timer到下一阶段
    void handleTimerClick(int rowNumber);             // 处理Timer点击事件
    void startNextWaitingTest();                      // 启动下一个等待的测试
    
    // Timer辅助方法
    bool hasTestsInPreIncubationStages() const;       // 检查是否有阶段1-2的测试
    int getNextWaitingRow() const;                    // 获取下一个等待的行号
    void generateAndUpdateResult(int rowNumber);      // 生成并更新测试结果
    void cleanupInvalidTimerStates();                // 清理无效的Timer状态
    void restoreWaitingToStage2();                   // 恢复Waiting状态的样本到阶段2
    
    // CRP项目相关方法
    int getIncubationTimeForProject(const QString& projectType) const;  // 获取项目温育时间
    bool shouldSkipStages01ForProject(const QString& projectType, const QString& sampleType) const;  // 是否跳过阶段0-1
    bool hasCRPInIncubation() const;                 // 检查是否有CRP项目在温育阶段
    void convertStage2ToWaiting(int excludeRowNumber); // 将其他阶段2样本转为Waiting状态
    void createCRPDualResults(int rowNumber);        // 创建CRP双行结果
    bool isResultInRange(const QString& result, const QString& cutoff); // 检查结果是否在范围内
    
    // 数据访问方法
    int getRowCount() const { return m_rowData.size(); }
    const AutoSampleRowData* getRowData(int rowNumber) const;
    bool hasRow(int rowNumber) const { return m_rowData.contains(rowNumber); }

signals:
    void addSampleClicked();

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void showEvent(QShowEvent *event) override; // 添加showEvent处理页面显示时的懒加载

private:
    Ui::AutoSamplePage *ui;
    
    // 动态行数据管理
    QMap<int, AutoSampleRowData> m_rowData;                      // 行号 -> 行数据
    QMap<int, AutoSampleDynamicRowUI*> m_dynamicRows;           // 行号 -> UI组件
    QVector<int> m_visibleRows;                                 // 当前可见的行号列表
    
    // Timer状态管理
    QMap<int, AutoTimerStateData> m_timerStates;               // 行号 -> Timer状态
    QTimer *m_globalTimer;                                     // 全局1秒定时器
    
    // 懒加载标志
    bool m_dataLoaded;                                          // 数据是否已从数据库加载
    
    // 布局配置常量
    static const int ROW_HEIGHT = 66;           // 行高
    static const int CONTENT_WIDTH = 1012;      // 内容宽度
    
    // AutoSample列位置和宽度配置
    static const AutoSampleColumnConfig COLUMN_PATIENT;
    static const AutoSampleColumnConfig COLUMN_PARAMETER;
    static const AutoSampleColumnConfig COLUMN_RESULT;
    static const AutoSampleColumnConfig COLUMN_DATETIME;
    static const AutoSampleColumnConfig COLUMN_TIMER;
    static const AutoSampleColumnConfig COLUMN_LOT;
    static const AutoSampleColumnConfig COLUMN_CUTOFF;
    
    // Helper methods
    void setupScrollableTable();
    void enableMouseScrolling();
    void enableTouchScrolling();
    void updateVisibleRows();
};

#endif // AUTOSAMPLEPAGE_H 
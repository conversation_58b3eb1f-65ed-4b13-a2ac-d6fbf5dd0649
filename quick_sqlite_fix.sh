#!/bin/bash
# TRF SQLite快速修复脚本
# 基于system.txt分析：目标系统无SQLite插件

echo "=== TRF SQLite快速修复 ==="
echo "基于system.txt分析：myd-y6ull14x14无SQLite插件"

# 检查当前目录
if [ ! -f "./trf" ]; then
  echo "❌ 请在TRF程序目录中运行此脚本"
  exit 1
fi

APP_DIR="$(pwd)"

echo "✓ 当前目录: $APP_DIR"

# 检查SQLite插件
if [ -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
  echo "✓ 发现应用SQLite插件"
else
  echo "❌ 缺失SQLite插件文件"
  exit 1
fi

echo ""
echo "=== 应用SQLite独立模式配置 ==="

# 设置环境变量
export QT_PLUGIN_PATH="$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"

echo "✓ QT_PLUGIN_PATH=$QT_PLUGIN_PATH"
echo "✓ QT_QPA_PLATFORM_PLUGIN_PATH=$QT_QPA_PLATFORM_PLUGIN_PATH"

echo ""
echo "=== 测试运行 ==="
echo "启动TRF进行SQLite测试..."

# 运行程序并检查SQLite
timeout 10s ./trf 2>&1 | grep -E "(SQLite|QSQLITE|available drivers)" | head -5

echo ""
echo "=== 手动运行命令 ==="
echo "要手动运行，请使用以下命令:"
echo "export QT_PLUGIN_PATH=\"$APP_DIR/plugins\""
echo "export QT_QPA_PLATFORM_PLUGIN_PATH=\"$APP_DIR/plugins/platforms\""
echo "./trf" 
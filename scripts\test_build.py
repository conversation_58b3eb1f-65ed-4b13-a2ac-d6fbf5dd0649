#!/usr/bin/env python3
"""
TRF 项目本地构建测试脚本
用于验证 qmake 构建流程是否正常工作
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """运行命令并返回结果"""
    print(f"执行命令: {cmd}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print("输出:", result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        if check:
            sys.exit(1)
        return e

def check_qt_installation():
    """检查 Qt 安装"""
    print("=== 检查 Qt 安装 ===")
    
    # 检查 qmake
    result = run_command("qmake -version", check=False)
    if result.returncode != 0:
        print("❌ qmake 未找到，请确保 Qt 已正确安装并添加到 PATH")
        return False
    
    print("✅ qmake 可用")
    return True

def clean_build_files(project_dir):
    """清理构建文件"""
    print("=== 清理构建文件 ===")
    
    files_to_remove = [
        "Makefile", "Makefile.Debug", "Makefile.Release",
        ".qmake.stash", "*.o", "moc_*.cpp", "ui_*.h", "qrc_*.cpp"
    ]
    
    dirs_to_remove = ["debug", "release", ".moc", ".obj", ".rcc", ".uic"]
    
    for pattern in files_to_remove:
        for file_path in project_dir.glob(pattern):
            if file_path.is_file():
                print(f"删除文件: {file_path}")
                file_path.unlink()
    
    for dir_name in dirs_to_remove:
        dir_path = project_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"删除目录: {dir_path}")
            shutil.rmtree(dir_path)

def test_qmake_build():
    """测试 qmake 构建"""
    print("=== 测试 qmake 构建 ===")
    
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    trf_project = project_root / "code" / "trf"
    
    if not trf_project.exists():
        print(f"❌ 项目目录不存在: {trf_project}")
        return False
    
    print(f"项目目录: {trf_project}")
    
    # 清理之前的构建文件
    clean_build_files(trf_project)
    
    # 运行 qmake
    print("\n--- 运行 qmake ---")
    result = run_command("qmake trf.pro CONFIG+=release", cwd=trf_project, check=False)
    if result.returncode != 0:
        print("❌ qmake 配置失败")
        return False
    
    print("✅ qmake 配置成功")
    
    # 检查生成的 Makefile
    makefile = trf_project / "Makefile"
    if not makefile.exists():
        print("❌ Makefile 未生成")
        return False
    
    print("✅ Makefile 已生成")
    
    # 根据平台选择构建命令
    if sys.platform == "win32":
        build_cmd = "nmake"  # Windows MSVC
    else:
        build_cmd = "make"   # Linux/macOS
    
    # 运行构建 (不强制检查，因为可能缺少某些依赖)
    print(f"\n--- 运行 {build_cmd} ---")
    result = run_command(build_cmd, cwd=trf_project, check=False)
    
    if result.returncode == 0:
        print("✅ 构建成功")
        
        # 检查可执行文件
        if sys.platform == "win32":
            exe_paths = [
                trf_project / "release" / "trf.exe",
                trf_project / "trf.exe"
            ]
        else:
            exe_paths = [
                trf_project / "trf"
            ]
        
        exe_found = False
        for exe_path in exe_paths:
            if exe_path.exists():
                print(f"✅ 可执行文件已生成: {exe_path}")
                exe_found = True
                break
        
        if not exe_found:
            print("⚠️  构建成功但未找到可执行文件")
            return False
        
        return True
    else:
        print("❌ 构建失败，这可能是由于缺少某些依赖")
        print("请检查错误信息并确保所有 Qt 模块都已正确安装")
        return False

def main():
    """主函数"""
    print("TRF 项目构建测试")
    print("=" * 50)
    
    # 检查 Qt 安装
    if not check_qt_installation():
        sys.exit(1)
    
    # 测试构建
    if test_qmake_build():
        print("\n🎉 构建测试通过！GitHub Actions 工作流应该能够正常工作。")
    else:
        print("\n⚠️  构建测试未完全通过，请检查上述错误信息。")
        print("这可能不影响 GitHub Actions 工作流，因为 CI 环境会自动安装所有依赖。")

if __name__ == "__main__":
    main() 
#ifndef QCMODULEPAGE_H
#define QCMODULEPAGE_H

#include <QWidget>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QMessageBox>

class QCModulePage : public QWidget
{
    Q_OBJECT

public:
    explicit QCModulePage(QWidget *parent = nullptr);
    ~QCModulePage();

signals:
    void dataCleared(); // 数据清空信号

private slots:
    void onClearDataButtonClicked();

private:
    void setupUi();
    void clearUserData();

private:
    QPushButton *clearDataButton;
    QLabel *titleLabel;
    QLabel *descriptionLabel;
};

#endif // QCMODULEPAGE_H 
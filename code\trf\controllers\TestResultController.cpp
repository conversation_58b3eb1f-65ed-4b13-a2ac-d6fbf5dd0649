#include "TestResultController.h"
#include "../database/storage/FileStorage.h"
#include <QDebug>
#include <QRegularExpression>
#include <QTime>
#include <cstdlib>
#include <ctime>

// AddSampleFormData 实现
bool AddSampleFormData::isValid() const
{
    // 用户信息现在允许为空，不再是必填项
    // 如果填写了生日，必须是有效日期且不能晚于当前日期
    if (birthday.isValid() && birthday > QDate::currentDate()) {
        return false;
    }
    
    // 性别如果有值，必须是有效选项
    if (!gender.isEmpty() && gender != "Male" && gender != "Female") {
        return false;
    }
    
    // 测试参数是必填的
    if (parameterType.trimmed().isEmpty()) {
        return false;
    }
    
    // 样本类型是必填的
    if (sampleType != "S" && sampleType != "P" && sampleType != "B") {
        return false;
    }
    
    return true;
}

QString AddSampleFormData::getValidationError() const
{
    QStringList errors;
    
    // 用户信息现在为可选，只在有值时验证
    if (birthday.isValid() && birthday > QDate::currentDate()) {
        errors << "生日不能晚于当前日期";
    }
    
    if (!gender.isEmpty() && gender != "Male" && gender != "Female") {
        errors << "性别必须为Male或Female";
    }
    
    // 测试相关信息是必填的
    if (parameterType.trimmed().isEmpty()) {
        errors << "参数类型不能为空";
    }
    
    if (sampleType != "S" && sampleType != "P" && sampleType != "B") {
        errors << "样本类型必须为S(血清)、P(血浆)或B(全血)";
    }
    
    return errors.join("; ");
}

// TestResultController 实现
TestResultController::TestResultController(QObject *parent)
    : QObject(parent)
{
}

bool TestResultController::handleInsertButtonClick(SourcePageType sourceType, const AddSampleFormData& formData)
{
    qDebug() << "Processing insert request for mode:" << TestResult::testModeToString(sourceType);
    
    // 1. 验证表单数据
    if (!validateFormData(formData)) {
        setError("表单数据验证失败: " + formData.getValidationError());
        emit insertFailed(m_lastError, sourceType);
        return false;
    }
    
    // 2. 创建或获取患者记录
    int patientId = createOrGetPatient(formData);
    if (patientId <= 0) {
        setError("创建或获取患者记录失败");
        emit insertFailed(m_lastError, sourceType);
        return false;
    }
    
    // 3. 创建测试结果记录
    int resultId = createTestResult(patientId, sourceType, formData);
    if (resultId <= 0) {
        setError("创建测试结果记录失败");
        emit insertFailed(m_lastError, sourceType);
        return false;
    }
    
    // 4. 发送成功信号
    qDebug() << "Data inserted successfully - Patient ID:" << patientId << "Result ID:" << resultId;
    emit dataInserted(resultId, sourceType);
    return true;
}

int TestResultController::createOrGetPatient(const AddSampleFormData& formData)
{
    // 1. 如果有姓名信息，检查患者是否已存在
    if (!formData.lastName.trimmed().isEmpty() && !formData.firstName.trimmed().isEmpty()) {
        int existingPatientId = findExistingPatient(formData.lastName, formData.firstName);
        if (existingPatientId > 0) {
            qDebug() << "Found existing patient with ID:" << existingPatientId;
            return existingPatientId;
        }
    }
    
    // 2. 创建新患者（可能只有部分信息）
    QString lastName = formData.lastName.trimmed().isEmpty() ? "Unknown" : formData.lastName;
    QString firstName = formData.firstName.trimmed().isEmpty() ? "Patient" : formData.firstName;
    QDate birthday = formData.birthday.isValid() ? formData.birthday : QDate::currentDate();
    QString gender = formData.gender.isEmpty() ? "Male" : formData.gender;
    
    Patient newPatient(lastName, firstName, birthday, gender);
    newPatient.setRemarks(formData.remarks);
    
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        setError("文件存储初始化失败: " + storage->getLastError());
        return -1;
    }
    
    bool saved = storage->savePatient(newPatient);
    if (!saved) {
        setError("创建患者失败: " + storage->getLastError());
        return -1;
    }
    
    // 获取刚保存的患者ID（通过姓名查找）
    QList<Patient> allPatients = storage->getAllPatients();
    int newPatientId = -1;
    for (const Patient& p : allPatients) {
        if (p.getLastName() == newPatient.getLastName() && 
            p.getFirstName() == newPatient.getFirstName()) {
            newPatientId = p.getPatientId();
            break;
        }
    }
    
    if (newPatientId <= 0) {
        setError("创建患者失败: " + storage->getLastError());
        return -1;
    }
    
    qDebug() << "Created new patient with ID:" << newPatientId;
    return newPatientId;
}

int TestResultController::createTestResult(int patientId, SourcePageType sourceType, const AddSampleFormData& formData)
{
    // 创建测试结果对象
    TestResult testResult;
    testResult.setPatientId(patientId);
    testResult.setTestMode(sourceType);
    testResult.setParameterType(formData.parameterType);
    testResult.setSampleType(formData.sampleType);
    testResult.setTestDateTime(QDateTime::currentDateTime());
    
    // 解析稀释倍数
    double dilutionFactor = parseDilutionFactor(formData.preDilution);
    testResult.setDilutionFactor(dilutionFactor);
    
    // 设置测试结果状态
    // 对于新添加的样本，结果尚未出来，显示等待状态
    QString resultValue = "--,--"; // 表示测试尚未完成
    QString resultStatus = "Testing"; 
    
    // 如果需要模拟已完成的测试结果，可以在这里生成
    // QString mockResult = generateMockTestResult(formData.parameterType);
    // resultValue = mockResult;
    // resultStatus = "正常";
    
    testResult.setTestResult(resultValue);
    testResult.setResultStatus(resultStatus);
    
    // 生成批号和截止值
    testResult.setLotNumber(generateLotNumber(sourceType));
    testResult.setCutoffValue(generateCutoffValue(formData.parameterType));
    
    // 验证测试结果数据
    if (!testResult.isValid()) {
        setError("测试结果数据无效: " + testResult.getValidationError());
        return -1;
    }
    
    // 保存测试结果到文件存储
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        setError("文件存储初始化失败: " + storage->getLastError());
        return -1;
    }
    
    bool saved = storage->saveTestResult(testResult);
    if (!saved) {
        setError("保存测试结果失败: " + storage->getLastError());
        return -1;
    }
    
    // 获取刚保存的测试结果ID（通过患者ID和时间查找最新的）
    QList<TestResult> allResults = storage->getTestResultsByPatientId(testResult.getPatientId());
    int resultId = -1;
    QDateTime latestTime;
    for (const TestResult& r : allResults) {
        if (r.getTestDateTime() > latestTime) {
            latestTime = r.getTestDateTime();
            resultId = r.getResultId();
        }
    }
    
    if (resultId <= 0) {
        setError("保存测试结果失败: " + storage->getLastError());
        return -1;
    }
    
    qDebug() << "Test result saved to database with ID:" << resultId;
    qDebug() << "Test result details:" << testResult.toString();
    
    return resultId;
}

bool TestResultController::validateFormData(const AddSampleFormData& formData)
{
    if (!formData.isValid()) {
        return false;
    }
    
    // 额外的业务逻辑验证
    if (!isValidParameterType(formData.parameterType)) {
        return false;
    }
    
    if (!isValidSampleType(formData.sampleType)) {
        return false;
    }
    
    return true;
}

int TestResultController::findExistingPatient(const QString& lastName, const QString& firstName)
{
    FileStorage* storage = FileStorage::getInstance();
    if (!storage->initialize()) {
        return -1;
    }
    
    // 精确匹配姓名 - 从所有患者中查找
    QList<Patient> patients = storage->getAllPatients();
    
    // 查找完全匹配的患者
    for (const Patient& patient : patients) {
        if (patient.getLastName() == lastName && patient.getFirstName() == firstName) {
            return patient.getPatientId();
        }
    }
    
    return -1; // 未找到
}

double TestResultController::parseDilutionFactor(const QString& preDilutionText)
{
    if (preDilutionText.isEmpty() || preDilutionText == "No") {
        return 1.0;
    }
    
    if (preDilutionText.startsWith("Yes")) {
        // 解析 "Yes 1:2.5" 格式
        QRegularExpression regex(R"(Yes\s+1:([\d.]+))");
        QRegularExpressionMatch match = regex.match(preDilutionText);
        
        if (match.hasMatch()) {
            bool ok;
            double factor = match.captured(1).toDouble(&ok);
            if (ok && factor > 0) {
                return factor;
            }
        }
    }
    
    return 1.0; // 默认值
}

QString TestResultController::generateMockTestResult(const QString& parameterType)
{
    // 初始化随机数种子（只在第一次调用时执行）
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<uint>(QTime::currentTime().msec()));
        seeded = true;
    }
    
    // 根据参数类型生成合理的模拟值
    if (parameterType.contains("CRP", Qt::CaseInsensitive)) {
        double value = 0.5 + (rand() / static_cast<double>(RAND_MAX) * 49.5); // 0.5 to 50.0
        return QString::number(value, 'f', 2);
    } else if (parameterType.contains("PCT", Qt::CaseInsensitive)) {
        double value = 0.1 + (rand() / static_cast<double>(RAND_MAX) * 24.9); // 0.1 to 25.0
        return QString::number(value, 'f', 2);
    } else if (parameterType.contains("IL-6", Qt::CaseInsensitive)) {
        double value = 1.0 + (rand() / static_cast<double>(RAND_MAX) * 99.0); // 1.0 to 100.0
        return QString::number(value, 'f', 1);
    } else if (parameterType.contains("TNF", Qt::CaseInsensitive)) {
        double value = 0.5 + (rand() / static_cast<double>(RAND_MAX) * 19.5); // 0.5 to 20.0
        return QString::number(value, 'f', 1);
    } else {
        // 通用数值范围
        double value = 1.0 + (rand() / static_cast<double>(RAND_MAX) * 99.0); // 1.0 to 100.0
        return QString::number(value, 'f', 2);
    }
}

QString TestResultController::getLastError() const
{
    return m_lastError;
}

void TestResultController::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "TestResultController Error:" << error;
}

QString TestResultController::generateLotNumber(SourcePageType sourceType)
{
    QString prefix;
    switch (sourceType) {
        case SourcePageType::AUTO_SAMPLE:
            prefix = "AUTO";
            break;
        case SourcePageType::STAT_SAMPLE:
            prefix = "STAT";
            break;
        case SourcePageType::FAST_MODE:
            prefix = "FAST";
            break;
    }
    
    QString dateStr = QDate::currentDate().toString("yyyyMMdd");
    int randomNum = 100 + (rand() % 900); // 100-999范围
    
    return QString("%1%2%3").arg(prefix, dateStr).arg(randomNum);
}

QString TestResultController::generateCutoffValue(const QString& parameterType)
{
    // 根据参数类型生成合理的截止值
    if (parameterType.contains("CRP", Qt::CaseInsensitive)) {
        return "<3.00";
    } else if (parameterType.contains("PCT", Qt::CaseInsensitive)) {
        return "<0.25";
    } else if (parameterType.contains("IL-6", Qt::CaseInsensitive)) {
        return "<7.00";
    } else if (parameterType.contains("TNF", Qt::CaseInsensitive)) {
        return "<8.10";
    } else {
        return "<5.00"; // 默认值
    }
}

bool TestResultController::isValidParameterType(const QString& parameterType)
{
    if (parameterType.trimmed().isEmpty()) {
        return false;
    }
    
    // 常见参数类型列表
    QStringList validTypes = {
        "hs-CRP [mg/L]", "CRP [mg/L]", "PCT [ng/mL]", "IL-6 [pg/mL]",
        "TNF-α [pg/mL]", "ESR [mm/h]", "WBC [×10³/μL]", "Hb [g/dL]",
        "cTnI [ng/mL]", "Myo [ng/mL]", "CK-MB [ng/mL]"
    };
    
    // 精确匹配或包含匹配
    for (const QString& validType : validTypes) {
        if (parameterType == validType || 
            parameterType.contains(validType.split(" ").first(), Qt::CaseInsensitive)) {
            return true;
        }
    }
    
    return false;
}

bool TestResultController::isValidSampleType(const QString& sampleType)
{
    return sampleType == "S" || sampleType == "P" || sampleType == "B";
} 
#ifndef TESTRESULTCONTROLLER_H
#define TESTRESULTCONTROLLER_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include "../addsamplepage.h"
#include "../database/entities/Patient.h"
#include "../database/entities/TestResult.h"

/**
 * AddSample表单数据结构
 * 包含用户在AddSamplePage中输入的所有数据
 */
struct AddSampleFormData {
    QString lastName;           // 姓氏
    QString firstName;          // 名字
    QDate birthday;            // 生日
    QString gender;            // 性别
    QString remarks;           // 备注
    QString parameterType;     // 参数类型
    QString sampleType;        // 样本类型
    QString preDilution;       // 预稀释信息
    QString addId;             // 添加的ID
    
    // 默认构造函数
    AddSampleFormData() : gender("Male") {}
    
    // 验证表单数据
    bool isValid() const;
    QString getValidationError() const;
};

/**
 * 测试结果业务逻辑控制器
 * 负责处理三种测试模式的数据插入和验证逻辑
 */
class TestResultController : public QObject
{
    Q_OBJECT

public:
    explicit TestResultController(QObject *parent = nullptr);
    
    /**
     * 处理AddSamplePage的Insert按钮点击事件
     * 这是主要的业务逻辑入口点
     * @param sourceType 来源页面类型（三种模式之一）
     * @param formData 表单数据
     * @return bool 处理是否成功
     */
    bool handleInsertButtonClick(SourcePageType sourceType, const AddSampleFormData& formData);
    
    /**
     * 创建或获取患者记录
     * @param formData 表单数据
     * @return int 患者ID，失败返回-1
     */
    int createOrGetPatient(const AddSampleFormData& formData);
    
    /**
     * 创建测试结果记录
     * @param patientId 患者ID
     * @param sourceType 测试模式
     * @param formData 表单数据
     * @return int 测试结果ID，失败返回-1
     */
    int createTestResult(int patientId, SourcePageType sourceType, const AddSampleFormData& formData);
    
    /**
     * 验证表单数据
     * @param formData 表单数据
     * @return bool 数据是否有效
     */
    bool validateFormData(const AddSampleFormData& formData);
    
    /**
     * 检查患者是否已存在
     * @param lastName 姓氏
     * @param firstName 名字
     * @return int 存在的患者ID，不存在返回-1
     */
    int findExistingPatient(const QString& lastName, const QString& firstName);
    
    /**
     * 解析预稀释信息
     * @param preDilutionText 预稀释文本（如 "Yes 1:2.5"）
     * @return double 稀释倍数，默认1.0
     */
    double parseDilutionFactor(const QString& preDilutionText);
    
    /**
     * 生成测试结果值（模拟）
     * 在实际系统中，这将来自测试设备
     * @param parameterType 参数类型
     * @return QString 模拟的测试结果
     */
    QString generateMockTestResult(const QString& parameterType);
    
    /**
     * 获取最后的错误信息
     * @return QString 错误描述
     */
    QString getLastError() const;

signals:
    /**
     * 数据插入成功信号
     * @param resultId 新创建的测试结果ID
     * @param sourceType 测试模式
     */
    void dataInserted(int resultId, SourcePageType sourceType);
    
    /**
     * 数据插入失败信号
     * @param error 错误信息
     * @param sourceType 测试模式
     */
    void insertFailed(const QString& error, SourcePageType sourceType);

private:
    QString m_lastError;
    
    /**
     * 设置错误信息
     * @param error 错误描述
     */
    void setError(const QString& error);
    
    /**
     * 根据测试模式生成特定的批号
     * @param sourceType 测试模式
     * @return QString 批号
     */
    QString generateLotNumber(SourcePageType sourceType);
    
    /**
     * 根据参数类型生成截止值
     * @param parameterType 参数类型
     * @return QString 截止值
     */
    QString generateCutoffValue(const QString& parameterType);
    
    /**
     * 验证参数类型是否有效
     * @param parameterType 参数类型
     * @return bool 是否有效
     */
    bool isValidParameterType(const QString& parameterType);
    
    /**
     * 验证样本类型是否有效
     * @param sampleType 样本类型
     * @return bool 是否有效
     */
    bool isValidSampleType(const QString& sampleType);
};

#endif // TESTRESULTCONTROLLER_H 
# AutoSample页面背景图片实现

## 任务背景
为AutoSample页面实现背景图片功能：
1. 为header row的7个label添加背景图片
2. 为"Add sample"按钮添加背景图片，参考mainscreen实现方案

## 图片资源
Header labels背景图片：
- patient_id.png → Patient, ID
- parameter.png → Parameter  
- result.png → Result
- date_time.png → Date&Time, sample
- timer.png → Timer
- lot.png → Lot
- cut_off.png → Cut-off

Add sample按钮背景图片：
- add_sample.png → Add sample

## 实现方案
1. **Header labels**：直接设置pixmap属性和scaledContents
2. **Add sample按钮**：使用mainscreen的分层结构（背景QLabel + 前景QPushButton）

## 实现步骤
1. 更新resources.qrc添加图片资源
2. 修改autosamplepage.ui设置header背景图片
3. 实现Add sample按钮分层结构
4. 调整样式确保文字可见性

## 预期结果
- 图片填满label区域
- 文字居中显示在背景图片之上
- 保持良好的可读性 
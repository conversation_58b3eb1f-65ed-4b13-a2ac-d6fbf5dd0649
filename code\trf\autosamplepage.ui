<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AutoSamplePage</class>
 <widget class="QWidget" name="AutoSamplePage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>475</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
QWidget#AutoSamplePage {
    background-color: #7f7f7f; /* Use global dark gray */
}

/* Data row labels - keep white background */
QLabel[objectName*="label_r"] {
    border: 1px solid black;
    padding: 4px;
    background-color: white;
    color: #333333;
    font-size: 11pt;
    alignment: AlignCenter;
}

/* Patient and Parameter columns - optimized for word wrap */
QLabel[objectName*="label_r"][objectName*="patient"],
QLabel[objectName*="label_r"][objectName*="param"] {
    font-size: 10pt;
    padding: 2px;
    line-height: 1.2;
}

/* Header Row Style */
#label_header_patient, #label_header_parameter, #label_header_result, #label_header_datetime, #label_header_timer, #label_header_lot, #label_header_cutoff {
    border: 1px solid black;
    padding: 4px;
    font-weight: bold;
    color: black;
    font-size: 12pt;
}

/* Add Sample Row Style */
#widget_add_sample {
    background-color: transparent;
}

/* Result Column cells with special colors */
#label_r1_result, #label_r2_result, #label_r3_result, #label_r4_result, #label_r5_result, #label_r6_result, #label_r7_result, #label_r8_result, #label_r9_result, #label_r10_result, #label_r11_result {
    color: #D32F2F;
    font-weight: bold;
}

/* Scroll Area Style */
#scrollArea {
    border: none;
    background-color: transparent;
}

#scrollAreaWidgetContents {
    background-color: transparent;
}
   </string>
  </property>
  
  <!-- Fixed Header Row -->
  <widget class="QWidget" name="widget_header_patient" native="true">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>5</y>
     <width>190</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_patient">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/patient_id.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_patient">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>190</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Patient, ID</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_parameter" native="true">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>5</y>
     <width>152</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_parameter">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>152</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/parameter.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_parameter">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>152</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Parameter</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_result" native="true">
   <property name="geometry">
    <rect>
     <x>352</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_result">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/result.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_result">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>114</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Result</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_datetime" native="true">
   <property name="geometry">
    <rect>
     <x>466</x>
     <y>5</y>
     <width>190</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_datetime">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/date_time.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_datetime">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>190</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Date&amp;Time, sample</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_timer" native="true">
   <property name="geometry">
    <rect>
     <x>656</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_timer">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/timer.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_timer">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>114</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Timer</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_lot" native="true">
   <property name="geometry">
    <rect>
     <x>770</x>
     <y>5</y>
     <width>114</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_lot">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>114</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/lot.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_lot">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>114</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Lot</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>
  
  <widget class="QWidget" name="widget_header_cutoff" native="true">
   <property name="geometry">
    <rect>
     <x>884</x>
     <y>5</y>
     <width>138</width>
     <height>66</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_header_cutoff">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>138</width>
      <height>66</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/cut_off.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QLabel" name="label_header_cutoff">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>138</width>
       <height>66</height>
      </rect>
     </property>
     <property name="text">
      <string>Cut-off</string>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: transparent; border: 1px solid black; padding: 4px; font-weight: bold; color: black; font-size: 12pt;</string>
     </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
  </widget>

  <!-- Fixed Add Sample Button Row -->
  <widget class="QWidget" name="widget_add_sample" native="true">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>75</y>
     <width>190</width>
     <height>58</height>
    </rect>
   </property>
   <widget class="QLabel" name="bg_label_add_sample">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>58</height>
     </rect>
    </property>
    <property name="pixmap">
     <pixmap resource="resources.qrc">:/images/auto_sample/add_sample.png</pixmap>
    </property>
    <property name="scaledContents">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="button_add_sample">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>190</width>
      <height>58</height>
     </rect>
    </property>
    <property name="text">
     <string>Add sample</string>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton {
    background-color: transparent;
    border: 1px solid black;
    color: black;
    font-weight: bold;
    font-size: 11pt;
}
QPushButton:hover {
    background-color: rgba(255, 255, 255, 50);
}
QPushButton:pressed {
    background-color: rgba(0, 0, 0, 50);
}</string>
    </property>
   </widget>
  </widget>

  <!-- Scrollable Data Table Area -->
  <widget class="QScrollArea" name="scrollArea">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>137</y>
     <width>1012</width>
     <height>338</height>
    </rect>
   </property>
   <property name="verticalScrollBarPolicy">
    <enum>Qt::ScrollBarAlwaysOff</enum>
   </property>
   <property name="horizontalScrollBarPolicy">
    <enum>Qt::ScrollBarAlwaysOff</enum>
   </property>
   <property name="widgetResizable">
    <bool>false</bool>
   </property>
   <property name="focusPolicy">
    <enum>Qt::WheelFocus</enum>
   </property>
   <widget class="QWidget" name="scrollAreaWidgetContents">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>1012</width>
      <height>330</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>1012</width>
      <height>330</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>1012</width>
      <height>16777215</height>
     </size>
    </property>
    
    <!-- Dynamic rows will be created programmatically -->

   </widget>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>

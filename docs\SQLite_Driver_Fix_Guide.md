# TRF SQLite 驱动修复指南摘要

## 问题状况
在 myd-y6ull14x14 系统上运行 TRF 时遇到：
```
QSqlDatabase: QSQLITE driver not loaded
Database initialization failed: Cannot establish database connection
```

## 解决方案

### 🚀 即时解决方案（现在就能用）

**在您的 ARM 设备上执行以下命令：**

```bash
# 1. 快速创建插件目录
mkdir -p /mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers

# 2. 搜索并复制系统 SQLite 驱动
find /usr -name "*sqlite*.so" 2>/dev/null | head -1 | xargs -I {} cp {} /mnt/sd/TRF-********-Linux-ARM-v7l/plugins/sqldrivers/ 2>/dev/null || echo "系统中未找到 SQLite 驱动"

# 3. 创建简单启动脚本
cat > /mnt/sd/TRF-********-Linux-ARM-v7l/run_fixed.sh << 'EOF'
#!/bin/bash
cd /mnt/sd/TRF-********-Linux-ARM-v7l
export QT_PLUGIN_PATH="$(pwd)/plugins:/usr/lib/qt5/plugins"
export LC_ALL=C
./trf
EOF

# 4. 运行测试
chmod +x /mnt/sd/TRF-********-Linux-ARM-v7l/run_fixed.sh
cd /mnt/sd/TRF-********-Linux-ARM-v7l && ./run_fixed.sh
```

### 🔧 自动化解决方案

**我已为您准备了完整的修复工具：**

1. **`scripts/deploy_sqlite_driver.sh`** - 自动检测和部署脚本
2. **`scripts/extract_sqlite_driver_docker.sh`** - Docker 环境提取工具
3. **修改后的 GitHub Actions** - 新版本自动包含 SQLite 驱动

### 📦 长期解决方案

下次发布版本将自动包含 SQLite 驱动，无需手动修复。

## 验证成功

修复后应看到：
```
"QApplication created successfully"
Available SQL Drivers: QSQLITE
"HumaFIA database initialized successfully"
```

## 需要帮助？

如果上述方案都不工作，请提供以下信息：
```bash
# 运行这些命令并提供输出
find /usr -name "*sqlite*.so" 2>/dev/null
ls -la /usr/lib/qt5/plugins/ 2>/dev/null || echo "Qt5 插件目录不存在"
ldconfig -p | grep -i sql
```

---
**状态**: ✅ 解决方案已准备就绪  
**下一步**: 在设备上执行快速解决方案命令 
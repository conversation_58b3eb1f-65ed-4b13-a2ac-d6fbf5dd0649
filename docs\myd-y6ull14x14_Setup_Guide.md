# myd-y6ull14x14 系统环境准备指南

## 概述
根据之前1.0.0.9版本的运行错误分析，目标系统需要进行一些环境配置才能成功运行TRF程序。

## 基于错误日志的问题分析

### 主要问题（来自error.txt）
1. **字体目录缺失**: `QFontDatabase: Cannot find font directory /usr/share/fonts`
2. **tslib插件错误**: `No such plugin for spec "tslib:/dev/input/event0"`
3. **中文字符显示乱码**: 字符编码问题
4. **触摸输入配置**: iMX6UL TouchScreen Controller需要正确配置

## 环境准备清单

### 1. 字体系统配置
```bash
# 创建字体目录
sudo mkdir -p /usr/share/fonts
sudo mkdir -p /usr/share/fonts/truetype

# 如果有中文字体需求，安装字体包
sudo apt-get update
sudo apt-get install fonts-wqy-zenhei fonts-wqy-microhei

# 或者手动复制字体文件
sudo cp /path/to/chinese/fonts/* /usr/share/fonts/truetype/
sudo fc-cache -fv  # 刷新字体缓存
```

### 2. Qt5运行时环境
```bash
# 验证Qt5库（从system.txt看已经安装完整）
ldconfig -p | grep Qt5

# 如果缺少某些库，可以安装
sudo apt-get install qt5-default libqt5sql5-sqlite
```

### 3. 触摸屏驱动配置

#### 检查触摸设备
```bash
# 查看当前输入设备
ls -la /dev/input/

# 检查iMX6UL TouchScreen Controller
cat /proc/bus/input/devices | grep -A 5 "iMX6UL"

# 确保设备权限正确
sudo chmod 666 /dev/input/event*
```

#### tslib配置（可选）
```bash
# 如果系统支持tslib，配置环境变量
export TSLIB_TSDEVICE=/dev/input/event1
export TSLIB_CALIBFILE=/etc/pointercal

# 校准触摸屏（如果需要）
sudo ts_calibrate
```

### 4. 图形系统配置

#### LinuxFB配置
```bash
# 检查帧缓冲设备
ls -la /dev/fb*

# 确保fb0权限正确
sudo chmod 666 /dev/fb0

# 检查当前分辨率
cat /sys/class/graphics/fb0/virtual_size
```

#### EGL/OpenGL ES验证
```bash
# 验证图形库（从system.txt看已经存在）
ldconfig -p | grep -E "(EGL|GLES)"

# 检查GPU设备
ls -la /dev/dri/ 2>/dev/null || echo "DRI设备不存在，使用软件渲染"
```

### 5. 系统环境变量配置

#### 创建TRF启动环境脚本
```bash
# 创建 /etc/trf-env.sh
sudo tee /etc/trf-env.sh > /dev/null << 'EOF'
#!/bin/bash
# TRF 系统环境变量配置

# Qt5基础配置
export QT_SELECT=qt5
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_DISABLE_INPUT=0

# 字体配置
export QT_QPA_FONTDIR="/usr/share/fonts:/usr/share/fonts/truetype:/usr/local/share/fonts"

# 插件路径
export QT_PLUGIN_PATH="/usr/lib/qt5/plugins:/usr/lib/arm-linux-gnueabihf/qt5/plugins"

# 图形配置
export QT_QPA_EGLFS_INTEGRATION=eglfs_viv
export QT_QPA_EGLFS_DISABLE_INPUT=0

# 触摸输入（自动检测最佳设备）
if [ -c /dev/input/event1 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"
elif [ -c /dev/input/event0 ]; then
    export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event0"
fi

# 字符编码
export LC_ALL=C.UTF-8
export LANG=C.UTF-8
export LC_CTYPE=C.UTF-8

# 日志控制（减少无关警告）
export QT_LOGGING_RULES="qt.qpa.fonts.warning=false;qt.widgets.gestures.debug=false;qt.qpa.input.debug=false"

echo "TRF环境变量已配置完成"
EOF

# 设置执行权限
sudo chmod +x /etc/trf-env.sh
```

### 6. 用户权限配置
```bash
# 确保当前用户可以访问输入设备
sudo usermod -a -G input $USER

# 确保可以访问图形设备
sudo usermod -a -G video $USER

# 重新登录或者使用newgrp命令生效
newgrp input
newgrp video
```

### 7. 系统服务配置（可选）
```bash
# 如果需要开机自动启动TRF，创建systemd服务
sudo tee /etc/systemd/system/trf.service > /dev/null << 'EOF'
[Unit]
Description=TRF Application
After=graphical.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/trf
EnvironmentFile=/etc/trf-env.sh
ExecStart=/opt/trf/run.sh
Restart=always
RestartSec=3

[Install]
WantedBy=graphical.target
EOF

# 启用服务（根据需要）
# sudo systemctl enable trf.service
```

## 快速验证脚本

### 创建环境检查脚本
```bash
# 创建快速检查脚本
cat > ~/check_trf_env.sh << 'EOF'
#!/bin/bash
echo "=== TRF 环境检查 ==="

echo "1. 检查Qt5库..."
ldconfig -p | grep Qt5 | head -3

echo -e "\n2. 检查字体目录..."
[ -d "/usr/share/fonts" ] && echo "✓ /usr/share/fonts 存在" || echo "✗ /usr/share/fonts 缺失"

echo -e "\n3. 检查输入设备..."
ls /dev/input/event* 2>/dev/null || echo "✗ 无输入设备"

echo -e "\n4. 检查图形设备..."
[ -c "/dev/fb0" ] && echo "✓ /dev/fb0 存在" || echo "✗ /dev/fb0 缺失"

echo -e "\n5. 检查图形库..."
ldconfig -p | grep -E "(EGL|GLES)" | head -2

echo -e "\n6. 检查当前环境变量..."
env | grep -E "(QT_|LC_)" | head -3

echo -e "\n环境检查完成"
EOF

chmod +x ~/check_trf_env.sh
```

## 使用说明

### 环境准备步骤
1. **运行检查脚本**: `~/check_trf_env.sh`
2. **配置缺失项**: 根据检查结果补充缺失的配置
3. **加载环境变量**: `source /etc/trf-env.sh`
4. **部署TRF程序**: 上传并解压新版本
5. **运行兼容性检查**: `./check.sh`
6. **启动程序**: `./run.sh`

### 故障排除
如果程序仍然无法启动：

1. **查看详细日志**:
   ```bash
   # 启用详细日志
   export QT_LOGGING_RULES="*.debug=true"
   ./run.sh
   
   # 查看启动日志
   cat trf_startup.log
   ```

2. **手动测试组件**:
   ```bash
   # 测试Qt5
   echo 'int main(){return 0;}' | qmake -project && qmake && make
   
   # 测试LinuxFB
   export QT_QPA_PLATFORM=linuxfb
   /usr/lib/qt5/bin/qmlscene --help
   ```

3. **检查系统兼容性**:
   ```bash
   # 运行我们的兼容性检查
   python3 scripts/verify_system_compatibility.py
   ```

## 常见问题解决

### Q: 字体显示乱码怎么办？
A: 确保安装了中文字体包，并设置了正确的LC_*环境变量

### Q: 触摸屏无响应怎么办？
A: 检查/dev/input/event*设备权限，确保QT_QPA_GENERIC_PLUGINS设置正确

### Q: 程序启动黑屏怎么办？
A: 检查LinuxFB配置，确保/dev/fb0可访问，尝试不同的QT_QPA_PLATFORM设置

### Q: Qt库版本不匹配怎么办？
A: 使用我们编译的版本，它与您的Qt 5.6.2精确匹配

---

遵循此指南配置环境后，新版本的TRF应该能够在myd-y6ull14x14系统上正常运行。 
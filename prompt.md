7-14-19-45：

1.CommonHeader的第二排调整下，{"Auto sample", "STAT sample", "Fast mode", "QC module", "Database", "Settings"};

对应的白底图片(激活状态)：    {
        ":/images/common/red_sample.png", ":/images/common/red_sample.png", ":/images/common/red_fast.png",
        ":/images/common/red_qc.png", ":/images/common/red_database.png", ":/images/common/red_settings.png"
    };

对应的红底图片(激活状态)：    {
        ":/images/common/white_sample.png", ":/images/common/white_sample.png", ":/images/common/white_fast.png",
        ":/images/common/white_qc.png", ":/images/common/white_database.png", ":/images/common/white_settings.png"
    };

激活状态下，选用白底图片，字为灰色，非激活的标签为红底图片，字为白色。同一时间，只有一个标签被激活。文字左对齐。图片刚好撑满一格的位置，可能需要调整长宽到格子大小一样。

7-14-20-00：

文字显示是正确的，但是图片没有显示出来，层级关系对吗？



7-14-20-26：

1.第二排的6个控件间隔略微有点大，改为第一排温度框和时间框那个间隔一样大。

2.第二排的字体略微缩小一点，文字稍微被遮住了一点

3.第一排左边的图片和中间填空白的部分的间隔去掉，别的不动。



7-14-20-35：

1.第二排第5个控件激活的时候，白色框把第6个框的字体盖住了一部分。我建议可以按照长度为1024分割固定长度，不用layout，这样不容易出问题。要细细的边框效果。

2.第一排和第二排间隔小一些。



7-14-20-39：

第二排的图片的长宽不对，要和控件大小要一样大，填满。

7-14-20-43：

还是不对，和刚刚差不多，你得重新好好检查下

7-14-20-47：

还是有些重叠，检查下，红、白图都被正常限制在自己的区域内吗？并和区域一样大吗？

7-14-20-52：

红、白图都被设置为一样大吗？和文字框一样大吗？那怎么会被遮住一部分字呢？

7-14-20-55：

肯定不是一个像素的问题，应该是哪里不对。好好检查下

7-14-20-58：

第二排也加上细细的human红边框

7-14-21-00：

1.---图片文字的宽度，没有填满，没到上面的边框，有些间隔，是不是高度不对？

2.---4,6两个格子的红色图片长度小了一截，所以白色字体有些看不清了。



7-14-21-04：

不要用动态调整的，固定长和高，那么每个控件全部起始的XY起点，和长宽都知道了，全部用固定数值，这样就没问题了。

7-14-21-08：

宽度不够，能看到图片离外围的边框还有一些距离了。emmmm，还有别的想法吗？

7-14-21-11：

是不是因为被图片的比例限制了啊，宽度能拉也拉一下鸭

7-14-21-14：

好消息，大小对了。但是图片显示有问题，但能看到红底的宽度对了。

7-14-21-19：

又变成原来这样了，宽度不够

7-14-21-25：

搞不定，你换种激进的方案重新实现吧，先好好思考下在动手

21:26:35: 

Starting D:\dev\private project\code\trf-new\code\trf\build\desktop-Release\release\trf.exe...
qt.core.qmetaobject.connectslotsbyname: QMetaObject::connectSlotsByName: No matching signal for on_main_button_clicked()
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Button clicked: "Auto Sample"
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size
Unknown property background-size---------------图片没有显示

7-14-21-58

完全不行，重新思考这种控件的实现。把它抽出来作为基础控件实现，变量是x，y坐标，长和宽，还有背景图设置。参考mainscreen.ui的按钮设计，widget内套一个QLabel放背景图，再放一个QPushbutton带透明背景，文字显示居中或者靠左靠右都可以设置。QPushbutton显示在QLabel放背景图上面。这三个长宽一致，xy一致。作为一个整体控件。文字颜色也可改。



制作完后，再用这个基础控件去替代目前的实现。



22-13

图片和文字都没有正确显示

22-17

能看到6个区域，切换的时候背景变为灰色，其它的背景是白色的，大小是合适的。但是没有任何文字和背景图的显示，红白图都没有。

22-20

没区别，还是能看到6个区域，切换的时候背景变为灰色，其它的背景是白色的，大小是合适的。但是没有任何文字和背景图的显示，红白图都没有。

22-23

文字显示了，图片没有显示。也不能切换。

22-36：

不是这个，是更早版本，我让你做基础控件之前的版本。实现用那个，但是做成基础控件，能做到吗》？

7-15

下面开始做几个取样页面底下部分，你识别一下，能不能做到让我自己在design界面自己规划界面？

6个标签页除了共用上面两排的内容，是不是应该做成6个UI页面？

底下部分怎么设计一下？3个页面略有差异，底下应该是个table？

7-17

AddSamplePage要做一些调整。
1.第一行的header的文字右对齐。
2.第二行的label可以对对齐{label_patient_id/label_pre_dil/label_sample/label_parameter/label_add_id/label_add_sample/pushButton_add_sample},一个是空间有限，还有个这些label下面是相关的，所以你观察下，label是要和对应的下面的组件的列对齐的。这一行的label的文字左对齐。
3.第二行的label有几个是背景图的，label带背景图的方案已经有现成的，参考即可。我来指定资源路径。
{
label_patient_id ：images\add_sample\patient_id.png,
label_parameter : images\add_sample\parameter.png,
label_add_id : images\add_sample\patient_id.png.png
}
pushButton_add_sample没有文字，背景图为images\add_sample\add_sample.png
4.Gender下面的选框为Male和Female，默认为Male
5.label_pre_dil和底下的lineEdit_yes_or_no、pushButton_yes、pushButton_no这一列是属于一组，我们称他们为"label_pre_dil组"。pushButton_yes点击后在lineEdit_yes_or_no显示Yes，pushButton_no点击后在lineEdit_yes_or_no显示No。
6.label_sample和底下的lineEdit_sample、pushButton_blood、pushButton_serum、pushButton_plasma、pushButton_capillary这一列是属于一组，我们称他们为"label_sample组"。pushButton_blood、pushButton_serum、pushButton_plasma、pushButton_capillary点击后，lineEdit_sample显示对应刚刚点击按钮的text。
7.label_parameter和底下的lineEdit_parameter、还有8个pushbutton(能识别出来吗，应该可以把，我信任你，CRP/other这些)属于一组，我们称他们为"label_parameter组"。除other按钮外，其它的点击后都显示对应按钮的text到lineEdit_parameter。
8.label_add_id和底下的lineEdit_add_id还有小数字按钮们(0~9)，还有pushButton_exit属于一组,我们称他们为"label_add_id组"。lineEdit_add_id默认是空的，当按下小数字按钮时，在lineEdit_add_id当前后面追加对应的数字，pushButton_exit则是删除一个最后点击的数字，有点像退格键。
9."label_pre_dil组"、"label_sample组"、"label_parameter组"底下的pushbutton都按照label_add_id下面的按钮的长宽调整按钮大小，并且按钮去对齐label_add_id组下的4排小数字的高度。
10.对应几个组的lineEdit在高度方面也要对齐。宽度的话和组内的列宽保持一致。例如，只有一列按钮，对应的lineEdit宽度就是组内pushbutton的宽度。如果是两列按钮，对应的lineEdit宽度就是组内pushbutton的宽度之和再加上中间间隔的宽度，也就是保持对齐。以此类推。



------------

1.统一所有功能组按钮："label_pre_dil组"、"label_sample组"、"label_parameter组"底下的pushbutton看起来比label_add_id下面的按钮要大一些，怎么回事。
2.对应几个组的lineEdit在高度方面也要对齐：没对齐

-------

1.---AddSamplePage有三种模式，唯一的区别是toolButton_insert按钮的区别，显示的文字不同。AutoSamplePage还有STAT sample、还有Fast mode这三个页面都会有一个addsample，跳转后显示不同的toolButton_insert按钮。
---并且toolButton_insert按钮触发后，根据跳转过来的原页面，有不同的响应逻辑，先占位，打印不同数字，到时候调整。
2.---toolButton_cancel按钮的文字为"Insert"。
3.---toolButton_delete按钮的文字为"Delete"。
4.---
4.1---AutoSamplePage对应按下add sample后跳转的toolButton_insert按钮的文字为"Insert cartridge for incubation",文字自动换行。
4.2---STAT sample对应按下add sample(目前还没，未来加)后跳转的toolButton_insert按钮的文字为"Read incubated cartridge",文字自动换行。
4.3---Fast mode对应按下add sample(目前还没，未来加)后跳转的toolButton_insert按钮的文字为"Add test to Fast mode list",文字自动换行。

按钮+背景图的方案已经在项目里面多次出现了，我们直接复用方案即可。下面我指定下图片地址，需要你转为资源等操作下。
公共路径前缀为\images\add_sample\，后面省略了。
1.other按钮pushButton_other对应other.png
2.exit按钮pushButton_exit对应exit.png
3.cancel对应cancel.png
4.delete对应delete.png
5.toolButton_insert按钮的文字为"Insert cartridge for incubation",对应insert.png
6.toolButton_insert按钮的文字为"Read incubated cartridge",对应read.png
7.toolButton_insert按钮的文字为"Add test to Fast mode list",对应fast.png

------

1.加了图片的按钮，移上去没有之前的效果了
2.toolButton_cancel、toolButton_delete、toolButton_insert三个按钮文字没显示
3.toolButton_cancel、toolButton_delete、toolButton_insert三个按钮图片没有覆盖到整个按钮区域

-------

1.按钮悬停的效果还是没有
2.toolButton_cancel、toolButton_delete、toolButton_insert三个按钮图片大小不对，再确认下大小，文字也没有自动换行。

------

1.按钮悬停的效果还是没有
2.toolButton_cancel、toolButton_delete、toolButton_insert三个按钮,图片没有正常显示。文字也没有自动换行。

建议直接采用button_auto_sample类似的方案。

--------------------

1.toolButton_cancel、toolButton_delete可以了
2.toolButton_insert文字没有自动换行
3.other和exit按钮，没有悬停效果。
4.把本页面的按钮如果没有图片背景的，都做成深灰色背景。

-------

1.other和exit按钮，也参考button_auto_sample类似的方案。
2.toolButton_insert文字还是仍然没有自动换行
3.深灰色按钮好像变大了一号？
4.还有几个empty按钮没有变深灰色。

-------

1.other按钮图片显示了。exit按钮还没有。
2.左边的按钮好像还是都大了一号，大小都以小数字按钮0~9为基准。
3.toolButton_insert文字换行了，但是有部分没显示全，怀疑某个外面的容器挡住了或者是自己的大小不对。

-------

1.toolButton_insert文字左右还是被挡住了，感觉像是文字区域的宽度要宽一些，才被左右挡住了。
2.左边的按钮好像还是都大了一号，大小都以小数字按钮0~9为基准。不确定是什么问题，你再检查下。

------

1.toolButton_insert按钮的文字能不能按单词换行，一个单词一行？
2.我看出来的小数字大小是84X66，静态页面下，是这样吗？

-----

1.按单词处理可以，记得把"Read incubated cartridge"、"Add test to Fast mode list"也处理下，根据长度选择，短的话两个单词组一行。以最长的cartridge为基准。
2.84X66是我看到的大小，你分析下是什么原因，layout吗？还是你算错了。

----

1.要么你直接按84X66试下。
2.toolButton_insert字体大小和上面一致。
3.第一行的header的文字右对齐，垂直居中对齐。

-----------

按钮大小还是有差别，我有个想法。参考add id lebel下面的，做了一个widget。parameter，sample，predil下面都可以做一个widget，这样大家的按钮就可以对齐了，大小也一样，看起来更美观了，试试吧。

-------------

1.parameter下面的有点错乱。

2.我们改的几个组按钮的间隔不一，能不能参考add id下面的间隔和按钮大小？我也不知道应该调整什么，还是布局出来的，但是应该有参考性吧

----------------

1.当处于add sample页面时，6个标签页的切换功能无效。退出addsample页面后生效，可以切换。

2.addsample页面的patient id页面，当切到birthday这栏准备输入的时候，屏幕中间跳出来日期选择框，年月日可选，底下OK和cancel，ok确认日期填入birthday框，cancel则退出日期子页面，无事发生。默认跳转到当前日期。按钮深灰色底。保持风格。

--------------

pre-dil这个地方，no按钮还是保持现在的逻辑，填入No。点下Yes后，弹出一个对话框，没有标题。

最上面一个事一整行的输入框，默认显示文字为Dilution factor.浅灰色底。然后下面是一个4X4布局大小的按钮框组合。分别为

{

7,8,9，close，

4,5,6，delete，

1，2,3，OK

0，-，.，OK

}

两个OK合并成一个。总共15个，15个按钮是深灰色底，'.'是小数点，'-'是负号，再点一下取消负号。数字的话点一下在输入框显示一个数字，小数点点一下显示一个小数点，'-'是负号，点一下在输入框最前面显示一个负号，再点一下取消负号。点close是关闭对话框，不对外面的输入框做改变。点delete是从后往前删除一个字符。最后是点击OK，校验是不是一个数字，如果不是，弹出一个warning框，上面是深灰色栏，Warning，中间是错误信息“Input error, please check!”,最底下一栏深灰色底，显示OK。点击OK返回到Addsample页面。如果校验成功，是一个正确的数字，就显示'Yes 1：数字'。

------------------------

AutoSamplePage底下现在是个5X7的伪装成表格的假表格，你能按照目前表格的布局，做成一个真表格吗？因为我copy的目标UI，它这个表格是可以滚动的，但是滚动条是隐藏的。布局保持不变，刚好显示5行内容，但是允许拖动到下面的数据。

---------------

下面做STAT sample标签页：StatSamplePage

这个页面的UI基本和AutoSamplePage一样，如果能直接复用它就更好了。唯一的区别是在StatSamplePage这个页面，header的Timer这一格子只有浅灰色背景，没有文字。同时，底下的表格，这一列上，只要有其它的数据，这列就显示'--:--'.

因为我们应当是MVC模式，最后我们的数据来源是对应addsample按钮按下后添加的信息，后面我们做，预留下，现在就先随机填充一些数据做演示用。

--------------

1.header的每个widget是一个组合，调整的时候要整体调整，widget_header_number下面只看到了label_header_number，但是在widget_header_patient下面看到了三个，检查下吧，有问题。

2.表格的列要和上面的header对齐。

3.表格的默认状态，拖动这种，参照auto sample表格的效果，它只是多了一列罢了，其它的你检查下。现在会拖动到只显示前4行的状态，应该是实现的是拉到最上面，也只能显示刚好前5行。

------------

1.fastmode的header第一个图片应该是fast_header_1.png，我手动改完以后静态页面改好了，但是运行出来显示的时候，还是用了timer.png。

2.fastmode的列，原先最后三列是{。。。，Timer，Lot,Cut-off}，现在我们需要调整下，改为{。。。，Lot,Cut-off，Timer}。整列的宽度跟着走，内容跟着走。

-------------------------

1.打包的时候把readme.md文档不要打进去了

2.fastmode页面的addsample好像比autosample和是STATsample的addsample按钮要略微宽一些

----------

STAT界面的表格拉起来不是很丝滑，看看和Autosample的表格有啥区别。

-----------

增加数据库管理用户数据，根据system.txt看看我们适合哪种数据库，sqlite可以吗？

在三种样本运行模式中，均采用相同程序添加患者ID、选择参数和样本类型或预稀释。此外，还可以向样本添加其他患者数据，如姓氏、名字、生日、性别及备注。

三种模式右下角的button_insert按下后，检查选择的信息是否匹配，匹配的话就加到数据库里，然后在上一级的对应页面里添加进去。

---------------------

之前创建了一些测试数据，每个页面创建了100个用户数据，现在不需要了，我们就靠addsample来添加。一行就是一个addsample添加的项目信息，Patient，ID这栏，" last name ',' name ',' ID".反正就是显示到对应的列中，你先试试，继续吧。

----

下面进行Fast Mode页面剩余的设计。
fastmode的Timer列，是一个会变化显示的按钮或者类按钮框。
图片资源位置，记得整到资源文件：images\fast_mode\
当在fastmode添加一个用户测试的时候，对应的Timer列在阶段1显示 "Next Sample 'ss'",其中'ss'是倒计时秒数，每秒-1，减到0的时候，显示"Next Sample 00"，每个单词一行。
减到0后进入阶段2.阶段2的文字显示 "80ul vol."然后换行加上"added"，文字居中显示。
阶段1进到阶段2是自动的。
当阶段2时候按钮被按下，则会进入阶段3，
阶段3：现在剩余的孵育时间以mm：ss格式显示。当本试验的孵育时间快结束时，下一个按钮出现，进入阶段4按钮显示。无需按下，时间到00:00就会显示阶段4.
阶段4：当本阶段按钮被按下，会调用嵌入式模块的读数功能(目前没实现，用延时10s代替吧，但是要返回个读数的结果，最后可能用在result列)，读数完成，先校验进入本阶段4时间到按下阶段4按钮的时间差。然后后一个预定的允许温育误差阈值进行比较，例如误差阈值为01:00,一分钟，那么只要‘本阶段4时间到按下阶段4按钮的时间差’在1分钟内，阶段5就显示'--:--',如果超出了阈值时间，例如是2分11秒，则阶段5红色字显示'+02：11'。

文字都是居中显示。

0.阶段0，在上一个温育阶段没结束的时候，先不进入阶段1，不显示文字了.(一旦上一个测试的孵育时间开始,才进入阶段1)

1.阶段1"Next Sample 'ss'"对应的背景图为，白色字体，：next_sample.png
2.阶段2"80ul vol. added"对应的背景图为，白色字体，: add_80.png
3.阶段3"Incubation \n -mm:ss",黑色字体。incubation.png
4.阶段4"Read \n -ss",白色字体，ss显示的是进入阶段4过的秒数。read.png
5.阶段5，浅灰色背景，文字根据上面说的选择一种。


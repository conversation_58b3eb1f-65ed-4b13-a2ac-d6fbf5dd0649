#!/bin/bash

# ================================================================
# TRF SQLite Driver Final Solution for myd-y6ull14x14
# ================================================================
# Target Platform: Linux myd-y6ull14x14 4.1.15+ armv7l Qt 5.6.2
# Application: TRF ******** (Built with Qt 5.5.1)
# Issue: Qt version mismatch causing SQLite driver loading failure
# ================================================================

APP_DIR="/mnt/sd/TRF-********-Linux-ARM-v7l"

echo "TRF SQLite Driver Final Solution"
echo "Target: myd-y6ull14x14 Qt 5.6.2 runtime with Qt 5.5.1 app"
echo "========================================================"

if [ ! -d "$APP_DIR" ]; then
    echo "ERROR: Application directory not found: $APP_DIR"
    exit 1
fi

cd "$APP_DIR"

# Verify SQLite driver exists (should be packaged from GitHub Actions)
if [ ! -f "$APP_DIR/plugins/sqldrivers/libqsqlite.so" ]; then
    echo "ERROR: SQLite driver not found in plugins directory"
    echo "Expected: $APP_DIR/plugins/sqldrivers/libqsqlite.so"
    exit 1
fi

echo "SQLite driver found, setting up environment..."

# Fix permissions
chmod 755 "$APP_DIR/plugins/sqldrivers/libqsqlite.so"
chmod 755 "$APP_DIR/plugins/platforms/"*.so 2>/dev/null || true

# Create the working startup script
cat > "$APP_DIR/run_trf_final.sh" << 'EOF'
#!/bin/bash
# TRF Final Working Startup Script
# Resolves Qt 5.5.1 vs 5.6.2 compatibility issues

APP_DIR="/mnt/sd/TRF-********-Linux-ARM-v7l"
cd "$APP_DIR"

chmod +x ./trf

# Critical: Use ONLY app's own Qt 5.5.1 compatible plugins
export QT_PLUGIN_PATH="$APP_DIR/plugins"
export QT_QPA_PLATFORM_PLUGIN_PATH="$APP_DIR/plugins/platforms"

# Add SQLite driver to library path
export LD_LIBRARY_PATH="$APP_DIR/plugins/sqldrivers:$LD_LIBRARY_PATH"

# System configuration for myd-y6ull14x14
export LC_ALL=C
export LANG=C
export QT_QPA_PLATFORM="linuxfb"
export QT_QPA_GENERIC_PLUGINS="evdevtouch:/dev/input/event1"

echo "Starting TRF with Qt 5.5.1 compatible plugins..."
echo "Plugin path: $QT_PLUGIN_PATH"
echo "Expected result: SQLite driver should load successfully"

exec ./trf "$@"
EOF

chmod +x "$APP_DIR/run_trf_final.sh"

echo "Setup complete!"
echo ""
echo "To start TRF:"
echo "  cd $APP_DIR"
echo "  ./run_trf_final.sh"
echo ""
echo "Expected success indicators:"
echo "  - 'Available SQL Drivers: QSQLITE'"
echo "  - No 'QSQLITE driver not loaded' error"
echo "  - Database initialization success" 
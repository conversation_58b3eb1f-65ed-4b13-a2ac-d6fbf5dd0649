#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "MainScreen.h"
#include "basepage.h"
#include <QDebug>
#include "fastmodepage.h"
#include "statsamplepage.h"
#include "autosamplepage.h"
#include "addsamplepage.h" // Added for AddSamplePage
#include "qcmodulepage.h" // Added for QCModulePage

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    // Set window properties for framebuffer display
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);

    // 设置窗口状态
    setWindowState(Qt::WindowFullScreen);

    // 确保窗口尺寸和位置
    setFixedSize(1024, 600);
    setGeometry(0, 0, 1024, 600);

    // Create and set the stacked widget as the central widget
    stackedWidget = new QStackedWidget(this);
    setCentralWidget(stackedWidget);

    // Page 0: MainScreen
    mainScreen = new MainScreen(this);
    stackedWidget->addWidget(mainScreen);

    // Create the 6 mode pages using BasePage
    autoSamplePage = new BasePage(this);
    statSamplePage = new BasePage(this);
    fastModePage = new BasePage(this);
    qcModulePage = new BasePage(this);
    databasePage = new BasePage(this);
    settingsPage = new BasePage(this);

    // Add pages to the stacked widget (index 1-6)
    stackedWidget->addWidget(autoSamplePage);  // index 1
    stackedWidget->addWidget(statSamplePage);  // index 2
    stackedWidget->addWidget(fastModePage);    // index 3
    stackedWidget->addWidget(qcModulePage);    // index 4
    stackedWidget->addWidget(databasePage);    // index 5
    stackedWidget->addWidget(settingsPage);    // index 6

    // --- Inject content widgets into BasePages ---
    autoSamplePage->setContentWidget(new AutoSamplePage(this));
    statSamplePage->setContentWidget(new StatSamplePage(this));
    fastModePage->setContentWidget(new FastModePage(this));
    qcModulePage->setContentWidget(new QCModulePage(this));

    // Page 7: AddSamplePage (wrapped in BasePage for CommonHeader)
    addSampleBasePage = new BasePage(this);
    // AddSamplePage will be created dynamically with correct source type
    stackedWidget->addWidget(addSampleBasePage); // index 7

    setupConnections();

    // Start on the main screen
    stackedWidget->setCurrentIndex(0);
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupConnections()
{
    // Connect main screen buttons to navigate to pages (Qt 5.6 compatible)
    connect(mainScreen->getAutoSampleButton(), &QPushButton::clicked, this, &MainWindow::navigateToAutoSample);
    connect(mainScreen->getStatSampleButton(), &QPushButton::clicked, this, &MainWindow::navigateToStatSample);
    connect(mainScreen->getFastModeButton(), &QPushButton::clicked, this, &MainWindow::navigateToFastMode);
    connect(mainScreen->getQcModuleButton(), &QPushButton::clicked, this, &MainWindow::navigateToQcModule);
    connect(mainScreen->getDatabaseButton(), &QPushButton::clicked, this, &MainWindow::navigateToDatabase);
    connect(mainScreen->getSettingsButton(), &QPushButton::clicked, this, &MainWindow::navigateToSettings);

    // Connect header navigation to switch between pages (excluding MainScreen)
    BasePage* pages[] = {autoSamplePage, statSamplePage, fastModePage, qcModulePage, databasePage, settingsPage};
    for (int i = 0; i < 6; ++i) {
        connect(pages[i]->getHeader(), &CommonHeader::navigationClicked, this, &MainWindow::navigateToPage);
    }
    
    // Connect logo clicks to return to MainScreen
    for (int i = 0; i < 6; ++i) {
        connect(pages[i]->getHeader(), &CommonHeader::homeClicked, this, &MainWindow::returnToHome);
    }
    
    // Connect AutoSamplePage's add sample button to show AddSamplePage
    AutoSamplePage *autoSampleContent = qobject_cast<AutoSamplePage*>(autoSamplePage->getContentWidget());
    if (autoSampleContent) {
        connect(autoSampleContent, &AutoSamplePage::addSampleClicked, this, &MainWindow::navigateToAddSamplePage);
    }
    
    // Connect StatSamplePage's add sample button (Qt 5.6 compatible)
    StatSamplePage *statSampleContent = qobject_cast<StatSamplePage*>(statSamplePage->getContentWidget());
    if (statSampleContent) {
        connect(statSampleContent, &StatSamplePage::addSampleClicked, this, &MainWindow::navigateToStatSampleAdd);
    }
    
    // Connect FastModePage's add sample button (Qt 5.6 compatible)
    FastModePage *fastModeContent = qobject_cast<FastModePage*>(fastModePage->getContentWidget());
    if (fastModeContent) {
        connect(fastModeContent, &FastModePage::addSampleClicked, this, &MainWindow::navigateToFastModeAdd);
    }
    
    // Connect AddSamplePage header navigation
    connect(addSampleBasePage->getHeader(), &CommonHeader::navigationClicked, this, &MainWindow::navigateToPage);
    
    // Connect AddSamplePage logo click to return to MainScreen  
    connect(addSampleBasePage->getHeader(), &CommonHeader::homeClicked, this, &MainWindow::returnToHome);
    
    // Connect QCModulePage's data cleared signal to refresh all sample pages
    QCModulePage *qcContent = qobject_cast<QCModulePage*>(qcModulePage->getContentWidget());
    if (qcContent) {
        connect(qcContent, &QCModulePage::dataCleared, this, &MainWindow::onDataCleared);
    }
}

void MainWindow::navigateToAddSamplePage()
{
    navigateToAddSamplePageWithType(SourcePageType::AUTO_SAMPLE);
}

void MainWindow::navigateToAddSamplePageWithType(SourcePageType sourceType)
{
    // Record source page type
    currentAddSampleSourceType = sourceType;
    
    // Create new AddSamplePage with correct source type
    AddSamplePage* addSamplePage = new AddSamplePage(this, sourceType);
    addSampleBasePage->setContentWidget(addSamplePage);
    
    // Connect Exit signal
    connect(addSamplePage, &AddSamplePage::exitRequested, this, &MainWindow::onAddSamplePageExitRequested);
    
    // Connect data addition signal
    connect(addSamplePage, &AddSamplePage::dataAddedToDatabase, this, &MainWindow::onDataAddedToDatabase);
    
    // Set appropriate active button based on source type
    int activeButtonIndex = 0; // Default to Auto sample
    switch (sourceType) {
        case SourcePageType::AUTO_SAMPLE:
            activeButtonIndex = 0; // Auto sample button
            break;
        case SourcePageType::STAT_SAMPLE:
            activeButtonIndex = 1; // STAT sample button  
            break;
        case SourcePageType::FAST_MODE:
            activeButtonIndex = 2; // Fast mode button
            break;
    }
    
    addSampleBasePage->getHeader()->setActiveButton(activeButtonIndex);
    
    // Disable AddSamplePage navigation functionality
    addSampleBasePage->getHeader()->setNavigationEnabled(false);
    
    stackedWidget->setCurrentIndex(7);
}

void MainWindow::navigateToPage(int index)
{
    if (index >= 0 && index < stackedWidget->count()) {
        // Allow navigation to pages 1-7 (mode pages and AddSamplePage)
        if (index < 1 || index > 7) {
            qWarning() << "Invalid page index:" << index;
            return;
        }

        // Removed original AddSamplePage special check, now handled by CommonHeader disable functionality

        // Set the active button in the header of the destination page
        BasePage* page = qobject_cast<BasePage*>(stackedWidget->widget(index));
        if (page) {
            if (index <= 6) {
                // For regular mode pages (1-6): button index = page index - 1
                page->getHeader()->setActiveButton(index - 1);
            } else if (index == 7) {
                // For AddSamplePage: activate Auto sample button (index 0)
                page->getHeader()->setActiveButton(0);
            }
            
            // Ensure navigation is enabled when navigating to normal pages
            if (index <= 6) {
                page->getHeader()->setNavigationEnabled(true);
            }
        }
        
        // Switch to the page
        stackedWidget->setCurrentIndex(index);
    }
}

void MainWindow::returnToHome()
{
    stackedWidget->setCurrentIndex(0);
}

void MainWindow::onAddSamplePageExitRequested()
{
    // Re-enable navigation when exiting AddSamplePage
    if (addSampleBasePage && addSampleBasePage->getHeader()) {
        addSampleBasePage->getHeader()->setNavigationEnabled(true);
    }
    
    // Return to corresponding page based on source page type
    int targetPageIndex = 1; // Default to Auto sample page
    
    switch (currentAddSampleSourceType) {
        case SourcePageType::AUTO_SAMPLE:
            targetPageIndex = 1; // Auto sample page
            break;
        case SourcePageType::STAT_SAMPLE:
            targetPageIndex = 2; // STAT sample page
            break;
        case SourcePageType::FAST_MODE:
            targetPageIndex = 3; // Fast mode page
            break;
    }
    
    navigateToPage(targetPageIndex);
}

void MainWindow::onDataAddedToDatabase(SourcePageType sourceType, const QString& patientName, const QString& patientId, 
                                     const QString& parameter, const QString& sampleType, const QString& result,
                                     const QString& datetime, const QString& lot, const QString& cutoff)
{
    Q_UNUSED(sampleType)  // Suppress unused parameter warning
    qDebug() << "MainWindow: Received data for" << static_cast<int>(sourceType) 
             << "Patient:" << patientName << "Parameter:" << parameter;
    
    // Add data to corresponding page based on sourceType
    switch (sourceType) {
        case SourcePageType::AUTO_SAMPLE: {
            AutoSamplePage *autoContent = qobject_cast<AutoSamplePage*>(autoSamplePage->getContentWidget());
            if (autoContent) {
                // Use addNewSample to automatically start Timer test flow
                autoContent->addNewSample(patientId, parameter, result, datetime, "--:--", lot, cutoff);
                qDebug() << "Added new sample to AutoSamplePage with Timer flow";
            }
            break;
        }
        case SourcePageType::STAT_SAMPLE: {
            StatSamplePage *statContent = qobject_cast<StatSamplePage*>(statSamplePage->getContentWidget());
            if (statContent) {
                // Add row directly, addSampleRow will handle row number assignment
                statContent->addSampleRow(patientId, parameter, result, datetime, "--:--", lot, cutoff);
                qDebug() << "Added row to StatSamplePage";
            }
            break;
        }
        case SourcePageType::FAST_MODE: {
            FastModePage *fastContent = qobject_cast<FastModePage*>(fastModePage->getContentWidget());
            if (fastContent) {
                // Use addNewSample to automatically select appropriate row number
                fastContent->addNewSample(patientId, parameter, result, datetime, "--:--", lot, cutoff);
                qDebug() << "Added new sample to FastModePage";
            }
            break;
        }
    }
}

void MainWindow::onDataCleared()
{
    qDebug() << "MainWindow: Received data clear signal, starting to refresh all sample pages";
    
    // Refresh AutoSamplePage
    AutoSamplePage *autoContent = qobject_cast<AutoSamplePage*>(autoSamplePage->getContentWidget());
    if (autoContent) {
        autoContent->refreshAfterDataClear();
    }
    
    // Refresh StatSamplePage
    StatSamplePage *statContent = qobject_cast<StatSamplePage*>(statSamplePage->getContentWidget());
    if (statContent) {
        statContent->refreshAfterDataClear();
    }
    
    // Refresh FastModePage
    FastModePage *fastContent = qobject_cast<FastModePage*>(fastModePage->getContentWidget());
    if (fastContent) {
        fastContent->refreshAfterDataClear();
    }
    
    qDebug() << "MainWindow: All sample pages refresh completed";
}

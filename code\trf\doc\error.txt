root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# ./run.sh 
TRF ARM鍚姩鑴氭湰 - <PERSON><PERSON> Jan 18 05:34:56 UTC 2022
=========================
鍚姩 TRF (ARMv7l)...
"????? TRF ????..."
qt.qpa.input: evdevkeyboard: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x8)
qt.qpa.input: Found matching devices ("/dev/input/event2", "/dev/input/event0")
qt.qpa.input: Adding keyboard at "/dev/input/event2"
qt.qpa.input: Try to create keyboard handler for "/dev/input/event2" ""
qt.qpa.input: Opening keyboard at "/dev/input/event2"
qt.qpa.input: Create keyboard handler with for device "/dev/input/event2"
qt.qpa.input: Unload current keymap and restore built-in
qt.qpa.input: numlock=0 , capslock=0, scrolllock=0
qt.qpa.input: Adding keyboard at "/dev/input/event0"
qt.qpa.input: Try to create keyboard handler for "/dev/input/event0" ""
qt.qpa.input: Opening keyboard at "/dev/input/event0"
qt.qpa.input: Create keyboard handler with for device "/dev/input/event0"
qt.qpa.input: Unload current keymap and restore built-in
qt.qpa.input: numlock=0 , capslock=0, scrolllock=0
qt.qpa.input: evdevmouse: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x1|0x2)
qt.qpa.input: Found matching devices ()
qt.qpa.input: evdevtouch: Using device discovery
qt.qpa.input: udev device discovery for type QFlags(0x2|0x4)
qt.qpa.input: Found matching devices ("/dev/input/event1")
qt.qpa.input: evdevtouch: Adding device at "/dev/input/event1"
qt.qpa.input: evdevtouch: Using device /dev/input/event1
qt.qpa.input: evdevtouch: /dev/input/event1: Protocol type A  (single)
qt.qpa.input: evdevtouch: /dev/input/event1: min X: 0 max X: 4095
qt.qpa.input: evdevtouch: /dev/input/event1: min Y: 0 max Y: 4095
qt.qpa.input: evdevtouch: /dev/input/event1: min pressure: 0 max pressure: 0
qt.qpa.input: evdevtouch: /dev/input/event1: device name: iMX6UL TouchScreen Controller
No such plugin for spec  "tslib:/dev/input/event0"
"QApplication ????"
"=== ?????? ==="
"Qt ??: 5.12.8"
"Qt ?????: 5.6.2"
"??????: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l"
"???????: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l/trf"
"Qt ??????:"
"  - /usr/lib/qt5/plugins (??: ?)"
"  - /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l (??: ?)"
"???SQL??:"
"platforms??: /mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l/platforms (??: ?)"
"?????:"
"  - Windows"
"  - Fusion"
"=== ?????? ==="
"??????: ??"
"????????: global_colors.qss"
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
QFontDatabase: Cannot find font directory /usr/share/fonts - is Qt installed correctly?
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(PaletteChange, 0x7ee449ec)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(AcceptDropsChange, 0x7ee4471c)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(CursorChange, 0x7ee449f4)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(Polish, 0x7ee44934)
qt.qpa.input: evdevtouch: Updating QInputDeviceManager device count: 1  touch devices, 0 pending handler(s)
qt.widgets.gestures: QGestureManager:Recognizer: ignored the event:  QPanGesture(state=NoGesture,lastOffset=0,0QPointF(0,0),offset=0,0,acceleration=0,delta=0,0) QEvent(PolishRequest, 0x54d87960)
---------------------
root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# ./check.sh 
=== TRF 绯荤粺鍏煎鎬ф鏌===
棰勬湡鐩爣: Linux myd-y6ull14x14 4.1.15+ armv7l
褰撳墠绯荤粺: Linux myd-y6ull14x14 4.1.15+ #4 SMP PREEMPT Wed Jul 2 20:20:33 CST 2025 armv7l armv7l armv7l GNU/Linux

鉁鏋舵瀯鍏煎: armv7l

妫€鏌t5杩愯鏃跺簱:
鉁libQt5Core.so.5
鉁libQt5Gui.so.5
鉁libQt5Widgets.so.5
鉁libQt5Sql.so.5

妫€鏌QLite椹卞姩:
鉁Qt5 SQL搴撳瓨鍦紝SQLite鏀寔鍙敤

妫€鏌ュ浘褰㈢郴缁熸敮鎸
                        鉁甯х紦鍐茶澶/dev/fb0 瀛樺湪

妫€鏌ヨЕ鎽歌緭鍏ユ敮鎸
                       鉁瑙︽懜杈撳叆璁惧 /dev/input/event0 瀛樺湪
鉁tslib瑙︽懜搴撳瓨鍦

妫€鏌penGL鏀寔:
鉁EGL搴撳瓨鍦
鉁OpenGL ES 2.0搴撳瓨鍦

鉁绯荤粺鍏煎锛屽彲浠ヨ繍琛./run.sh
root@myd-y6ull14x14:/mnt/sd/TRF-1.0.0.9-Linux-ARM-v7l# 